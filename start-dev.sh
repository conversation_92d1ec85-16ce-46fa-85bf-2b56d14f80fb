#!/bin/bash

# AI Crypto Chart Analysis - Development Startup Script
echo "🚀 Starting AI Crypto Chart Analysis Application (Development Mode)..."

# Function to check if a command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if a port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command_exists python3; then
    echo "❌ Python 3 is not installed. Please install Python 3.9+ first."
    exit 1
fi

if ! command_exists node; then
    echo "❌ Node.js is not installed. Please install Node.js 18+ first."
    exit 1
fi

if ! command_exists npm; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

echo "✅ Prerequisites check passed!"

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p backend/uploads
mkdir -p backend/logs

# Check if ports are available
if port_in_use 8000; then
    echo "⚠️  Port 8000 is already in use. Please stop the service using it or use a different port."
    echo "   You can check what's using it with: lsof -i :8000"
    exit 1
fi

if port_in_use 3000; then
    echo "⚠️  Port 3000 is already in use. Please stop the service using it or use a different port."
    echo "   You can check what's using it with: lsof -i :3000"
    exit 1
fi

# Setup backend
echo "🐍 Setting up Enhanced Backend..."
cd backend

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "📦 Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
echo "🔧 Activating virtual environment..."
source venv/bin/activate

# Install backend dependencies
echo "📥 Installing backend dependencies..."
pip install -r requirements.txt

# Start backend in background
echo "🚀 Starting Enhanced Backend (enhanced_main.py)..."
uvicorn enhanced_main:app --reload --host 0.0.0.0 --port 8000 &
BACKEND_PID=$!

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 5

# Check if backend is running
if ! port_in_use 8000; then
    echo "❌ Failed to start backend. Check the logs above."
    exit 1
fi

echo "✅ Enhanced Backend started successfully!"

# Setup frontend
echo "⚛️  Setting up Frontend..."
cd ../frontend

# Install frontend dependencies
if [ ! -d "node_modules" ]; then
    echo "📥 Installing frontend dependencies..."
    npm install
fi

# Start frontend in background
echo "🚀 Starting Frontend..."
npm run dev &
FRONTEND_PID=$!

# Wait for frontend to start
echo "⏳ Waiting for frontend to start..."
sleep 10

# Check if frontend is running
if ! port_in_use 3000; then
    echo "❌ Failed to start frontend. Check the logs above."
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

echo "✅ Frontend started successfully!"

# Display success message
echo ""
echo "🎉 AI Chart Analysis Application is now running!"
echo ""
echo "🌐 Application URLs:"
echo "   📱 Frontend: http://localhost:3000"
echo "   🔧 Backend API: http://localhost:8000"
echo "   📚 API Documentation: http://localhost:8000/docs"
echo "   🧪 Enhanced Analysis Endpoint: http://localhost:8000/api/v1/analyze-chart-enhanced"
echo ""
echo "🔧 Backend Features:"
echo "   ✅ Enhanced AI Vision Processing (OpenAI GPT-4o)"
echo "   ✅ Multi-Factor Analysis Scoring"
echo "   ✅ Detailed AI Reasoning Engine"
echo "   ✅ Historical Pattern Matching"
echo "   ✅ Real-time Market Context"
echo "   ✅ Professional Risk Assessment"
echo ""
echo "📱 Frontend Features:"
echo "   ✅ Drag & Drop Chart Upload"
echo "   ✅ Real-time Progress Updates"
echo "   ✅ Multi-Factor Score Visualization"
echo "   ✅ Detailed AI Reasoning Display"
echo "   ✅ Professional Results Interface"
echo "   ✅ Mobile-Responsive Design"
echo ""
echo "🛑 To stop the application:"
echo "   Press Ctrl+C or run: kill $BACKEND_PID $FRONTEND_PID"
echo ""
echo "📝 To view logs:"
echo "   Backend logs: Check the terminal output above"
echo "   Frontend logs: Check the terminal output above"
echo ""
echo "🚀 Ready to analyze crypto charts with AI!"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ Services stopped. Goodbye!"
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Keep script running
echo "💡 Press Ctrl+C to stop all services..."
wait
