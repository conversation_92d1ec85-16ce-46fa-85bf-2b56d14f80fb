
# AI Crypto Chart Analysis

An AI-powered cryptocurrency chart analysis application that analyzes chart screenshots and provides future position recommendations based on multiple technical factors.

## Features
- 📊 Upload crypto chart screenshots for analysis
- 🤖 AI-powered technical analysis using computer vision
- 📈 Multiple factor analysis (RSI, MACD, patterns, volume, etc.)
- 🎯 Future position recommendations (Buy/Sell/Hold)
- 📱 Modern web interface with real-time results
- 🔍 Pattern recognition and trend analysis

## Architecture
- **Frontend**: Next.js React application
- **Backend**: Python FastAPI with AI/ML capabilities
- **AI Engine**: Computer vision + Technical analysis + ML predictions
- **Database**: Analysis history and model training data

## Project Structure
```
├── frontend/          # Next.js React application
├── backend/           # Python FastAPI backend
├── ml_models/         # AI/ML models and algorithms
├── docker-compose.yml # Container orchestration
└── README.md         # This file
```

## Getting Started

### Prerequisites
- Node.js 18+ and npm/yarn
- Python 3.9+
- Docker (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd Chart\ Analysis
   ```

2. **Setup Frontend**
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

3. **Setup Backend**
   ```bash
   cd backend
   pip install -r requirements.txt
   uvicorn main:app --reload
   ```

4. **Using Docker (Alternative)**
   ```bash
   docker-compose up --build
   ```

## Usage
1. Open the web application
2. Upload a crypto chart screenshot
3. Specify additional parameters (timeframe, coin, etc.)
4. Get AI-powered analysis and position recommendations

## Analysis Factors
- **Technical Indicators**: RSI, MACD, Moving Averages, Bollinger Bands
- **Chart Patterns**: Head & Shoulders, Triangles, Support/Resistance
- **Candlestick Patterns**: Doji, Hammer, Engulfing patterns
- **Volume Analysis**: Volume trends and anomalies
- **Market Sentiment**: Overall crypto market context
- **Risk Assessment**: Stop-loss and take-profit levels

## Roadmap
- [x] Project structure setup
- [ ] Image upload functionality
- [ ] Computer vision chart reader
- [ ] Technical analysis engine
- [ ] AI prediction models
- [ ] Results visualization
- [ ] Historical analysis tracking
- [ ] Real-time market data integration

## License
This project is licensed under the MIT License.

## Running the Project

To run the project, use the following command:

```bash
./start.sh
```
