"""
Chart Analysis Router
Handles chart image upload and analysis endpoints
"""

from fastapi import APIRouter, UploadFile, File, HTTPException, Form
from fastapi.responses import <PERSON><PERSON><PERSON>esponse
from typing import Optional
import aiofiles
import uuid
from pathlib import Path
import logging

# Import services
try:
    from services.image_processor import ImageProcessor
    from services.technical_analysis import Technical<PERSON>nalyzer
    from services.ai_predictor import AIPredictor
    from models.chart_data import ChartAnalysisRequest, ChartAnalysisResponse

    # Initialize services
    image_processor = ImageProcessor()
    technical_analyzer = TechnicalAnalyzer()
    ai_predictor = AIPredictor()
    services_available = True
except ImportError as e:
    logger.warning(f"Some services not available: {e}")
    services_available = False

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

router = APIRouter()

# Services are initialized above in the try block

@router.post("/analyze-chart", response_model=ChartAnalysisResponse)
async def analyze_chart(
    file: UploadFile = File(...),
    coin_symbol: Optional[str] = Form(None),
    timeframe: Optional[str] = Form("1h"),
    chart_type: Optional[str] = Form("candlestick"),
    additional_notes: Optional[str] = Form(None)
):
    """
    Analyze uploaded chart image and provide AI-powered insights
    """
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        file_extension = Path(file.filename).suffix
        filename = f"{file_id}{file_extension}"
        file_path = Path("uploads") / filename
        
        # Save uploaded file
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        logger.info(f"Uploaded file saved: {file_path}")
        
        # Create analysis request
        request = ChartAnalysisRequest(
            file_path=str(file_path),
            coin_symbol=coin_symbol,
            timeframe=timeframe,
            chart_type=chart_type,
            additional_notes=additional_notes
        )
        
        # Process image and extract chart data
        chart_data = await image_processor.process_chart_image(request)
        
        # Perform technical analysis
        technical_indicators = await technical_analyzer.analyze(chart_data)
        
        # Generate AI predictions
        predictions = await ai_predictor.predict(chart_data, technical_indicators)
        
        # Create response
        response = ChartAnalysisResponse(
            analysis_id=file_id,
            chart_data=chart_data,
            technical_indicators=technical_indicators,
            predictions=predictions,
            file_url=f"/uploads/{filename}"
        )
        
        logger.info(f"Analysis completed for file: {filename}")
        return response
        
    except Exception as e:
        logger.error(f"Error analyzing chart: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

@router.get("/analysis/{analysis_id}")
async def get_analysis(analysis_id: str):
    """
    Retrieve previous analysis by ID
    """
    # TODO: Implement database storage and retrieval
    return {"message": f"Analysis {analysis_id} - Database integration pending"}

@router.get("/supported-coins")
async def get_supported_coins():
    """
    Get list of supported cryptocurrency symbols
    """
    supported_coins = [
        "BTC", "ETH", "ADA", "SOL", "DOT", "LINK", "UNI", "AAVE",
        "MATIC", "AVAX", "ATOM", "XRP", "LTC", "BCH", "ETC"
    ]
    return {"supported_coins": supported_coins}

@router.get("/timeframes")
async def get_timeframes():
    """
    Get list of supported timeframes
    """
    timeframes = [
        {"value": "1m", "label": "1 Minute"},
        {"value": "5m", "label": "5 Minutes"},
        {"value": "15m", "label": "15 Minutes"},
        {"value": "1h", "label": "1 Hour"},
        {"value": "4h", "label": "4 Hours"},
        {"value": "1d", "label": "1 Day"},
        {"value": "1w", "label": "1 Week"}
    ]
    return {"timeframes": timeframes}
