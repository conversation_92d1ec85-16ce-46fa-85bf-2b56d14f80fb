"""
Enhanced FastAPI backend with real AI analysis
"""

from fastapi import <PERSON><PERSON><PERSON>, UploadFile, File, HTTPException, Form
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from typing import Optional
import os
import uuid
import aiofiles
from pathlib import Path
import logging
import time
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import our enhanced services
try:
    from services.image_processor import ImageProcessor
    from services.technical_analysis import TechnicalAnalyzer
    from services.ai_predictor import AIPredictor
    from services.ai_vision_processor import AIVisionProcessor
    from models.chart_data import ChartAnalysisRequest, ChartAnalysisResponse
    services_available = True
except ImportError as e:
    print(f"Warning: Some services not available: {e}")
    services_available = False

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Create FastAPI app
app = FastAPI(
    title="AI Crypto Chart Analysis API - Enhanced",
    description="Enhanced AI-powered cryptocurrency chart analysis with real computer vision",
    version="2.0.0",
    docs_url="/docs",
    redoc_url="/redoc"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Create uploads directory
uploads_dir = Path("uploads")
uploads_dir.mkdir(exist_ok=True)

# Initialize services if available
if services_available:
    image_processor = ImageProcessor()
    technical_analyzer = TechnicalAnalyzer()
    ai_predictor = AIPredictor()
    ai_vision_processor = AIVisionProcessor()
    logger.info("All AI services initialized successfully")
else:
    logger.warning("AI services not available, using fallback mode")

async def _convert_ai_vision_to_chart_data(ai_data, request):
    """Convert AI vision data to expected ChartAnalysisResponse format"""
    from models.chart_data import ChartAnalysisResponse, PriceData, DetectedPatterns

    # Convert price data
    extracted_prices = []
    for price_point in ai_data.get("price_data", []):
        extracted_prices.append(PriceData(
            open=price_point.get("open", 0),
            high=price_point.get("high", 0),
            low=price_point.get("low", 0),
            close=price_point.get("close", 0),
            volume=price_point.get("volume", 0)
        ))

    # Convert patterns
    detected_patterns = DetectedPatterns(
        candlestick_patterns=ai_data.get("candlestick_patterns", []),
        chart_patterns=ai_data.get("chart_patterns", []),
        trend_direction=ai_data.get("trend_direction", "sideways"),
        trend_strength=ai_data.get("trend_strength", 0.5)
    )

    return ChartAnalysisResponse(
        extracted_prices=extracted_prices,
        detected_patterns=detected_patterns,
        processing_status="ai_vision_success"
    )

@app.get("/")
async def root():
    """Root endpoint with API information"""
    return {
        "message": "Enhanced AI Crypto Chart Analysis API",
        "version": "2.0.0",
        "docs": "/docs",
        "status": "running",
        "ai_services": services_available
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy", 
        "service": "enhanced-chart-analysis-api",
        "ai_services": services_available
    }

@app.post("/api/v1/analyze-chart")
async def analyze_chart(
    file: UploadFile = File(...),
    coin_symbol: Optional[str] = Form("BTC"),
    timeframe: Optional[str] = Form("1h"),
    chart_type: Optional[str] = Form("candlestick"),
    additional_notes: Optional[str] = Form(None)
):
    """
    Analyze uploaded chart image with enhanced AI processing
    """
    start_time = time.time()
    
    try:
        # Validate file type
        if not file.content_type.startswith('image/'):
            raise HTTPException(status_code=400, detail="File must be an image")
        
        # Generate unique filename
        file_id = str(uuid.uuid4())
        file_extension = Path(file.filename).suffix
        filename = f"{file_id}{file_extension}"
        file_path = uploads_dir / filename
        
        # Save uploaded file
        async with aiofiles.open(file_path, 'wb') as f:
            content = await file.read()
            await f.write(content)
        
        logger.info(f"Processing chart analysis for file: {filename}")
        
        if services_available:
            # Create analysis request
            request = ChartAnalysisRequest(
                file_path=str(file_path),
                coin_symbol=coin_symbol,
                timeframe=timeframe,
                chart_type=chart_type,
                additional_notes=additional_notes
            )
            
            # Try AI vision first, fallback to computer vision
            logger.info("Analyzing chart with AI vision...")
            ai_data = await ai_vision_processor.analyze_chart(str(file_path), coin_symbol, timeframe)

            if ai_data.get("status") == "success" and len(ai_data.get("price_data", [])) > 0:
                logger.info(f"✅ AI Vision successful: {len(ai_data['price_data'])} price points extracted")
                # Convert AI vision data to expected format
                chart_data = await _convert_ai_vision_to_chart_data(ai_data, request)
            else:
                logger.info("AI Vision failed, falling back to computer vision...")
                chart_data = await image_processor.process_chart_image(request)
            
            # Perform technical analysis
            logger.info("Performing technical analysis...")
            technical_indicators = await technical_analyzer.analyze(chart_data)
            
            # Generate AI predictions
            logger.info("Generating AI predictions...")
            predictions = await ai_predictor.predict(chart_data, technical_indicators)
            
            # Calculate processing time
            processing_time = round(time.time() - start_time, 2)
            
            # Helper function to convert numpy types to Python types
            def convert_numpy_types(obj):
                if hasattr(obj, 'item'):  # numpy scalar
                    return obj.item()
                elif isinstance(obj, (list, tuple)):
                    return [convert_numpy_types(item) for item in obj]
                elif isinstance(obj, dict):
                    return {key: convert_numpy_types(value) for key, value in obj.items()}
                else:
                    return obj

            # Create response with type conversion
            response = {
                "analysis_id": file_id,
                "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "chart_data": {
                    "extracted_prices": [
                        {
                            "open": float(p.open) if p.open else None,
                            "high": float(p.high) if p.high else None,
                            "low": float(p.low) if p.low else None,
                            "close": float(p.close) if p.close else None,
                            "volume": int(p.volume) if p.volume else None
                        } for p in chart_data.extracted_prices
                    ],
                    "detected_patterns": {
                        "candlestick_patterns": chart_data.detected_patterns.candlestick_patterns or [],
                        "chart_patterns": chart_data.detected_patterns.chart_patterns or [],
                        "trend_direction": chart_data.detected_patterns.trend_direction,
                        "trend_strength": float(chart_data.detected_patterns.trend_strength) if chart_data.detected_patterns.trend_strength else None
                    },
                    "processing_status": chart_data.processing_status
                },
                "technical_indicators": {
                    "rsi": float(technical_indicators.rsi) if technical_indicators.rsi else None,
                    "macd": convert_numpy_types(technical_indicators.macd) if technical_indicators.macd else None,
                    "moving_averages": convert_numpy_types(technical_indicators.moving_averages) if technical_indicators.moving_averages else None,
                    "bollinger_bands": convert_numpy_types(technical_indicators.bollinger_bands) if technical_indicators.bollinger_bands else None,
                    "volume_analysis": convert_numpy_types(technical_indicators.volume_analysis) if technical_indicators.volume_analysis else None,
                    "support_resistance": convert_numpy_types(technical_indicators.support_resistance) if technical_indicators.support_resistance else None
                },
                "predictions": {
                    "recommendation": predictions.recommendation.value if hasattr(predictions.recommendation, 'value') else str(predictions.recommendation),
                    "confidence": float(predictions.confidence),
                    "target_price": float(predictions.target_price) if predictions.target_price else None,
                    "time_horizon": predictions.time_horizon,
                    "reasoning": predictions.reasoning,
                    "risk_assessment": {
                        "risk_level": predictions.risk_assessment.risk_level,
                        "stop_loss": float(predictions.risk_assessment.stop_loss) if predictions.risk_assessment.stop_loss else None,
                        "take_profit": float(predictions.risk_assessment.take_profit) if predictions.risk_assessment.take_profit else None,
                        "risk_reward_ratio": float(predictions.risk_assessment.risk_reward_ratio) if predictions.risk_assessment.risk_reward_ratio else None
                    }
                },
                "file_url": f"/uploads/{filename}",
                "processing_time": processing_time,
                "analysis_type": "enhanced_ai"
            }
            
            logger.info(f"Analysis completed in {processing_time}s")
            return response
            
        else:
            # Fallback to mock data if services not available
            logger.warning("Using fallback analysis due to missing services")
            return await _generate_fallback_analysis(file_id, filename, start_time)
        
    except Exception as e:
        logger.error(f"Error analyzing chart: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Analysis failed: {str(e)}")

async def _generate_fallback_analysis(file_id: str, filename: str, start_time: float):
    """Generate fallback analysis when AI services are not available"""
    processing_time = round(time.time() - start_time, 2)
    
    return {
        "analysis_id": file_id,
        "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
        "chart_data": {
            "extracted_prices": [
                {"open": 50000, "high": 51000, "low": 49500, "close": 50500, "volume": 1000},
                {"open": 50500, "high": 52000, "low": 50000, "close": 51500, "volume": 1200},
                {"open": 51500, "high": 51800, "low": 50800, "close": 51200, "volume": 900},
            ],
            "detected_patterns": {
                "candlestick_patterns": ["doji"],
                "chart_patterns": ["ascending_triangle"],
                "trend_direction": "bullish",
                "trend_strength": 0.7
            },
            "processing_status": "fallback_mode"
        },
        "technical_indicators": {
            "rsi": 65.5,
            "macd": {"macd": 0.0012, "signal": 0.0008, "histogram": 0.0004},
            "moving_averages": {"ma_20": 50800, "ma_50": 50200},
            "bollinger_bands": {"upper": 52000, "middle": 51000, "lower": 50000}
        },
        "predictions": {
            "recommendation": "buy",
            "confidence": 0.65,
            "target_price": 52500,
            "time_horizon": "24h",
            "reasoning": "Fallback analysis: Basic bullish pattern detected. Please ensure all AI services are properly installed for enhanced analysis.",
            "risk_assessment": {
                "risk_level": "medium",
                "stop_loss": 49000,
                "take_profit": 52500,
                "risk_reward_ratio": 2.3
            }
        },
        "file_url": f"/uploads/{filename}",
        "processing_time": processing_time,
        "analysis_type": "fallback"
    }

@app.get("/api/v1/supported-coins")
async def get_supported_coins():
    """Get list of supported cryptocurrency symbols"""
    supported_coins = [
        "BTC", "ETH", "ADA", "SOL", "DOT", "LINK", "UNI", "AAVE",
        "MATIC", "AVAX", "ATOM", "XRP", "LTC", "BCH", "ETC", "BNB",
        "DOGE", "SHIB", "ALGO", "VET", "FTM", "NEAR", "SAND", "MANA"
    ]
    return {"supported_coins": supported_coins}

@app.get("/api/v1/timeframes")
async def get_timeframes():
    """Get list of supported timeframes"""
    timeframes = [
        {"value": "1m", "label": "1 Minute"},
        {"value": "5m", "label": "5 Minutes"},
        {"value": "15m", "label": "15 Minutes"},
        {"value": "30m", "label": "30 Minutes"},
        {"value": "1h", "label": "1 Hour"},
        {"value": "2h", "label": "2 Hours"},
        {"value": "4h", "label": "4 Hours"},
        {"value": "6h", "label": "6 Hours"},
        {"value": "12h", "label": "12 Hours"},
        {"value": "1d", "label": "1 Day"},
        {"value": "3d", "label": "3 Days"},
        {"value": "1w", "label": "1 Week"},
        {"value": "1M", "label": "1 Month"}
    ]
    return {"timeframes": timeframes}

@app.get("/api/v1/status")
async def get_status():
    """Get detailed system status"""
    return {
        "api_version": "2.0.0",
        "services": {
            "image_processor": services_available,
            "technical_analyzer": services_available,
            "ai_predictor": services_available
        },
        "features": {
            "computer_vision": services_available,
            "pattern_recognition": services_available,
            "technical_analysis": services_available,
            "ai_predictions": services_available
        }
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
