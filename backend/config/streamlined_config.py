"""
Configuration for Streamlined AI Chart Analysis Architecture
OpenAI-focused design with enhanced context and robust error handling
"""

import os
from typing import Dict, Any

class StreamlinedConfig:
    """Configuration class for the streamlined architecture"""
    
    # AI Vision Configuration
    AI_VISION = {
        "primary_model": "gpt-4o",
        "max_retries": 2,
        "timeout_seconds": 30,
        "max_tokens": 4000,
        "image_max_size": (1024, 1024),
        "confidence_threshold": 0.7
    }
    
    # Data Collection Configuration
    DATA_COLLECTION = {
        "binance_rate_limit": 1200,  # requests per minute
        "historical_lookback_days": 365,
        "pattern_similarity_threshold": 0.7,
        "cache_ttl_seconds": 300  # 5 minutes
    }
    
    # Multi-Factor Analysis Weights
    ANALYSIS_WEIGHTS = {
        "bull_market": {
            "technical": 0.70,
            "historical": 0.20,
            "fundamental": 0.15,
            "sentiment": 0.10
        },
        "bear_market": {
            "technical": 0.50,
            "historical": 0.25,
            "fundamental": 0.30,
            "sentiment": 0.15
        },
        "sideways_market": {
            "technical": 0.60,
            "historical": 0.25,
            "fundamental": 0.20,
            "sentiment": 0.15
        }
    }
    
    # Error Handling Configuration
    ERROR_HANDLING = {
        "max_retry_attempts": 3,
        "retry_delay_seconds": 1,
        "fallback_confidence": 0.5,
        "emergency_cache_ttl": 3600  # 1 hour
    }
    
    # Performance Monitoring
    PERFORMANCE = {
        "target_accuracy": 0.95,
        "max_processing_time": 15,  # seconds
        "min_confidence_score": 0.8,
        "alert_threshold": 0.7
    }
    
    @classmethod
    def get_openai_config(cls) -> Dict[str, Any]:
        """Get OpenAI-specific configuration"""
        return {
            "api_key": os.getenv("OPENAI_API_KEY"),
            "model": cls.AI_VISION["primary_model"],
            "max_tokens": cls.AI_VISION["max_tokens"],
            "timeout": cls.AI_VISION["timeout_seconds"]
        }
    
    @classmethod
    def get_binance_config(cls) -> Dict[str, Any]:
        """Get Binance API configuration"""
        return {
            "base_url": "https://api.binance.com",
            "rate_limit": cls.DATA_COLLECTION["binance_rate_limit"],
            "timeout": 30
        }
    
    @classmethod
    def get_analysis_weights(cls, market_phase: str) -> Dict[str, float]:
        """Get analysis weights based on market phase"""
        return cls.ANALYSIS_WEIGHTS.get(
            f"{market_phase}_market", 
            cls.ANALYSIS_WEIGHTS["sideways_market"]
        )
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate configuration settings"""
        required_env_vars = ["OPENAI_API_KEY"]
        
        for var in required_env_vars:
            if not os.getenv(var):
                print(f"❌ Missing required environment variable: {var}")
                return False
        
        print("✅ Configuration validation passed")
        return True

# Streamlined Architecture Settings
STREAMLINED_SETTINGS = {
    "architecture_version": "2.1",
    "ai_models": ["openai_gpt4o"],  # Only OpenAI
    "fallback_strategy": "technical_analysis",
    "context_enhancement": True,
    "historical_integration": True,
    "multi_factor_analysis": True,
    "dynamic_weighting": True,
    "real_time_data": True
}

# API Endpoints Configuration
API_CONFIG = {
    "openai": {
        "base_url": "https://api.openai.com/v1",
        "chat_completions": "/chat/completions",
        "models": "/models"
    },
    "binance": {
        "base_url": "https://api.binance.com",
        "klines": "/api/v3/klines",
        "ticker_24hr": "/api/v3/ticker/24hr",
        "ticker_price": "/api/v3/ticker/price"
    },
    "coingecko": {
        "base_url": "https://api.coingecko.com/api/v3",
        "simple_price": "/simple/price",
        "coins": "/coins"
    }
}

# Logging Configuration
LOGGING_CONFIG = {
    "level": "INFO",
    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    "handlers": ["console", "file"],
    "file_path": "logs/streamlined_architecture.log"
}

# Cache Configuration
CACHE_CONFIG = {
    "redis_url": os.getenv("REDIS_URL", "redis://localhost:6379"),
    "default_ttl": 300,  # 5 minutes
    "historical_data_ttl": 3600,  # 1 hour
    "pattern_cache_ttl": 86400,  # 24 hours
    "real_time_ttl": 60  # 1 minute
}

def get_config() -> Dict[str, Any]:
    """Get complete configuration dictionary"""
    return {
        "streamlined": StreamlinedConfig,
        "settings": STREAMLINED_SETTINGS,
        "api": API_CONFIG,
        "logging": LOGGING_CONFIG,
        "cache": CACHE_CONFIG
    }

def print_config_summary():
    """Print configuration summary"""
    print("🎯 STREAMLINED ARCHITECTURE CONFIGURATION")
    print("=" * 50)
    print(f"Version: {STREAMLINED_SETTINGS['architecture_version']}")
    print(f"AI Models: {', '.join(STREAMLINED_SETTINGS['ai_models'])}")
    print(f"Fallback Strategy: {STREAMLINED_SETTINGS['fallback_strategy']}")
    print(f"Context Enhancement: {'✅' if STREAMLINED_SETTINGS['context_enhancement'] else '❌'}")
    print(f"Historical Integration: {'✅' if STREAMLINED_SETTINGS['historical_integration'] else '❌'}")
    print(f"Multi-Factor Analysis: {'✅' if STREAMLINED_SETTINGS['multi_factor_analysis'] else '❌'}")
    print(f"Dynamic Weighting: {'✅' if STREAMLINED_SETTINGS['dynamic_weighting'] else '❌'}")
    print(f"Real-time Data: {'✅' if STREAMLINED_SETTINGS['real_time_data'] else '❌'}")
    print("=" * 50)

if __name__ == "__main__":
    print_config_summary()
    StreamlinedConfig.validate_config()
