"""
Direct test of OpenAI Vision API
"""

import asyncio
import httpx
import base64
import json
import os
from dotenv import load_dotenv
from PIL import Image
import io

load_dotenv()

async def test_openai_vision():
    """
    Test OpenAI Vision API directly
    """
    print("🔍 TESTING OPENAI VISION API DIRECTLY")
    print("=" * 50)
    
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        print("❌ No OpenAI API key found")
        return
    
    print(f"✅ API Key found: {api_key[:20]}...")
    
    # Prepare test image
    image_path = "uploads/test_chart.png"
    try:
        with Image.open(image_path) as img:
            if img.mode != 'RGB':
                img = img.convert('RGB')
            
            # Resize if needed
            if img.size[0] > 1024 or img.size[1] > 1024:
                img.thumbnail((1024, 1024), Image.Resampling.LANCZOS)
            
            buffer = io.BytesIO()
            img.save(buffer, format='PNG')
            image_bytes = buffer.getvalue()
            image_data = base64.b64encode(image_bytes).decode('utf-8')
            
        print(f"✅ Image prepared: {len(image_data)} bytes")
        
    except Exception as e:
        print(f"❌ Error preparing image: {e}")
        return
    
    # Simple prompt
    prompt = """
    Analyze this cryptocurrency chart and extract the following information as JSON:
    
    {
        "current_price": 50000,
        "price_range": {"min": 49000, "max": 51000},
        "trend": "bullish",
        "patterns": ["hammer", "doji"],
        "confidence": 0.8
    }
    
    Look at the chart and provide actual values you can see.
    """
    
    try:
        print("🔄 Calling OpenAI API...")
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(
                "https://api.openai.com/v1/chat/completions",
                headers={
                    "Authorization": f"Bearer {api_key}",
                    "Content-Type": "application/json"
                },
                json={
                    "model": "gpt-4o",
                    "messages": [
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": prompt},
                                {
                                    "type": "image_url",
                                    "image_url": {
                                        "url": f"data:image/png;base64,{image_data}"
                                    }
                                }
                            ]
                        }
                    ],
                    "max_tokens": 1000
                }
            )
            
            print(f"📊 Response status: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result["choices"][0]["message"]["content"]
                
                print(f"✅ OpenAI Response:")
                print(f"📝 Content: {content}")
                
                # Try to extract JSON
                try:
                    start_idx = content.find('{')
                    end_idx = content.rfind('}') + 1
                    if start_idx >= 0 and end_idx > start_idx:
                        json_str = content[start_idx:end_idx]
                        parsed_json = json.loads(json_str)
                        print(f"✅ Parsed JSON:")
                        print(json.dumps(parsed_json, indent=2))
                    else:
                        print("⚠️ No JSON found in response")
                        
                except json.JSONDecodeError as e:
                    print(f"❌ JSON parsing error: {e}")
                    print(f"Raw JSON string: {json_str}")
                    
            else:
                print(f"❌ API Error: {response.status_code}")
                print(f"Response: {response.text}")
                
    except Exception as e:
        print(f"❌ Error calling API: {e}")

if __name__ == "__main__":
    asyncio.run(test_openai_vision())
