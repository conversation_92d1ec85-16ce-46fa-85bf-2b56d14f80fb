"""
Debug script to trace the complete chart analysis pipeline
"""

import cv2
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path
import json
import asyncio

# Import our services
from services.image_processor import ImageProcessor
from services.technical_analysis import TechnicalAnalyzer
from services.ai_predictor import AIPredictor
from models.chart_data import ChartAnalysisRequest

async def debug_chart_analysis(image_path: str):
    """
    Debug the complete chart analysis pipeline step by step
    """
    print("=" * 80)
    print("🔍 DEBUGGING CHART ANALYSIS PIPELINE")
    print("=" * 80)
    
    # Initialize services
    image_processor = ImageProcessor()
    technical_analyzer = TechnicalAnalyzer()
    ai_predictor = AIPredictor()
    
    # Create analysis request
    request = ChartAnalysisRequest(
        file_path=image_path,
        coin_symbol="BTC",
        timeframe="1h",
        chart_type="candlestick"
    )
    
    print(f"📁 Input Image: {image_path}")
    print(f"🪙 Coin: {request.coin_symbol}")
    print(f"⏰ Timeframe: {request.timeframe}")
    print(f"📊 Chart Type: {request.chart_type}")
    print()
    
    # Step 1: Load and preprocess image
    print("🔄 STEP 1: IMAGE LOADING & PREPROCESSING")
    print("-" * 50)
    
    try:
        # Load original image
        original_image = cv2.imread(image_path)
        if original_image is None:
            print("❌ Failed to load image")
            return
        
        print(f"✅ Image loaded successfully")
        print(f"📏 Original dimensions: {original_image.shape}")
        
        # Preprocess image
        processed_image = image_processor._preprocess_image(original_image)
        print(f"📏 Processed dimensions: {processed_image.shape}")
        
        # Save debug images
        debug_dir = Path("debug_output")
        debug_dir.mkdir(exist_ok=True)
        
        cv2.imwrite(str(debug_dir / "01_original.png"), original_image)
        cv2.imwrite(str(debug_dir / "02_processed.png"), cv2.cvtColor(processed_image, cv2.COLOR_RGB2BGR))
        print(f"💾 Debug images saved to {debug_dir}/")
        
    except Exception as e:
        print(f"❌ Error in image preprocessing: {e}")
        return
    
    print()
    
    # Step 2: Chart element detection
    print("🔄 STEP 2: CHART ELEMENT DETECTION")
    print("-" * 50)
    
    try:
        chart_elements = image_processor._detect_chart_elements(processed_image)
        
        print(f"🔍 Horizontal lines detected: {len(chart_elements.get('horizontal_lines', []))}")
        print(f"🔍 Vertical lines detected: {len(chart_elements.get('vertical_lines', []))}")
        print(f"🔍 Grid detected: {chart_elements.get('grid_detected', False)}")
        
        chart_area = chart_elements.get('chart_area', {})
        if chart_area:
            print(f"📊 Chart area detected:")
            print(f"   📍 Position: ({chart_area['x']}, {chart_area['y']})")
            print(f"   📏 Size: {chart_area['width']} x {chart_area['height']}")
        
        # Visualize detected elements
        debug_image = processed_image.copy()
        
        # Draw horizontal lines in blue
        for line in chart_elements.get('horizontal_lines', []):
            cv2.line(debug_image, (line[0], line[1]), (line[2], line[3]), (255, 0, 0), 2)
        
        # Draw vertical lines in green
        for line in chart_elements.get('vertical_lines', []):
            cv2.line(debug_image, (line[0], line[1]), (line[2], line[3]), (0, 255, 0), 2)
        
        # Draw chart area in red
        if chart_area:
            cv2.rectangle(debug_image, 
                         (chart_area['x'], chart_area['y']), 
                         (chart_area['x'] + chart_area['width'], chart_area['y'] + chart_area['height']), 
                         (0, 0, 255), 3)
        
        cv2.imwrite(str(debug_dir / "03_chart_elements.png"), cv2.cvtColor(debug_image, cv2.COLOR_RGB2BGR))
        
    except Exception as e:
        print(f"❌ Error in chart element detection: {e}")
        chart_area = {}
    
    print()
    
    # Step 3: Candlestick extraction
    print("🔄 STEP 3: CANDLESTICK EXTRACTION")
    print("-" * 50)
    
    try:
        if chart_area:
            candlesticks = image_processor._extract_candlestick_data(processed_image, chart_area)
            print(f"🕯️ Candlesticks detected: {len(candlesticks)}")
            
            if candlesticks:
                bullish_count = len([c for c in candlesticks if c['type'] == 'bullish'])
                bearish_count = len([c for c in candlesticks if c['type'] == 'bearish'])
                print(f"   📈 Bullish candles: {bullish_count}")
                print(f"   📉 Bearish candles: {bearish_count}")
                
                # Show sample candlesticks
                print(f"   📊 Sample candlesticks:")
                for i, candle in enumerate(candlesticks[:3]):
                    print(f"      {i+1}. {candle['type']} at x={candle['x']}, size={candle['width']}x{candle['height']}")
            
            # Visualize candlesticks
            candle_debug = processed_image.copy()
            if chart_area:
                x, y, w, h = chart_area['x'], chart_area['y'], chart_area['width'], chart_area['height']
                chart_roi = candle_debug[y:y+h, x:x+w]
                
                for candle in candlesticks:
                    color = (0, 255, 0) if candle['type'] == 'bullish' else (255, 0, 0)
                    cv2.rectangle(chart_roi, 
                                 (candle['x'], candle['y']), 
                                 (candle['x'] + candle['width'], candle['y'] + candle['height']), 
                                 color, 2)
                
                candle_debug[y:y+h, x:x+w] = chart_roi
            
            cv2.imwrite(str(debug_dir / "04_candlesticks.png"), cv2.cvtColor(candle_debug, cv2.COLOR_RGB2BGR))
        else:
            candlesticks = []
            print("⚠️ No chart area detected, skipping candlestick extraction")
    
    except Exception as e:
        print(f"❌ Error in candlestick extraction: {e}")
        candlesticks = []
    
    print()
    
    # Step 4: Complete image processing
    print("🔄 STEP 4: COMPLETE IMAGE PROCESSING")
    print("-" * 50)
    
    try:
        chart_data = await image_processor.process_chart_image(request)
        
        print(f"📊 Processing status: {chart_data.processing_status}")
        print(f"💰 Price data points extracted: {len(chart_data.extracted_prices)}")
        
        if chart_data.extracted_prices:
            first_price = chart_data.extracted_prices[0]
            last_price = chart_data.extracted_prices[-1]
            print(f"   📈 First price: O:{first_price.open:.2f} H:{first_price.high:.2f} L:{first_price.low:.2f} C:{first_price.close:.2f}")
            print(f"   📈 Last price: O:{last_price.open:.2f} H:{last_price.high:.2f} L:{last_price.low:.2f} C:{last_price.close:.2f}")
        
        print(f"🔍 Detected patterns:")
        print(f"   🕯️ Candlestick: {chart_data.detected_patterns.candlestick_patterns}")
        print(f"   📊 Chart: {chart_data.detected_patterns.chart_patterns}")
        print(f"   📈 Trend: {chart_data.detected_patterns.trend_direction} (strength: {chart_data.detected_patterns.trend_strength})")
        
    except Exception as e:
        print(f"❌ Error in complete image processing: {e}")
        return
    
    print()
    
    # Step 5: Technical analysis
    print("🔄 STEP 5: TECHNICAL ANALYSIS")
    print("-" * 50)
    
    try:
        technical_indicators = await technical_analyzer.analyze(chart_data)
        
        print(f"📊 Technical Indicators:")
        print(f"   📈 RSI: {technical_indicators.rsi}")
        print(f"   📊 MACD: {technical_indicators.macd}")
        print(f"   📈 Moving Averages: {technical_indicators.moving_averages}")
        print(f"   📊 Bollinger Bands: {technical_indicators.bollinger_bands}")
        print(f"   📊 Volume Analysis: {technical_indicators.volume_analysis}")
        print(f"   📊 Support/Resistance: {technical_indicators.support_resistance}")
        
    except Exception as e:
        print(f"❌ Error in technical analysis: {e}")
        return
    
    print()
    
    # Step 6: AI prediction
    print("🔄 STEP 6: AI PREDICTION")
    print("-" * 50)
    
    try:
        predictions = await ai_predictor.predict(chart_data, technical_indicators)
        
        print(f"🤖 AI Prediction Results:")
        print(f"   📊 Recommendation: {predictions.recommendation}")
        print(f"   🎯 Confidence: {predictions.confidence:.2%}")
        print(f"   💰 Target Price: ${predictions.target_price:.2f}")
        print(f"   ⏰ Time Horizon: {predictions.time_horizon}")
        print(f"   🛡️ Risk Level: {predictions.risk_assessment.risk_level}")
        print(f"   🔻 Stop Loss: ${predictions.risk_assessment.stop_loss:.2f}")
        print(f"   🔺 Take Profit: ${predictions.risk_assessment.take_profit:.2f}")
        print(f"   ⚖️ Risk/Reward: {predictions.risk_assessment.risk_reward_ratio:.2f}")
        print(f"   💭 Reasoning: {predictions.reasoning}")
        
    except Exception as e:
        print(f"❌ Error in AI prediction: {e}")
        return
    
    print()
    print("=" * 80)
    print("✅ ANALYSIS COMPLETE!")
    print("=" * 80)
    print(f"📁 Debug images saved to: {debug_dir}/")
    print("   01_original.png - Original uploaded image")
    print("   02_processed.png - Preprocessed image")
    print("   03_chart_elements.png - Detected lines and chart area")
    print("   04_candlesticks.png - Detected candlesticks")

if __name__ == "__main__":
    # Test with the uploaded chart
    test_image = "uploads/test_chart.png"
    if Path(test_image).exists():
        asyncio.run(debug_chart_analysis(test_image))
    else:
        print(f"❌ Test image not found: {test_image}")
        print("Available images:")
        for img in Path("uploads").glob("*.png"):
            print(f"   📁 {img}")
