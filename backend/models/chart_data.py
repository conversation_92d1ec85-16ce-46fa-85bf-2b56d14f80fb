"""
Data models for chart analysis
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any
from datetime import datetime
from enum import Enum

class ChartType(str, Enum):
    CANDLESTICK = "candlestick"
    LINE = "line"
    AREA = "area"
    OHLC = "ohlc"

class Timeframe(str, Enum):
    ONE_MINUTE = "1m"
    FIVE_MINUTES = "5m"
    FIFTEEN_MINUTES = "15m"
    ONE_HOUR = "1h"
    FOUR_HOURS = "4h"
    ONE_DAY = "1d"
    ONE_WEEK = "1w"

class PositionRecommendation(str, Enum):
    STRONG_BUY = "strong_buy"
    BUY = "buy"
    HOLD = "hold"
    SELL = "sell"
    STRONG_SELL = "strong_sell"

class ChartAnalysisRequest(BaseModel):
    file_path: str
    coin_symbol: Optional[str] = None
    timeframe: str = "1h"
    chart_type: str = "candlestick"
    additional_notes: Optional[str] = None

class PriceData(BaseModel):
    timestamp: Optional[datetime] = None
    open: Optional[float] = None
    high: Optional[float] = None
    low: Optional[float] = None
    close: Optional[float] = None
    volume: Optional[float] = None

class TechnicalIndicators(BaseModel):
    rsi: Optional[float] = None
    macd: Optional[Dict[str, float]] = None
    moving_averages: Optional[Dict[str, float]] = None
    bollinger_bands: Optional[Dict[str, float]] = None
    volume_analysis: Optional[Dict[str, Any]] = None
    support_resistance: Optional[Dict[str, List[float]]] = None

class PatternRecognition(BaseModel):
    candlestick_patterns: List[str] = []
    chart_patterns: List[str] = []
    trend_direction: Optional[str] = None
    trend_strength: Optional[float] = None

class RiskAssessment(BaseModel):
    risk_level: str = "medium"
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    risk_reward_ratio: Optional[float] = None

class PredictionResult(BaseModel):
    recommendation: PositionRecommendation
    confidence: float = Field(..., ge=0, le=1)
    target_price: Optional[float] = None
    time_horizon: str = "24h"
    reasoning: str
    risk_assessment: RiskAssessment

class ChartData(BaseModel):
    extracted_prices: List[PriceData] = []
    detected_patterns: PatternRecognition = PatternRecognition()
    chart_metadata: Dict[str, Any] = {}
    processing_status: str = "success"

class ChartAnalysisResponse(BaseModel):
    analysis_id: str
    timestamp: datetime = Field(default_factory=datetime.now)
    chart_data: ChartData
    technical_indicators: TechnicalIndicators
    predictions: PredictionResult
    file_url: str
    processing_time: Optional[float] = None

class AnalysisHistory(BaseModel):
    analysis_id: str
    timestamp: datetime
    coin_symbol: Optional[str]
    recommendation: PositionRecommendation
    confidence: float
    file_url: str
