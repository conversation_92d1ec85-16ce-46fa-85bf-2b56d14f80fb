"""
Demo script showing AI Vision capabilities with mock responses
"""

import asyncio
import json
from services.ai_vision_processor import AIVisionProcessor
from models.chart_data import PriceData, PatternRecognition, ChartData

class MockAIVisionProcessor(AIVisionProcessor):
    """
    Mock AI Vision processor for demonstration
    """
    
    async def _analyze_with_claude(self, image_data: str, coin_symbol: str, timeframe: str) -> dict:
        """
        Mock Claude response with realistic chart analysis
        """
        return {
            "price_data": [
                {"timestamp": "0", "open": 49850.25, "high": 50120.80, "low": 49720.15, "close": 50050.60, "volume": 1250},
                {"timestamp": "1", "open": 50050.60, "high": 50380.45, "low": 49950.30, "close": 50280.75, "volume": 1180},
                {"timestamp": "2", "open": 50280.75, "high": 50420.90, "low": 50180.20, "close": 50350.40, "volume": 980},
                {"timestamp": "3", "open": 50350.40, "high": 50580.65, "low": 50250.85, "close": 50520.30, "volume": 1420},
                {"timestamp": "4", "open": 50520.30, "high": 50720.55, "low": 50480.10, "close": 50680.25, "volume": 1350},
                {"timestamp": "5", "open": 50680.25, "high": 50850.70, "low": 50620.40, "close": 50780.85, "volume": 1100},
                {"timestamp": "6", "open": 50780.85, "high": 50920.30, "low": 50650.75, "close": 50720.60, "volume": 890},
                {"timestamp": "7", "open": 50720.60, "high": 50780.45, "low": 50580.20, "close": 50620.35, "volume": 1050},
                {"timestamp": "8", "open": 50620.35, "high": 50750.80, "low": 50520.15, "close": 50690.50, "volume": 1280},
                {"timestamp": "9", "open": 50690.50, "high": 50820.75, "low": 50650.30, "close": 50780.40, "volume": 1150},
                {"timestamp": "10", "open": 50780.40, "high": 50950.85, "low": 50720.60, "close": 50890.25, "volume": 1380},
                {"timestamp": "11", "open": 50890.25, "high": 51080.50, "low": 50850.75, "close": 51020.80, "volume": 1450},
                {"timestamp": "12", "open": 51020.80, "high": 51150.35, "low": 50980.20, "close": 51080.65, "volume": 1200},
                {"timestamp": "13", "open": 51080.65, "high": 51220.90, "low": 51050.40, "close": 51180.75, "volume": 1320},
                {"timestamp": "14", "open": 51180.75, "high": 51280.30, "low": 51120.85, "close": 51250.50, "volume": 1080},
                {"timestamp": "15", "open": 51250.50, "high": 51350.75, "low": 51180.20, "close": 51320.40, "volume": 950},
                {"timestamp": "16", "open": 51320.40, "high": 51420.85, "low": 51280.60, "close": 51380.25, "volume": 1150},
                {"timestamp": "17", "open": 51380.25, "high": 51480.70, "low": 51320.45, "close": 51450.80, "volume": 1250},
                {"timestamp": "18", "open": 51450.80, "high": 51520.35, "low": 51380.90, "close": 51480.60, "volume": 1100},
                {"timestamp": "19", "open": 51480.60, "high": 51580.25, "low": 51420.75, "close": 51550.40, "volume": 1380}
            ],
            "current_price": 51550.40,
            "price_range": {"min": 49720.15, "max": 51580.25},
            "candlestick_patterns": ["bullish_engulfing", "hammer", "doji"],
            "chart_patterns": ["ascending_triangle", "higher_lows"],
            "trend_direction": "bullish",
            "trend_strength": 0.75,
            "technical_indicators": {
                "rsi": 68.5,
                "moving_averages": {
                    "ma20": 50850.30,
                    "ma50": 50420.75,
                    "ma200": 49980.60
                },
                "support_levels": [50200.00, 50500.00, 50800.00],
                "resistance_levels": [51600.00, 51800.00, 52000.00],
                "macd": {
                    "macd": 125.45,
                    "signal": 118.30,
                    "histogram": 7.15
                },
                "bollinger_bands": {
                    "upper": 51850.75,
                    "middle": 50850.30,
                    "lower": 49849.85,
                    "bandwidth": 3.93
                }
            },
            "analysis_confidence": 0.92,
            "notes": "Strong bullish momentum with clear uptrend. Price is above all major moving averages. RSI approaching overbought but not extreme. Volume supporting the move higher. Key resistance at 51600-51800 area."
        }

async def demo_ai_vision_analysis():
    """
    Demonstrate AI vision analysis capabilities
    """
    print("🤖 AI VISION CHART ANALYSIS DEMO")
    print("=" * 60)
    
    print("📊 Simulating AI Vision Analysis...")
    print("   (This shows what happens when API keys are configured)")
    
    # Use mock processor
    processor = MockAIVisionProcessor()
    
    try:
        # Simulate analysis
        result = await processor.process_chart_with_ai_vision("uploads/test_chart.png", "BTC", "1h")
        
        print(f"\n✅ AI Analysis Complete!")
        print(f"   Status: {result.processing_status}")
        print(f"   Method: AI Vision (Claude/OpenAI)")
        
        print(f"\n📈 EXTRACTED PRICE DATA:")
        print(f"   • Data points: {len(result.extracted_prices)}")
        
        if result.extracted_prices:
            first = result.extracted_prices[0]
            last = result.extracted_prices[-1]
            
            print(f"   • First candle: O:${first.open:.2f} H:${first.high:.2f} L:${first.low:.2f} C:${first.close:.2f}")
            print(f"   • Last candle:  O:${last.open:.2f} H:${last.high:.2f} L:${last.low:.2f} C:${last.close:.2f}")
            
            # Calculate statistics
            all_highs = [p.high for p in result.extracted_prices if p.high]
            all_lows = [p.low for p in result.extracted_prices if p.low]
            
            if all_highs and all_lows:
                price_change = last.close - first.open
                price_change_pct = (price_change / first.open) * 100
                
                print(f"   • Price range: ${min(all_lows):.2f} - ${max(all_highs):.2f}")
                print(f"   • Price change: ${price_change:+.2f} ({price_change_pct:+.2f}%)")
        
        print(f"\n🔍 DETECTED PATTERNS:")
        print(f"   • Candlestick: {', '.join(result.detected_patterns.candlestick_patterns)}")
        print(f"   • Chart formations: {', '.join(result.detected_patterns.chart_patterns)}")
        print(f"   • Trend: {result.detected_patterns.trend_direction} (strength: {result.detected_patterns.trend_strength:.1%})")
        
        print(f"\n📊 AI INSIGHTS:")
        metadata = result.chart_metadata
        if metadata.get("current_price"):
            print(f"   • Current price: ${metadata['current_price']:.2f}")
        
        if metadata.get("technical_indicators"):
            ti = metadata["technical_indicators"]
            if ti.get("rsi"):
                print(f"   • RSI: {ti['rsi']:.1f}")
            if ti.get("moving_averages"):
                mas = ti["moving_averages"]
                print(f"   • MA20: ${mas.get('ma20', 0):.2f}")
                print(f"   • MA50: ${mas.get('ma50', 0):.2f}")
            if ti.get("support_levels"):
                supports = ti["support_levels"][:2]  # Show first 2
                print(f"   • Support levels: {', '.join([f'${s:.0f}' for s in supports])}")
            if ti.get("resistance_levels"):
                resistances = ti["resistance_levels"][:2]  # Show first 2
                print(f"   • Resistance levels: {', '.join([f'${r:.0f}' for r in resistances])}")
        
        if metadata.get("analysis_confidence"):
            print(f"   • AI Confidence: {metadata['analysis_confidence']:.1%}")
        
        if metadata.get("notes"):
            print(f"\n💭 AI ANALYSIS NOTES:")
            print(f"   {metadata['notes']}")
        
        print(f"\n🎯 KEY ADVANTAGES OF AI VISION:")
        print(f"   ✅ Reads actual price values from chart axes")
        print(f"   ✅ Understands chart context and timeframes")
        print(f"   ✅ Identifies complex patterns accurately")
        print(f"   ✅ Extracts technical indicator values")
        print(f"   ✅ Provides confidence scoring")
        print(f"   ✅ Works with any chart style or platform")
        
    except Exception as e:
        print(f"❌ Error in demo: {str(e)}")

def show_setup_instructions():
    """
    Show setup instructions for AI vision
    """
    print(f"\n" + "=" * 60)
    print("🛠️  SETUP INSTRUCTIONS FOR AI VISION")
    print("=" * 60)
    
    print("\n1️⃣ GET API KEYS:")
    print("   🔗 Claude (Anthropic): https://console.anthropic.com/")
    print("      • Create account and get API key")
    print("      • $3-5 per 1000 image analyses")
    print("      • Excellent for chart reading")
    
    print("\n   🔗 OpenAI: https://platform.openai.com/api-keys")
    print("      • Create account and get API key")
    print("      • $0.01-0.02 per image analysis")
    print("      • Good alternative option")
    
    print("\n2️⃣ CONFIGURE ENVIRONMENT:")
    print("   📝 Create .env file:")
    print("      cp .env.example .env")
    
    print("\n   🔑 Add your API keys to .env:")
    print("      ANTHROPIC_API_KEY=sk-ant-api03-...")
    print("      OPENAI_API_KEY=sk-...")
    
    print("\n3️⃣ RESTART THE SERVER:")
    print("   🔄 The enhanced backend will automatically use AI vision")
    print("   📊 Upload any chart to see accurate analysis")
    
    print("\n💡 COST ESTIMATION:")
    print("   • Claude: ~$0.003-0.005 per chart analysis")
    print("   • OpenAI: ~$0.01-0.02 per chart analysis")
    print("   • 1000 analyses ≈ $3-20 depending on provider")

if __name__ == "__main__":
    asyncio.run(demo_ai_vision_analysis())
    show_setup_instructions()
