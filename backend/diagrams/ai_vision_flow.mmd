flowchart TD
    START([Chart Image Upload]) --> VALIDATE{Validate Image}
    VALIDATE -->|Valid| AIVISION[AI Vision Processor]
    VALIDATE -->|Invalid| ERROR[Return Error]
    
    AIVISION --> OPENAI{Try OpenAI GPT-4o}
    
    OPENAI -->|Success| PARSE1[Parse OpenAI Response]
    OPENAI -->|Fail| CLAUDE{Try Claude Vision}
    
    CLAUDE -->|Success| PARSE2[Parse Claude Response]
    CLAUDE -->|Fail| FALLBACK[Computer Vision Fallback]
    
    PARSE1 --> EXTRACT1[Extract Price Data]
    PARSE2 --> EXTRACT2[Extract Price Data]
    FALLBACK --> EXTRACT3[Basic Pattern Detection]
    
    EXTRACT1 --> ACCURACY1[90-95% Accuracy]
    EXTRACT2 --> ACCURACY2[85-90% Accuracy]
    EXTRACT3 --> ACCURACY3[40-60% Accuracy]
    
    ACCURACY1 --> COMBINE[Combine Results]
    ACCURACY2 --> COMBINE
    ACCURACY3 --> COMBIN<PERSON>
    
    COMBINE --> TECHNICAL[Technical Analysis]
    TECHNICAL --> RSI[Calculate RSI]
    TECHNICAL --> MACD[Calculate MACD]
    TECHNICAL --> MA[Moving Averages]
    TECHNICAL --> BB[Bollinger Bands]
    TECHNICAL --> VOL[Volume Analysis]
    TECHNICAL --> SR[Support/Resistance]
    
    RSI --> PREDICT[AI Prediction Engine]
    MACD --> PREDICT
    MA --> PREDICT
    BB --> PREDICT
    VOL --> PREDICT
    SR --> PREDICT
    
    PREDICT --> TREND[Trend Analysis]
    PREDICT --> MOMENTUM[Momentum Score]
    PREDICT --> VOLUME[Volume Score]
    PREDICT --> PATTERN[Pattern Score]
    
    TREND --> SCORE[Overall Score]
    MOMENTUM --> SCORE
    VOLUME --> SCORE
    PATTERN --> SCORE
    
    SCORE --> RECOMMENDATION[Generate Recommendation]
    SCORE --> CONFIDENCE[Calculate Confidence]
    SCORE --> TARGET[Target Price]
    SCORE --> RISK[Risk Assessment]
    
    RECOMMENDATION --> RESPONSE[Final Response]
    CONFIDENCE --> RESPONSE
    TARGET --> RESPONSE
    RISK --> RESPONSE
    
    RESPONSE --> END([Return to Frontend])
    
    %% Styling
    classDef success fill:#d4edda,stroke:#155724
    classDef warning fill:#fff3cd,stroke:#856404
    classDef danger fill:#f8d7da,stroke:#721c24
    classDef info fill:#d1ecf1,stroke:#0c5460
    classDef primary fill:#cce5ff,stroke:#004085
    
    class PARSE1,EXTRACT1,ACCURACY1 success
    class PARSE2,EXTRACT2,ACCURACY2 warning
    class FALLBACK,EXTRACT3,ACCURACY3 danger
    class TECHNICAL,PREDICT,SCORE info
    class RESPONSE,END primary
