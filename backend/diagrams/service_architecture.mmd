graph LR
    subgraph "Client Layer"
        WEB[Web Browser]
        MOBILE[Mobile App]
        API_CLIENT[API Client]
    end
    
    subgraph "Load Balancer"
        LB[Load Balancer]
    end
    
    subgraph "API Gateway Layer"
        GATEWAY[FastAPI Gateway]
        AUTH[Authentication]
        RATE[Rate Limiting]
        CORS[CORS Handler]
    end
    
    subgraph "Core Services"
        UPLOAD[Upload Service]
        ANALYSIS[Analysis Service]
        CACHE[Cache Service]
        NOTIFICATION[Notification Service]
    end
    
    subgraph "AI Processing Layer"
        AI_VISION[AI Vision Service]
        CV_FALLBACK[Computer Vision Service]
        TECHNICAL[Technical Analysis Service]
        PREDICTION[Prediction Service]
    end
    
    subgraph "External AI APIs"
        OPENAI_API[OpenAI API]
        CLAUDE_API[Claude API]
    end
    
    subgraph "Data Layer"
        FILE_STORAGE[File Storage]
        REDIS[Redis Cache]
        LOGS[Log Storage]
        METRICS[Metrics DB]
    end
    
    subgraph "Monitoring & Observability"
        MONITORING[Health Monitoring]
        LOGGING[Centralized Logging]
        METRICS_COLLECTOR[Metrics Collection]
        ALERTS[Alert System]
    end
    
    %% Client connections
    WEB --> LB
    MOBILE --> LB
    API_CLIENT --> LB
    
    %% Load balancer to gateway
    LB --> GATEWAY
    
    %% Gateway layer
    GATEWAY --> AUTH
    GATEWAY --> RATE
    GATEWAY --> CORS
    
    %% Gateway to services
    GATEWAY --> UPLOAD
    GATEWAY --> ANALYSIS
    GATEWAY --> CACHE
    GATEWAY --> NOTIFICATION
    
    %% Core service interactions
    UPLOAD --> FILE_STORAGE
    ANALYSIS --> AI_VISION
    ANALYSIS --> TECHNICAL
    ANALYSIS --> PREDICTION
    CACHE --> REDIS
    
    %% AI processing
    AI_VISION --> OPENAI_API
    AI_VISION --> CLAUDE_API
    AI_VISION --> CV_FALLBACK
    
    %% Data storage
    ANALYSIS --> FILE_STORAGE
    ANALYSIS --> LOGS
    CACHE --> REDIS
    
    %% Monitoring
    GATEWAY --> MONITORING
    ANALYSIS --> LOGGING
    AI_VISION --> METRICS_COLLECTOR
    MONITORING --> ALERTS
    
    %% Styling
    classDef client fill:#e3f2fd
    classDef gateway fill:#f3e5f5
    classDef service fill:#e8f5e8
    classDef ai fill:#fff3e0
    classDef external fill:#ffebee
    classDef data fill:#f1f8e9
    classDef monitoring fill:#fce4ec
    
    class WEB,MOBILE,API_CLIENT client
    class LB,GATEWAY,AUTH,RATE,CORS gateway
    class UPLOAD,ANALYSIS,CACHE,NOTIFICATION service
    class AI_VISION,CV_FALLBACK,TECHNICAL,PREDICTION ai
    class OPENAI_API,CLAUDE_API external
    class FILE_STORAGE,REDIS,LOGS,METRICS data
    class MONITORING,LOGGING,METRICS_COLLECTOR,ALERTS monitoring
