sequenceDiagram
    participant User
    participant Frontend
    participant API
    participant AIVision
    participant OpenAI
    participant Claude
    participant ComputerVision
    participant TechnicalAnalysis
    participant AIPredictor
    participant Database

    User->>Frontend: Upload Chart Image
    Frontend->>API: POST /api/v1/analyze-chart
    
    Note over API: File Validation & Storage
    API->>API: Validate file type & size
    API->>Database: Store uploaded file
    
    Note over API,AIVision: AI Vision Processing (Primary)
    API->>AIVision: Process chart with AI
    AIVision->>OpenAI: Send image + analysis prompt
    
    alt OpenAI Success
        OpenAI-->>AIVision: Return detailed analysis JSON
        AIVision->>AIVision: Parse price data & patterns
        Note over AIVision: ✅ 90-95% Accuracy
    else OpenAI Fails
        AIVision->><PERSON>: Fallback to Claude Vision
        alt Claude Success
            Claude-->>AIVision: Return analysis JSON
            AIVision->>AIVision: Parse data
        else Claude Fails
            Note over AIVision,ComputerVision: Computer Vision Fallback
            AIVision->>ComputerVision: Traditional CV processing
            ComputerVision->>ComputerVision: Extract basic patterns
            ComputerVision-->>AIVision: Return basic data
            Note over ComputerVision: ⚠️ 40-60% Accuracy
        end
    end
    
    AIVision-->>API: Return chart data
    
    Note over API,TechnicalAnalysis: Technical Analysis
    API->>TechnicalAnalysis: Analyze extracted data
    TechnicalAnalysis->>TechnicalAnalysis: Calculate RSI, MACD, MA
    TechnicalAnalysis->>TechnicalAnalysis: Bollinger Bands, Volume
    TechnicalAnalysis->>TechnicalAnalysis: Support/Resistance levels
    TechnicalAnalysis-->>API: Return technical indicators
    
    Note over API,AIPredictor: AI Prediction
    API->>AIPredictor: Generate predictions
    AIPredictor->>AIPredictor: Analyze trend signals
    AIPredictor->>AIPredictor: Calculate momentum score
    AIPredictor->>AIPredictor: Assess volume patterns
    AIPredictor->>AIPredictor: Evaluate chart patterns
    AIPredictor->>AIPredictor: Generate recommendation
    AIPredictor->>AIPredictor: Calculate risk assessment
    AIPredictor-->>API: Return prediction results
    
    Note over API: Compile Final Response
    API->>API: Combine all analysis results
    API->>Database: Cache analysis results
    API-->>Frontend: Return complete analysis
    
    Frontend->>Frontend: Display results
    Frontend-->>User: Show analysis & recommendations
    
    Note over User,Database: Analysis Complete
    Note over AIVision: 🎯 Key Innovation: AI reads exact prices from chart axes
    Note over TechnicalAnalysis: 📊 Professional-grade technical indicators
    Note over AIPredictor: 🤖 Multi-factor AI trading recommendations
