erDiagram
    ChartAnalysisRequest {
        string file_path
        string coin_symbol
        string timeframe
        string chart_type
        string additional_notes
    }
    
    ChartData {
        list extracted_prices
        object detected_patterns
        dict chart_metadata
        string processing_status
    }
    
    PriceData {
        float open
        float high
        float low
        float close
        int volume
        string timestamp
    }
    
    PatternRecognition {
        list candlestick_patterns
        list chart_patterns
        string trend_direction
        float trend_strength
    }
    
    TechnicalIndicators {
        float rsi
        dict macd
        dict moving_averages
        dict bollinger_bands
        dict volume_analysis
        dict support_resistance
    }
    
    PredictionResult {
        enum recommendation
        float confidence
        float target_price
        string time_horizon
        string reasoning
        object risk_assessment
    }
    
    RiskAssessment {
        string risk_level
        float stop_loss
        float take_profit
        float risk_reward_ratio
    }
    
    AIVisionResult {
        list price_data
        float current_price
        dict price_range
        list candlestick_patterns
        list chart_patterns
        string trend_direction
        float trend_strength
        dict technical_indicators
        float analysis_confidence
        string notes
    }
    
    ChartAnalysisResponse {
        string analysis_id
        string timestamp
        object chart_data
        object technical_indicators
        object predictions
        string file_url
        float processing_time
        string analysis_type
    }
    
    %% Relationships
    ChartAnalysisRequest ||--|| ChartData : processes
    ChartData ||--o{ PriceData : contains
    ChartData ||--|| PatternRecognition : includes
    ChartData ||--|| TechnicalIndicators : generates
    TechnicalIndicators ||--|| PredictionResult : feeds_into
    PredictionResult ||--|| RiskAssessment : includes
    AIVisionResult ||--|| ChartData : converts_to
    ChartData ||--|| ChartAnalysisResponse : builds
