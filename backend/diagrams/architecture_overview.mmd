graph TB
    %% Frontend Layer
    subgraph "Frontend Layer (React + Next.js)"
        UI[Chart Upload Interface]
        DISPLAY[Analysis Results Display]
        CONFIG[Configuration Panel]
        CHARTS[Chart Visualization]
    end

    %% API Gateway
    subgraph "API Gateway (FastAPI)"
        MAIN[Enhanced Main API]
        CORS[CORS Middleware]
        UPLOAD[File Upload Handler]
        ENDPOINTS[REST Endpoints]
    end

    %% Core Processing Services
    subgraph "Core AI Services"
        AIVISION[AI Vision Processor]
        IMGPROC[Image Processor]
        TECHANALYSIS[Technical Analyzer]
        AIPREDICT[AI Predictor]
    end

    %% AI Vision Models
    subgraph "AI Vision Models"
        OPENAI[OpenAI GPT-4o Vision]
        CLAUDE[Claude Vision API]
        FALLBACK[Computer Vision Fallback]
    end

    %% Technical Analysis Components
    subgraph "Technical Analysis Engine"
        RSI[RSI Calculator]
        MACD[MACD Calculator]
        MA[Moving Averages]
        BB[Bollinger Bands]
        VOLUME[Volume Analysis]
        SUPPORT[Support/Resistance]
    end

    %% Pattern Recognition
    subgraph "Pattern Recognition"
        CANDLESTICK[Candlestick Patterns]
        CHARTPATTERNS[Chart Formations]
        TRENDS[Trend Analysis]
        MOMENTUM[Momentum Detection]
    end

    %% Data Models
    subgraph "Data Models"
        CHARTDATA[Chart Data Model]
        PRICEDATA[Price Data Model]
        PATTERNS[Pattern Models]
        PREDICTIONS[Prediction Models]
        TECHNICAL[Technical Indicators Model]
    end

    %% External APIs
    subgraph "External APIs"
        OPENAIAPI[OpenAI API]
        CLAUDEAPI[Anthropic Claude API]
    end

    %% Storage
    subgraph "Storage"
        UPLOADS[File Storage]
        CACHE[Analysis Cache]
        LOGS[Application Logs]
    end

    %% User Flow
    UI --> UPLOAD
    UPLOAD --> MAIN
    MAIN --> AIVISION
    
    %% AI Vision Processing
    AIVISION --> OPENAI
    AIVISION --> CLAUDE
    AIVISION --> FALLBACK
    
    OPENAI --> OPENAIAPI
    CLAUDE --> CLAUDEAPI
    
    %% Fallback to Computer Vision
    AIVISION -.->|"If AI Vision Fails"| IMGPROC
    
    %% Technical Analysis Flow
    AIVISION --> TECHANALYSIS
    IMGPROC --> TECHANALYSIS
    
    TECHANALYSIS --> RSI
    TECHANALYSIS --> MACD
    TECHANALYSIS --> MA
    TECHANALYSIS --> BB
    TECHANALYSIS --> VOLUME
    TECHANALYSIS --> SUPPORT
    
    %% Pattern Recognition Flow
    AIVISION --> CANDLESTICK
    AIVISION --> CHARTPATTERNS
    TECHANALYSIS --> TRENDS
    TECHANALYSIS --> MOMENTUM
    
    %% AI Prediction Flow
    TECHANALYSIS --> AIPREDICT
    CANDLESTICK --> AIPREDICT
    CHARTPATTERNS --> AIPREDICT
    TRENDS --> AIPREDICT
    
    %% Data Model Integration
    AIVISION --> CHARTDATA
    TECHANALYSIS --> TECHNICAL
    AIPREDICT --> PREDICTIONS
    CANDLESTICK --> PATTERNS
    
    %% Response Flow
    AIPREDICT --> MAIN
    MAIN --> DISPLAY
    MAIN --> CHARTS
    
    %% Storage Integration
    UPLOAD --> UPLOADS
    MAIN --> CACHE
    MAIN --> LOGS
    
    %% Configuration
    CONFIG --> MAIN

    %% Styling
    classDef frontend fill:#e1f5fe
    classDef api fill:#f3e5f5
    classDef ai fill:#e8f5e8
    classDef technical fill:#fff3e0
    classDef external fill:#ffebee
    classDef storage fill:#f1f8e9
    classDef models fill:#fce4ec

    class UI,DISPLAY,CONFIG,CHARTS frontend
    class MAIN,CORS,UPLOAD,ENDPOINTS api
    class AIVISION,IMGPROC,TECHANALYSIS,AIPREDICT ai
    class RSI,MACD,MA,BB,VOLUME,SUPPORT,CANDLESTICK,CHARTPATTERNS,TRENDS,MOMENTUM technical
    class OPENAIAPI,CLAUDEAPI external
    class UPLOADS,CACHE,LOGS storage
    class CHARTDATA,PRICEDATA,PATTERNS,PREDICTIONS,TECHNICAL models
