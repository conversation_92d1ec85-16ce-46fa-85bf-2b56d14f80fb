"""
Test script for AI Vision chart analysis
"""

import asyncio
import os
from pathlib import Path
from dotenv import load_dotenv
from services.ai_vision_processor import AIVisionProcessor

# Load environment variables
load_dotenv()

async def test_ai_vision():
    """
    Test AI vision capabilities
    """
    print("🔍 TESTING AI VISION CHART ANALYSIS")
    print("=" * 60)
    
    # Check API keys
    claude_key = os.getenv("ANTHROPIC_API_KEY")
    openai_key = os.getenv("OPENAI_API_KEY")
    
    print("🔑 API Key Status:")
    print(f"   Claude (Anthropic): {'✅ Available' if claude_key else '❌ Not set'}")
    print(f"   OpenAI: {'✅ Available' if openai_key else '❌ Not set'}")
    
    if not claude_key and not openai_key:
        print("\n⚠️  No AI vision API keys found!")
        print("To use AI vision analysis, you need to set up API keys:")
        print("\n📝 Setup Instructions:")
        print("1. Copy .env.example to .env:")
        print("   cp .env.example .env")
        print("\n2. Add your API keys to .env:")
        print("   ANTHROPIC_API_KEY=your_claude_api_key")
        print("   OPENAI_API_KEY=your_openai_api_key")
        print("\n🔗 Get API keys from:")
        print("   • Claude: https://console.anthropic.com/")
        print("   • OpenAI: https://platform.openai.com/api-keys")
        print("\n💡 Benefits of AI Vision:")
        print("   • Accurate price reading from chart axes")
        print("   • Better pattern recognition")
        print("   • Understanding of chart context")
        print("   • Reading of technical indicator values")
        print("   • More reliable OHLC data extraction")
        return
    
    # Test with available image
    test_image = "uploads/test_chart.png"
    if not Path(test_image).exists():
        print(f"\n❌ Test image not found: {test_image}")
        print("Available images:")
        for img in Path("uploads").glob("*.png"):
            print(f"   📁 {img}")
        return
    
    print(f"\n📊 Testing with: {test_image}")
    
    # Initialize AI vision processor
    processor = AIVisionProcessor()
    
    try:
        print("\n🔄 Processing chart with AI vision...")
        result = await processor.process_chart_with_ai_vision(test_image, "BTC", "1h")
        
        print(f"\n📊 AI Vision Results:")
        print(f"   Status: {result.processing_status}")
        print(f"   Price points: {len(result.extracted_prices)}")
        
        if result.extracted_prices:
            first = result.extracted_prices[0]
            last = result.extracted_prices[-1]
            print(f"   First price: ${first.close:.2f}")
            print(f"   Last price: ${last.close:.2f}")
            
            # Calculate price range
            all_prices = []
            for p in result.extracted_prices:
                if p.high: all_prices.append(p.high)
                if p.low: all_prices.append(p.low)
            
            if all_prices:
                print(f"   Price range: ${min(all_prices):.2f} - ${max(all_prices):.2f}")
        
        print(f"   Patterns detected:")
        print(f"     Candlestick: {result.detected_patterns.candlestick_patterns}")
        print(f"     Chart: {result.detected_patterns.chart_patterns}")
        print(f"     Trend: {result.detected_patterns.trend_direction}")
        
        if result.chart_metadata.get("ai_analysis"):
            print(f"   AI Confidence: {result.chart_metadata.get('analysis_confidence', 0):.1%}")
            if result.chart_metadata.get("notes"):
                print(f"   Notes: {result.chart_metadata['notes']}")
        
        print("\n✅ AI Vision test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error testing AI vision: {str(e)}")

def show_comparison():
    """
    Show comparison between computer vision and AI vision
    """
    print("\n" + "=" * 60)
    print("🔍 COMPUTER VISION vs AI VISION COMPARISON")
    print("=" * 60)
    
    print("\n📊 COMPUTER VISION (Current Basic Method):")
    print("   ✅ Fast processing")
    print("   ✅ No API costs")
    print("   ✅ Works offline")
    print("   ❌ Inaccurate price extraction")
    print("   ❌ Limited pattern recognition")
    print("   ❌ Cannot read text/numbers")
    print("   ❌ Struggles with different chart styles")
    
    print("\n🤖 AI VISION (Claude/OpenAI):")
    print("   ✅ Accurate price reading from axes")
    print("   ✅ Advanced pattern recognition")
    print("   ✅ Reads actual price values")
    print("   ✅ Understands chart context")
    print("   ✅ Works with any chart style")
    print("   ✅ Can read technical indicators")
    print("   ❌ Requires API key")
    print("   ❌ Small API cost per analysis")
    print("   ❌ Requires internet connection")
    
    print("\n💡 RECOMMENDATION:")
    print("   Use AI Vision for production applications")
    print("   Computer Vision as fallback method")

if __name__ == "__main__":
    asyncio.run(test_ai_vision())
    show_comparison()
