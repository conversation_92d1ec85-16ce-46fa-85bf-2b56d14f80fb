"""
Technical Analysis Service
Calculates technical indicators and performs chart analysis
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Any, Optional
import logging

from models.chart_data import ChartData, TechnicalIndicators, PriceData

logger = logging.getLogger(__name__)

class TechnicalAnalyzer:
    def __init__(self):
        self.indicators = {}
        
    async def analyze(self, chart_data: ChartData) -> TechnicalIndicators:
        """
        Perform comprehensive technical analysis
        """
        try:
            if not chart_data.extracted_prices:
                logger.warning("No price data available for technical analysis")
                return TechnicalIndicators()
            
            # Convert price data to DataFrame
            df = self._create_dataframe(chart_data.extracted_prices)
            
            # Calculate various technical indicators
            rsi = self._calculate_rsi(df)
            macd = self._calculate_macd(df)
            moving_averages = self._calculate_moving_averages(df)
            bollinger_bands = self._calculate_bollinger_bands(df)
            volume_analysis = self._analyze_volume(df)
            support_resistance = self._find_support_resistance(df)
            
            return TechnicalIndicators(
                rsi=rsi,
                macd=macd,
                moving_averages=moving_averages,
                bollinger_bands=bollinger_bands,
                volume_analysis=volume_analysis,
                support_resistance=support_resistance
            )
            
        except Exception as e:
            logger.error(f"Error in technical analysis: {str(e)}")
            return TechnicalIndicators()
    
    def _create_dataframe(self, price_data: List[PriceData]) -> pd.DataFrame:
        """Convert price data to pandas DataFrame"""
        data = []
        for price in price_data:
            data.append({
                'open': price.open or 0,
                'high': price.high or 0,
                'low': price.low or 0,
                'close': price.close or 0,
                'volume': price.volume or 0
            })
        
        df = pd.DataFrame(data)
        return df
    
    def _calculate_rsi(self, df: pd.DataFrame, period: int = 14) -> Optional[float]:
        """Calculate Relative Strength Index"""
        if len(df) < period + 1:
            return None
        
        try:
            closes = df['close'].values
            deltas = np.diff(closes)
            
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            avg_gain = np.mean(gains[:period])
            avg_loss = np.mean(losses[:period])
            
            if avg_loss == 0:
                return 100.0
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return round(rsi, 2)
            
        except Exception as e:
            logger.error(f"Error calculating RSI: {str(e)}")
            return None
    
    def _calculate_macd(self, df: pd.DataFrame) -> Optional[Dict[str, float]]:
        """Calculate MACD (Moving Average Convergence Divergence)"""
        if len(df) < 26:
            return None
        
        try:
            closes = df['close'].values
            
            # Calculate EMAs
            ema_12 = self._calculate_ema(closes, 12)
            ema_26 = self._calculate_ema(closes, 26)
            
            if ema_12 is None or ema_26 is None:
                return None
            
            macd_line = ema_12 - ema_26
            signal_line = self._calculate_ema([macd_line], 9)
            
            if signal_line is None:
                signal_line = macd_line
            
            histogram = macd_line - signal_line
            
            return {
                "macd": round(macd_line, 4),
                "signal": round(signal_line, 4),
                "histogram": round(histogram, 4)
            }
            
        except Exception as e:
            logger.error(f"Error calculating MACD: {str(e)}")
            return None
    
    def _calculate_ema(self, values: List[float], period: int) -> Optional[float]:
        """Calculate Exponential Moving Average"""
        if len(values) < period:
            return None
        
        try:
            multiplier = 2 / (period + 1)
            ema = values[0]
            
            for value in values[1:]:
                ema = (value * multiplier) + (ema * (1 - multiplier))
            
            return ema
            
        except Exception as e:
            logger.error(f"Error calculating EMA: {str(e)}")
            return None
    
    def _calculate_moving_averages(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate various moving averages"""
        closes = df['close'].values
        
        mas = {}
        periods = [5, 10, 20, 50, 100, 200]
        
        for period in periods:
            if len(closes) >= period:
                ma = np.mean(closes[-period:])
                mas[f"ma_{period}"] = round(ma, 2)
        
        return mas
    
    def _calculate_bollinger_bands(self, df: pd.DataFrame, period: int = 20) -> Optional[Dict[str, float]]:
        """Calculate Bollinger Bands"""
        if len(df) < period:
            return None
        
        try:
            closes = df['close'].values[-period:]
            
            middle = np.mean(closes)
            std = np.std(closes)
            
            upper = middle + (2 * std)
            lower = middle - (2 * std)
            
            return {
                "upper": round(upper, 2),
                "middle": round(middle, 2),
                "lower": round(lower, 2),
                "bandwidth": round((upper - lower) / middle * 100, 2)
            }
            
        except Exception as e:
            logger.error(f"Error calculating Bollinger Bands: {str(e)}")
            return None
    
    def _analyze_volume(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Analyze volume patterns"""
        if 'volume' not in df.columns or df['volume'].sum() == 0:
            return {"status": "no_volume_data"}
        
        try:
            volumes = df['volume'].values
            
            avg_volume = np.mean(volumes)
            current_volume = volumes[-1] if len(volumes) > 0 else 0
            volume_ratio = current_volume / avg_volume if avg_volume > 0 else 0
            
            # Volume trend (last 5 periods)
            if len(volumes) >= 5:
                recent_avg = np.mean(volumes[-5:])
                earlier_avg = np.mean(volumes[-10:-5]) if len(volumes) >= 10 else avg_volume
                volume_trend = "increasing" if recent_avg > earlier_avg else "decreasing"
            else:
                volume_trend = "insufficient_data"
            
            return {
                "average_volume": round(avg_volume, 2),
                "current_volume": round(current_volume, 2),
                "volume_ratio": round(volume_ratio, 2),
                "volume_trend": volume_trend,
                "high_volume": volume_ratio > 1.5
            }
            
        except Exception as e:
            logger.error(f"Error analyzing volume: {str(e)}")
            return {"status": "error"}
    
    def _find_support_resistance(self, df: pd.DataFrame) -> Dict[str, List[float]]:
        """Find support and resistance levels"""
        try:
            highs = df['high'].values
            lows = df['low'].values
            
            # Simple approach: find local maxima and minima
            resistance_levels = []
            support_levels = []
            
            # Find peaks and troughs
            for i in range(1, len(highs) - 1):
                # Resistance (local maxima)
                if highs[i] > highs[i-1] and highs[i] > highs[i+1]:
                    resistance_levels.append(highs[i])
                
                # Support (local minima)
                if lows[i] < lows[i-1] and lows[i] < lows[i+1]:
                    support_levels.append(lows[i])
            
            # Sort and get most significant levels
            resistance_levels = sorted(set(resistance_levels), reverse=True)[:3]
            support_levels = sorted(set(support_levels))[:3]
            
            return {
                "resistance": [round(level, 2) for level in resistance_levels],
                "support": [round(level, 2) for level in support_levels]
            }
            
        except Exception as e:
            logger.error(f"Error finding support/resistance: {str(e)}")
            return {"resistance": [], "support": []}
