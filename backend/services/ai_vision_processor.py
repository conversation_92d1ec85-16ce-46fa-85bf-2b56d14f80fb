"""
Streamlined AI Vision Processor for Chart Analysis
Uses OpenAI GPT-4o Vision with enhanced context and robust error handling
"""

import base64
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import asyncio
import httpx
import os
from PIL import Image
import io

from models.chart_data import PriceData, PatternRecognition, ChartData

logger = logging.getLogger(__name__)

class AIVisionProcessor:
    """
    Streamlined AI Vision processor using OpenAI GPT-4o Vision with enhanced context
    """

    def __init__(self):
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.max_image_size = (1024, 1024)
        self.max_retries = 2
        self.retry_count = 0

        if not self.openai_api_key:
            logger.error("OpenAI API key not found. AI vision will not work.")
            raise ValueError("OpenAI API key is required for AI vision processing")

    async def analyze_chart(self, image_path: str, coin_symbol: str = "BTC",
                          timeframe: str = "1h", market_context: Optional[Dict] = None) -> Dict:
        """
        Main entry point for AI vision chart analysis with enhanced context
        """
        try:
            logger.info(f"Starting AI vision analysis for {coin_symbol} ({timeframe})")

            # Prepare image
            image_data = self._prepare_image(image_path)

            # Try primary analysis with full context
            result = await self._analyze_with_enhanced_context(
                image_data, coin_symbol, timeframe, market_context
            )

            if result.get("status") == "success":
                logger.info("✅ Primary AI vision analysis successful")
                return result

            # Retry with simplified prompt
            logger.warning("Primary analysis failed, retrying with simplified prompt...")
            result = await self._analyze_with_simplified_prompt(
                image_data, coin_symbol, timeframe
            )

            if result.get("status") == "success":
                logger.info("✅ Retry AI vision analysis successful")
                return result

            # Final fallback
            logger.warning("All AI vision attempts failed, using technical fallback")
            return self._create_technical_fallback(coin_symbol)

        except Exception as e:
            logger.error(f"Error in AI vision processing: {str(e)}")
            return self._create_technical_fallback(coin_symbol)

    async def process_chart_with_ai_vision(self, image_path: str, coin_symbol: str = "BTC", timeframe: str = "1h") -> ChartData:
        """
        Legacy method for backward compatibility
        """
        result = await self.analyze_chart(image_path, coin_symbol, timeframe)
        return self._convert_result_to_chart_data(result)
    
    def _prepare_image(self, image_path: str) -> str:
        """
        Prepare image for AI vision API
        """
        try:
            # Open and resize image if needed
            with Image.open(image_path) as img:
                # Convert to RGB if needed
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize if too large
                if img.size[0] > self.max_image_size[0] or img.size[1] > self.max_image_size[1]:
                    img.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)
                
                # Convert to base64
                buffer = io.BytesIO()
                img.save(buffer, format='PNG')
                image_bytes = buffer.getvalue()
                
                return base64.b64encode(image_bytes).decode('utf-8')
                
        except Exception as e:
            logger.error(f"Error preparing image: {str(e)}")
            raise
    
    async def _analyze_with_enhanced_context(self, image_data: str, coin_symbol: str,
                                           timeframe: str, market_context: Optional[Dict] = None) -> Dict:
        """
        Analyze chart using OpenAI with enhanced market context
        """
        # Build enhanced prompt with market context
        context_info = ""
        if market_context:
            context_info = f"""
CURRENT MARKET CONTEXT:
- Current Price: ${market_context.get('current_price', 'N/A'):,.2f}
- 24h Change: {market_context.get('price_change_24h', 'N/A'):+.2f}%
- 7d Change: {market_context.get('price_change_7d', 'N/A'):+.2f}%
- Market Phase: {market_context.get('market_phase', 'unknown').upper()}
- Trend Strength: {market_context.get('trend_strength', 'N/A'):.2f}
- 30d Volatility: {market_context.get('volatility_30d', 'N/A'):.2f}%

SUPPORT/RESISTANCE LEVELS:
- Support: {', '.join([f'${level:,.0f}' for level in market_context.get('support_levels', [])])}
- Resistance: {', '.join([f'${level:,.0f}' for level in market_context.get('resistance_levels', [])])}

HISTORICAL PATTERNS:
{self._format_historical_patterns(market_context.get('historical_patterns', []))}
"""

        prompt = f"""
Analyze this {coin_symbol} cryptocurrency chart ({timeframe} timeframe) with comprehensive market context.

{context_info}

CHART ANALYSIS INSTRUCTIONS:
1. Read the EXACT price values from the chart axes and candlestick positions
2. Extract OHLC data for at least 10-15 visible candlesticks
3. Identify current price and validate against market context
4. Analyze patterns considering historical context
5. Provide professional trading insights

Return EXACTLY this JSON format:
```json
{{
    "status": "success",
    "price_data": [
        {{"timestamp": "0", "open": 50000.0, "high": 50500.0, "low": 49800.0, "close": 50200.0, "volume": 1000}},
        {{"timestamp": "1", "open": 50200.0, "high": 50600.0, "low": 50000.0, "close": 50400.0, "volume": 1200}}
    ],
    "current_price": 50400.0,
    "price_range": {{"min": 49500.0, "max": 51000.0}},
    "candlestick_patterns": ["hammer", "doji"],
    "chart_patterns": ["ascending_triangle"],
    "trend_direction": "bullish",
    "trend_strength": 0.75,
    "technical_indicators": {{
        "rsi": 65.5,
        "moving_averages": {{"ma20": 50000.0, "ma50": 49500.0}},
        "support_levels": [49500.0, 50000.0],
        "resistance_levels": [51000.0, 51500.0]
    }},
    "analysis_confidence": 0.92,
    "notes": "Analysis considering market context and historical patterns"
}}
```

Focus on EXACT price reading and integrate the provided market context into your analysis.
        """

        return await self._call_openai_api(prompt, image_data)
    
    async def _analyze_with_simplified_prompt(self, image_data: str, coin_symbol: str, timeframe: str) -> Dict:
        """
        Analyze chart using simplified OpenAI prompt for retry scenarios
        """
        prompt = f"""
Analyze this {coin_symbol} chart ({timeframe}) and extract basic trading data.

Read the price values from the chart and return this JSON:
```json
{{
    "status": "success",
    "price_data": [
        {{"timestamp": "0", "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.0, "volume": 1000}}
    ],
    "current_price": 0.0,
    "trend_direction": "bullish/bearish/sideways",
    "trend_strength": 0.5,
    "analysis_confidence": 0.8,
    "notes": "Basic chart analysis"
}}
```

Focus on reading the actual price values from the chart axes.
        """

        return await self._call_openai_api(prompt, image_data)

    async def _call_openai_api(self, prompt: str, image_data: str) -> Dict:
        """
        Make API call to OpenAI with error handling
        """
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.openai_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": "gpt-4o",
                        "messages": [
                            {
                                "role": "user",
                                "content": [
                                    {"type": "text", "text": prompt},
                                    {
                                        "type": "image_url",
                                        "image_url": {
                                            "url": f"data:image/png;base64,{image_data}"
                                        }
                                    }
                                ]
                            }
                        ],
                        "max_tokens": 4000
                    }
                )

                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]

                    # Extract JSON from response
                    try:
                        if "```json" in content:
                            start_idx = content.find("```json") + 7
                            end_idx = content.find("```", start_idx)
                            json_str = content[start_idx:end_idx].strip()
                        else:
                            start_idx = content.find('{')
                            end_idx = content.rfind('}') + 1
                            json_str = content[start_idx:end_idx]

                        parsed_result = json.loads(json_str)
                        parsed_result["status"] = "success"
                        return parsed_result

                    except json.JSONDecodeError as e:
                        logger.error(f"Failed to parse JSON from OpenAI response: {e}")
                        return {"status": "error", "error": "Failed to parse response"}
                else:
                    logger.error(f"OpenAI API error: {response.status_code} - {response.text}")
                    return {"status": "error", "error": f"API error: {response.status_code}"}

        except Exception as e:
            logger.error(f"Error calling OpenAI API: {str(e)}")
            return {"status": "error", "error": str(e)}
    
    def _parse_ai_response(self, ai_result: Dict) -> ChartData:
        """
        Parse AI vision response into ChartData format
        """
        try:
            if "error" in ai_result:
                logger.error(f"AI analysis error: {ai_result['error']}")
                return self._create_fallback_data()
            
            # Extract price data
            price_data = []
            for i, price_point in enumerate(ai_result.get("price_data", [])):
                price_data.append(PriceData(
                    open=float(price_point.get("open", 0)),
                    high=float(price_point.get("high", 0)),
                    low=float(price_point.get("low", 0)),
                    close=float(price_point.get("close", 0)),
                    volume=int(price_point.get("volume", 1000))
                ))
            
            # Extract patterns
            patterns = PatternRecognition(
                candlestick_patterns=ai_result.get("candlestick_patterns", []),
                chart_patterns=ai_result.get("chart_patterns", []),
                trend_direction=ai_result.get("trend_direction", "sideways"),
                trend_strength=float(ai_result.get("trend_strength", 0.5))
            )
            
            # Create chart data
            chart_data = ChartData(
                extracted_prices=price_data,
                detected_patterns=patterns,
                chart_metadata={
                    "ai_analysis": True,
                    "current_price": ai_result.get("current_price"),
                    "price_range": ai_result.get("price_range"),
                    "technical_indicators": ai_result.get("technical_indicators", {}),
                    "analysis_confidence": ai_result.get("analysis_confidence", 0.8),
                    "notes": ai_result.get("notes", "")
                },
                processing_status="ai_vision_success"
            )
            
            logger.info(f"AI vision analysis successful: {len(price_data)} price points extracted")
            return chart_data
            
        except Exception as e:
            logger.error(f"Error parsing AI response: {str(e)}")
            return self._create_fallback_data()
    
    def _format_historical_patterns(self, patterns) -> str:
        """Format historical patterns for AI prompt"""
        if not patterns:
            return "- No significant historical patterns found"

        formatted = []
        for i, pattern in enumerate(patterns[:3], 1):
            # Handle both HistoricalPattern objects and dictionaries
            if hasattr(pattern, 'date'):  # HistoricalPattern object
                formatted.append(
                    f"- Pattern {i}: {pattern.date} "
                    f"(Similarity: {pattern.similarity_score:.2f}) "
                    f"→ {pattern.outcome.upper()} "
                    f"(7d: {pattern.price_change_7d:+.1f}%)"
                )
            else:  # Dictionary
                formatted.append(
                    f"- Pattern {i}: {pattern.get('date', 'N/A')} "
                    f"(Similarity: {pattern.get('similarity_score', 0):.2f}) "
                    f"→ {pattern.get('outcome', 'unknown').upper()} "
                    f"(7d: {pattern.get('price_change_7d', 0):+.1f}%)"
                )

        return '\n'.join(formatted)

    def _create_technical_fallback(self, coin_symbol: str) -> Dict:
        """
        Create technical analysis fallback when AI vision completely fails
        """
        return {
            "status": "fallback",
            "price_data": [],
            "current_price": 0.0,
            "trend_direction": "sideways",
            "trend_strength": 0.5,
            "analysis_confidence": 0.3,
            "notes": f"AI vision failed, using technical analysis fallback for {coin_symbol}",
            "processing_method": "technical_fallback"
        }

    def _convert_result_to_chart_data(self, result: Dict) -> ChartData:
        """
        Convert new result format to legacy ChartData format
        """
        try:
            if result.get("status") != "success":
                return self._create_fallback_chart_data()

            # Extract price data
            price_data = []
            for price_point in result.get("price_data", []):
                price_data.append(PriceData(
                    open=float(price_point.get("open", 0)),
                    high=float(price_point.get("high", 0)),
                    low=float(price_point.get("low", 0)),
                    close=float(price_point.get("close", 0)),
                    volume=int(price_point.get("volume", 1000))
                ))

            # Extract patterns
            patterns = PatternRecognition(
                candlestick_patterns=result.get("candlestick_patterns", []),
                chart_patterns=result.get("chart_patterns", []),
                trend_direction=result.get("trend_direction", "sideways"),
                trend_strength=float(result.get("trend_strength", 0.5))
            )

            # Create chart data
            chart_data = ChartData(
                extracted_prices=price_data,
                detected_patterns=patterns,
                chart_metadata={
                    "ai_analysis": True,
                    "current_price": result.get("current_price"),
                    "price_range": result.get("price_range"),
                    "technical_indicators": result.get("technical_indicators", {}),
                    "analysis_confidence": result.get("analysis_confidence", 0.8),
                    "notes": result.get("notes", ""),
                    "processing_method": result.get("processing_method", "openai_vision")
                },
                processing_status="ai_vision_success"
            )

            return chart_data

        except Exception as e:
            logger.error(f"Error converting result to chart data: {str(e)}")
            return self._create_fallback_chart_data()

    def _create_fallback_chart_data(self) -> ChartData:
        """
        Create fallback ChartData when AI vision fails
        """
        return ChartData(
            extracted_prices=[],
            detected_patterns=PatternRecognition(),
            chart_metadata={"ai_analysis": False, "processing_method": "fallback"},
            processing_status="ai_vision_failed"
        )
