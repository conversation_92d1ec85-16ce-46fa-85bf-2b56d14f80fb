"""
AI Vision Processor using Claude and OpenAI for accurate chart reading
"""

import base64
import json
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path
import asyncio
import httpx
import os
from PIL import Image
import io

from models.chart_data import PriceData, PatternRecognition, ChartData

logger = logging.getLogger(__name__)

class AIVisionProcessor:
    def __init__(self):
        self.claude_api_key = os.getenv("ANTHROPIC_API_KEY")
        self.openai_api_key = os.getenv("OPENAI_API_KEY")
        self.max_image_size = (1024, 1024)  # Resize for API limits
        
    async def process_chart_with_ai_vision(self, image_path: str, coin_symbol: str = "BTC", timeframe: str = "1h") -> ChartData:
        """
        Process chart using AI vision models (Claude or OpenAI)
        """
        try:
            # Prepare image
            image_data = self._prepare_image(image_path)
            
            # Try OpenAI first (since we have a valid key), then <PERSON>
            if self.openai_api_key:
                logger.info("Using OpenAI Vision for chart analysis")
                result = await self._analyze_with_openai(image_data, coin_symbol, timeframe)
                if "error" not in result:
                    return self._parse_ai_response(result)
                else:
                    logger.warning(f"OpenAI failed: {result.get('error')}, trying Claude...")

            if self.claude_api_key and self.claude_api_key != "your_claude_api_key_here":
                logger.info("Using Claude Vision for chart analysis")
                result = await self._analyze_with_claude(image_data, coin_symbol, timeframe)
                if "error" not in result:
                    return self._parse_ai_response(result)

            logger.warning("No valid AI vision API keys available, using fallback")
            return self._create_fallback_data()
            
            return self._parse_ai_response(result)
            
        except Exception as e:
            logger.error(f"Error in AI vision processing: {str(e)}")
            return self._create_fallback_data()
    
    def _prepare_image(self, image_path: str) -> str:
        """
        Prepare image for AI vision API
        """
        try:
            # Open and resize image if needed
            with Image.open(image_path) as img:
                # Convert to RGB if needed
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Resize if too large
                if img.size[0] > self.max_image_size[0] or img.size[1] > self.max_image_size[1]:
                    img.thumbnail(self.max_image_size, Image.Resampling.LANCZOS)
                
                # Convert to base64
                buffer = io.BytesIO()
                img.save(buffer, format='PNG')
                image_bytes = buffer.getvalue()
                
                return base64.b64encode(image_bytes).decode('utf-8')
                
        except Exception as e:
            logger.error(f"Error preparing image: {str(e)}")
            raise
    
    async def _analyze_with_claude(self, image_data: str, coin_symbol: str, timeframe: str) -> Dict:
        """
        Analyze chart using Claude Vision
        """
        prompt = f"""
        Analyze this cryptocurrency chart image for {coin_symbol} with {timeframe} timeframe. 

        Please extract the following information and return it as a JSON object:

        1. **Price Data**: Extract OHLC (Open, High, Low, Close) data for visible candlesticks/bars. Include at least 10-20 data points if visible.

        2. **Chart Patterns**: Identify any technical patterns like:
           - Candlestick patterns (doji, hammer, engulfing, shooting star, etc.)
           - Chart formations (triangles, channels, head and shoulders, etc.)
           - Trend direction and strength

        3. **Technical Indicators**: If visible on the chart, read values for:
           - Moving averages
           - RSI
           - MACD
           - Bollinger Bands
           - Volume data

        4. **Price Levels**: Identify key support and resistance levels

        5. **Current Market State**: 
           - Current price
           - Recent price movement
           - Overall trend direction

        Return the response in this exact JSON format:
        {{
            "price_data": [
                {{"timestamp": "index", "open": 0.0, "high": 0.0, "low": 0.0, "close": 0.0, "volume": 0}},
                ...
            ],
            "current_price": 0.0,
            "price_range": {{"min": 0.0, "max": 0.0}},
            "candlestick_patterns": ["pattern1", "pattern2"],
            "chart_patterns": ["pattern1", "pattern2"],
            "trend_direction": "bullish/bearish/sideways",
            "trend_strength": 0.0,
            "technical_indicators": {{
                "rsi": 0.0,
                "moving_averages": {{"ma20": 0.0, "ma50": 0.0}},
                "support_levels": [0.0, 0.0],
                "resistance_levels": [0.0, 0.0]
            }},
            "analysis_confidence": 0.0,
            "notes": "Additional observations about the chart"
        }}

        Be as accurate as possible in reading the actual price values from the chart axes and candlestick positions.
        """
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    "https://api.anthropic.com/v1/messages",
                    headers={
                        "Authorization": f"Bearer {self.claude_api_key}",
                        "Content-Type": "application/json",
                        "anthropic-version": "2023-06-01"
                    },
                    json={
                        "model": "claude-3-sonnet-20240229",
                        "max_tokens": 4000,
                        "messages": [
                            {
                                "role": "user",
                                "content": [
                                    {
                                        "type": "text",
                                        "text": prompt
                                    },
                                    {
                                        "type": "image",
                                        "source": {
                                            "type": "base64",
                                            "media_type": "image/png",
                                            "data": image_data
                                        }
                                    }
                                ]
                            }
                        ]
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["content"][0]["text"]
                    
                    # Extract JSON from response
                    try:
                        # Find JSON in the response
                        start_idx = content.find('{')
                        end_idx = content.rfind('}') + 1
                        json_str = content[start_idx:end_idx]
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        logger.error("Failed to parse JSON from Claude response")
                        return {"error": "Failed to parse response"}
                else:
                    logger.error(f"Claude API error: {response.status_code} - {response.text}")
                    return {"error": f"API error: {response.status_code}"}
                    
        except Exception as e:
            logger.error(f"Error calling Claude API: {str(e)}")
            return {"error": str(e)}
    
    async def _analyze_with_openai(self, image_data: str, coin_symbol: str, timeframe: str) -> Dict:
        """
        Analyze chart using OpenAI Vision
        """
        prompt = f"""
        Analyze this cryptocurrency chart for {coin_symbol} ({timeframe} timeframe) and extract detailed trading data.

        Extract and return the following as a JSON object:

        1. Price data from visible candlesticks (OHLC values)
        2. Current price and price range
        3. Candlestick patterns (doji, hammer, engulfing, etc.)
        4. Chart patterns (triangles, channels, trends)
        5. Technical indicators if visible (RSI, MACD, moving averages)
        6. Support and resistance levels
        7. Overall trend analysis

        Return in this JSON format:
        {{
            "price_data": [{{"open": 0, "high": 0, "low": 0, "close": 0, "volume": 0}}],
            "current_price": 0,
            "candlestick_patterns": [],
            "chart_patterns": [],
            "trend_direction": "bullish/bearish/sideways",
            "trend_strength": 0.0,
            "technical_indicators": {{}},
            "analysis_confidence": 0.0
        }}

        Be precise in reading actual price values from the chart.
        """
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    "https://api.openai.com/v1/chat/completions",
                    headers={
                        "Authorization": f"Bearer {self.openai_api_key}",
                        "Content-Type": "application/json"
                    },
                    json={
                        "model": "gpt-4-vision-preview",
                        "messages": [
                            {
                                "role": "user",
                                "content": [
                                    {"type": "text", "text": prompt},
                                    {
                                        "type": "image_url",
                                        "image_url": {
                                            "url": f"data:image/png;base64,{image_data}"
                                        }
                                    }
                                ]
                            }
                        ],
                        "max_tokens": 4000
                    }
                )
                
                if response.status_code == 200:
                    result = response.json()
                    content = result["choices"][0]["message"]["content"]
                    
                    # Extract JSON from response
                    try:
                        start_idx = content.find('{')
                        end_idx = content.rfind('}') + 1
                        json_str = content[start_idx:end_idx]
                        return json.loads(json_str)
                    except json.JSONDecodeError:
                        logger.error("Failed to parse JSON from OpenAI response")
                        return {"error": "Failed to parse response"}
                else:
                    logger.error(f"OpenAI API error: {response.status_code} - {response.text}")
                    return {"error": f"API error: {response.status_code}"}
                    
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {str(e)}")
            return {"error": str(e)}
    
    def _parse_ai_response(self, ai_result: Dict) -> ChartData:
        """
        Parse AI vision response into ChartData format
        """
        try:
            if "error" in ai_result:
                logger.error(f"AI analysis error: {ai_result['error']}")
                return self._create_fallback_data()
            
            # Extract price data
            price_data = []
            for i, price_point in enumerate(ai_result.get("price_data", [])):
                price_data.append(PriceData(
                    open=float(price_point.get("open", 0)),
                    high=float(price_point.get("high", 0)),
                    low=float(price_point.get("low", 0)),
                    close=float(price_point.get("close", 0)),
                    volume=int(price_point.get("volume", 1000))
                ))
            
            # Extract patterns
            patterns = PatternRecognition(
                candlestick_patterns=ai_result.get("candlestick_patterns", []),
                chart_patterns=ai_result.get("chart_patterns", []),
                trend_direction=ai_result.get("trend_direction", "sideways"),
                trend_strength=float(ai_result.get("trend_strength", 0.5))
            )
            
            # Create chart data
            chart_data = ChartData(
                extracted_prices=price_data,
                detected_patterns=patterns,
                chart_metadata={
                    "ai_analysis": True,
                    "current_price": ai_result.get("current_price"),
                    "price_range": ai_result.get("price_range"),
                    "technical_indicators": ai_result.get("technical_indicators", {}),
                    "analysis_confidence": ai_result.get("analysis_confidence", 0.8),
                    "notes": ai_result.get("notes", "")
                },
                processing_status="ai_vision_success"
            )
            
            logger.info(f"AI vision analysis successful: {len(price_data)} price points extracted")
            return chart_data
            
        except Exception as e:
            logger.error(f"Error parsing AI response: {str(e)}")
            return self._create_fallback_data()
    
    def _create_fallback_data(self) -> ChartData:
        """
        Create fallback data when AI vision fails
        """
        return ChartData(
            extracted_prices=[],
            detected_patterns=PatternRecognition(),
            chart_metadata={"ai_analysis": False},
            processing_status="ai_vision_failed"
        )
