"""
Binance API Data Provider
Fetches historical and real-time cryptocurrency data from Binance
"""

import asyncio
import httpx
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging
import time
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class BinanceKline:
    """Binance Kline (candlestick) data structure"""
    open_time: int
    open_price: float
    high_price: float
    low_price: float
    close_price: float
    volume: float
    close_time: int
    quote_asset_volume: float
    number_of_trades: int
    taker_buy_base_asset_volume: float
    taker_buy_quote_asset_volume: float
    
    @property
    def datetime(self) -> datetime:
        return datetime.fromtimestamp(self.open_time / 1000)
    
    @property
    def date_str(self) -> str:
        return self.datetime.strftime('%Y-%m-%d %H:%M:%S')

class BinanceDataProvider:
    """
    Binance API data provider for cryptocurrency historical and real-time data
    """
    
    def __init__(self):
        self.base_url = "https://api.binance.com"
        self.session = None
        
        # Interval mappings
        self.intervals = {
            "1m": "1m", "3m": "3m", "5m": "5m", "15m": "15m", "30m": "30m",
            "1h": "1h", "2h": "2h", "4h": "4h", "6h": "6h", "8h": "8h", "12h": "12h",
            "1d": "1d", "3d": "3d", "1w": "1w", "1M": "1M"
        }
        
    async def __aenter__(self):
        self.session = httpx.AsyncClient(timeout=30.0)
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.aclose()
    
    async def get_klines(self, symbol: str = "BTCUSDT", interval: str = "1d", 
                        limit: int = 1000, start_time: Optional[int] = None, 
                        end_time: Optional[int] = None) -> List[BinanceKline]:
        """
        Get historical kline/candlestick data from Binance
        
        Args:
            symbol: Trading pair symbol (e.g., BTCUSDT)
            interval: Kline interval (1m, 5m, 1h, 1d, etc.)
            limit: Number of klines to return (max 1000)
            start_time: Start time in milliseconds
            end_time: End time in milliseconds
        """
        try:
            if not self.session:
                self.session = httpx.AsyncClient(timeout=30.0)
            
            # Validate interval
            if interval not in self.intervals:
                raise ValueError(f"Invalid interval: {interval}")
            
            # Build parameters
            params = {
                "symbol": symbol.upper(),
                "interval": interval,
                "limit": min(limit, 1000)  # Binance max is 1000
            }
            
            if start_time:
                params["startTime"] = start_time
            if end_time:
                params["endTime"] = end_time
            
            # Make API request
            url = f"{self.base_url}/api/v3/klines"
            logger.info(f"Fetching Binance data: {symbol} {interval} (limit: {limit})")
            
            response = await self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            
            # Parse klines
            klines = []
            for kline_data in data:
                kline = BinanceKline(
                    open_time=int(kline_data[0]),
                    open_price=float(kline_data[1]),
                    high_price=float(kline_data[2]),
                    low_price=float(kline_data[3]),
                    close_price=float(kline_data[4]),
                    volume=float(kline_data[5]),
                    close_time=int(kline_data[6]),
                    quote_asset_volume=float(kline_data[7]),
                    number_of_trades=int(kline_data[8]),
                    taker_buy_base_asset_volume=float(kline_data[9]),
                    taker_buy_quote_asset_volume=float(kline_data[10])
                )
                klines.append(kline)
            
            logger.info(f"Successfully fetched {len(klines)} klines for {symbol}")
            return klines
            
        except httpx.HTTPStatusError as e:
            logger.error(f"Binance API HTTP error: {e.response.status_code} - {e.response.text}")
            raise
        except Exception as e:
            logger.error(f"Error fetching Binance data: {str(e)}")
            raise
    
    async def get_historical_data_range(self, symbol: str = "BTCUSDT", 
                                      interval: str = "1d", 
                                      start_date: str = "2017-01-01",
                                      end_date: Optional[str] = None) -> List[BinanceKline]:
        """
        Get historical data for a specific date range (handles pagination)
        
        Args:
            symbol: Trading pair symbol
            interval: Kline interval
            start_date: Start date in YYYY-MM-DD format
            end_date: End date in YYYY-MM-DD format (default: today)
        """
        try:
            # Parse dates
            start_dt = datetime.strptime(start_date, "%Y-%m-%d")
            end_dt = datetime.strptime(end_date, "%Y-%m-%d") if end_date else datetime.now()
            
            start_timestamp = int(start_dt.timestamp() * 1000)
            end_timestamp = int(end_dt.timestamp() * 1000)
            
            all_klines = []
            current_start = start_timestamp
            
            logger.info(f"Fetching historical data from {start_date} to {end_date or 'today'}")
            
            while current_start < end_timestamp:
                # Calculate end time for this batch (1000 intervals max)
                if interval == "1d":
                    batch_end = min(current_start + (1000 * 24 * 60 * 60 * 1000), end_timestamp)
                elif interval == "1h":
                    batch_end = min(current_start + (1000 * 60 * 60 * 1000), end_timestamp)
                else:
                    # For other intervals, calculate appropriately
                    batch_end = min(current_start + (1000 * 60 * 1000), end_timestamp)
                
                # Fetch batch
                batch_klines = await self.get_klines(
                    symbol=symbol,
                    interval=interval,
                    limit=1000,
                    start_time=current_start,
                    end_time=batch_end
                )
                
                if not batch_klines:
                    break
                
                all_klines.extend(batch_klines)
                
                # Update start time for next batch
                current_start = batch_klines[-1].close_time + 1
                
                # Rate limiting - be nice to Binance
                await asyncio.sleep(0.1)
            
            logger.info(f"Fetched total of {len(all_klines)} klines")
            return all_klines
            
        except Exception as e:
            logger.error(f"Error fetching historical data range: {str(e)}")
            raise
    
    async def get_current_price(self, symbol: str = "BTCUSDT") -> Dict:
        """Get current price information"""
        try:
            if not self.session:
                self.session = httpx.AsyncClient(timeout=30.0)
            
            url = f"{self.base_url}/api/v3/ticker/price"
            params = {"symbol": symbol.upper()}
            
            response = await self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            return {
                "symbol": data["symbol"],
                "price": float(data["price"]),
                "timestamp": int(time.time() * 1000)
            }
            
        except Exception as e:
            logger.error(f"Error fetching current price: {str(e)}")
            raise
    
    async def get_24hr_ticker(self, symbol: str = "BTCUSDT") -> Dict:
        """Get 24hr ticker statistics"""
        try:
            if not self.session:
                self.session = httpx.AsyncClient(timeout=30.0)
            
            url = f"{self.base_url}/api/v3/ticker/24hr"
            params = {"symbol": symbol.upper()}
            
            response = await self.session.get(url, params=params)
            response.raise_for_status()
            
            data = response.json()
            return {
                "symbol": data["symbol"],
                "price_change": float(data["priceChange"]),
                "price_change_percent": float(data["priceChangePercent"]),
                "weighted_avg_price": float(data["weightedAvgPrice"]),
                "prev_close_price": float(data["prevClosePrice"]),
                "last_price": float(data["lastPrice"]),
                "bid_price": float(data["bidPrice"]),
                "ask_price": float(data["askPrice"]),
                "open_price": float(data["openPrice"]),
                "high_price": float(data["highPrice"]),
                "low_price": float(data["lowPrice"]),
                "volume": float(data["volume"]),
                "quote_volume": float(data["quoteVolume"]),
                "open_time": int(data["openTime"]),
                "close_time": int(data["closeTime"]),
                "count": int(data["count"])
            }
            
        except Exception as e:
            logger.error(f"Error fetching 24hr ticker: {str(e)}")
            raise
    
    def klines_to_dataframe(self, klines: List[BinanceKline]) -> pd.DataFrame:
        """Convert klines to pandas DataFrame for analysis"""
        data = []
        for kline in klines:
            data.append({
                "datetime": kline.datetime,
                "open": kline.open_price,
                "high": kline.high_price,
                "low": kline.low_price,
                "close": kline.close_price,
                "volume": kline.volume,
                "quote_volume": kline.quote_asset_volume,
                "trades": kline.number_of_trades
            })
        
        df = pd.DataFrame(data)
        df.set_index("datetime", inplace=True)
        return df
