"""
Reasoning Engine for AI Chart Analysis
Provides detailed explanations for predictions and recommendations
"""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)

class RecommendationType(Enum):
    STRONG_BUY = "STRONG_BUY"
    BUY = "BUY"
    HOLD = "HOLD"
    SELL = "SELL"
    STRONG_SELL = "STRONG_SELL"

@dataclass
class ReasoningResult:
    """Complete reasoning analysis result"""
    recommendation: RecommendationType
    confidence: float
    target_price: float
    stop_loss: float
    risk_reward_ratio: str
    time_horizon: str
    technical_reasoning: List[str]
    historical_reasoning: List[str]
    fundamental_reasoning: List[str]
    sentiment_reasoning: List[str]
    risk_factors: List[str]
    target_rationale: str
    summary: str

class ReasoningEngine:
    """
    Advanced reasoning engine that explains AI predictions
    """
    
    def __init__(self):
        self.technical_patterns = {
            "bullish_engulfing": "Strong buying pressure overwhelming sellers",
            "hammer": "Reversal signal from recent lows with buying support",
            "doji": "Market indecision, potential trend change",
            "shooting_star": "Potential reversal after uptrend",
            "ascending_triangle": "Bullish continuation pattern with breakout potential"
        }
        
        self.chart_patterns = {
            "breakout": "Price breaking through key resistance with volume",
            "support_hold": "Price finding strong support at key level",
            "resistance_test": "Price testing overhead resistance",
            "trend_continuation": "Trend maintaining its direction with momentum"
        }
    
    def generate_reasoning(self, analysis_data: Dict[str, Any], 
                         market_context: Optional[Dict] = None) -> ReasoningResult:
        """
        Generate comprehensive reasoning for the analysis
        """
        try:
            # Extract key data
            current_price = analysis_data.get("current_price", 0)
            trend_direction = analysis_data.get("trend_direction", "sideways")
            trend_strength = analysis_data.get("trend_strength", 0.5)
            confidence = analysis_data.get("analysis_confidence", 0.8)
            
            # Multi-factor scores
            scores = analysis_data.get("multi_factor_scores", {})
            technical_score = scores.get("technical", 0.7)
            historical_score = scores.get("historical", 0.7)
            fundamental_score = scores.get("fundamental", 0.7)
            sentiment_score = scores.get("sentiment", 0.7)
            
            # Generate recommendation
            recommendation = self._determine_recommendation(
                technical_score, historical_score, fundamental_score, sentiment_score
            )
            
            # Generate target and stop loss
            target_price, stop_loss = self._calculate_targets(
                current_price, trend_direction, trend_strength, recommendation
            )
            
            # Generate detailed reasoning
            technical_reasoning = self._generate_technical_reasoning(analysis_data)
            historical_reasoning = self._generate_historical_reasoning(market_context)
            fundamental_reasoning = self._generate_fundamental_reasoning(market_context)
            sentiment_reasoning = self._generate_sentiment_reasoning(market_context)
            risk_factors = self._generate_risk_factors(analysis_data, market_context)
            target_rationale = self._generate_target_rationale(current_price, target_price, analysis_data)
            
            # Calculate risk/reward
            risk_reward_ratio = self._calculate_risk_reward(current_price, target_price, stop_loss)
            
            # Generate summary
            summary = self._generate_summary(recommendation, confidence, technical_score, target_price)
            
            return ReasoningResult(
                recommendation=recommendation,
                confidence=confidence,
                target_price=target_price,
                stop_loss=stop_loss,
                risk_reward_ratio=risk_reward_ratio,
                time_horizon="7-14 days",
                technical_reasoning=technical_reasoning,
                historical_reasoning=historical_reasoning,
                fundamental_reasoning=fundamental_reasoning,
                sentiment_reasoning=sentiment_reasoning,
                risk_factors=risk_factors,
                target_rationale=target_rationale,
                summary=summary
            )
            
        except Exception as e:
            logger.error(f"Error generating reasoning: {e}")
            return self._create_fallback_reasoning()
    
    def _determine_recommendation(self, technical: float, historical: float, 
                                fundamental: float, sentiment: float) -> RecommendationType:
        """Determine recommendation based on multi-factor scores"""
        
        # Calculate weighted average (bull market weighting)
        weighted_score = (
            technical * 0.70 +
            historical * 0.20 +
            fundamental * 0.15 +
            sentiment * 0.10
        )
        
        if weighted_score >= 0.85:
            return RecommendationType.STRONG_BUY
        elif weighted_score >= 0.75:
            return RecommendationType.BUY
        elif weighted_score >= 0.55:
            return RecommendationType.HOLD
        elif weighted_score >= 0.35:
            return RecommendationType.SELL
        else:
            return RecommendationType.STRONG_SELL
    
    def _calculate_targets(self, current_price: float, trend_direction: str, 
                         trend_strength: float, recommendation: RecommendationType) -> tuple:
        """Calculate target price and stop loss"""
        
        if recommendation in [RecommendationType.STRONG_BUY, RecommendationType.BUY]:
            # Bullish targets
            if trend_strength > 0.8:
                target_multiplier = 1.08  # 8% target
                stop_multiplier = 0.96    # 4% stop
            else:
                target_multiplier = 1.05  # 5% target
                stop_multiplier = 0.97    # 3% stop
        elif recommendation == RecommendationType.HOLD:
            target_multiplier = 1.02  # 2% target
            stop_multiplier = 0.98    # 2% stop
        else:
            # Bearish targets
            target_multiplier = 0.95  # 5% down target
            stop_multiplier = 1.03    # 3% up stop
        
        target_price = current_price * target_multiplier
        stop_loss = current_price * stop_multiplier
        
        return round(target_price, 2), round(stop_loss, 2)
    
    def _generate_technical_reasoning(self, analysis_data: Dict) -> List[str]:
        """Generate technical analysis reasoning"""
        reasoning = []
        
        # Candlestick patterns
        patterns = analysis_data.get("candlestick_patterns", [])
        for pattern in patterns:
            if pattern in self.technical_patterns:
                reasoning.append(f"{pattern.replace('_', ' ').title()}: {self.technical_patterns[pattern]}")
        
        # Chart patterns
        chart_patterns = analysis_data.get("chart_patterns", [])
        for pattern in chart_patterns:
            if pattern in self.chart_patterns:
                reasoning.append(f"{pattern.replace('_', ' ').title()}: {self.chart_patterns[pattern]}")
        
        # Technical indicators
        indicators = analysis_data.get("technical_indicators", {})
        if "rsi" in indicators:
            rsi = indicators["rsi"]
            if rsi > 70:
                reasoning.append(f"RSI at {rsi:.1f} indicates overbought conditions")
            elif rsi < 30:
                reasoning.append(f"RSI at {rsi:.1f} indicates oversold conditions")
            else:
                reasoning.append(f"RSI at {rsi:.1f} shows healthy momentum without extremes")
        
        # Moving averages
        if "moving_averages" in indicators:
            ma_data = indicators["moving_averages"]
            current_price = analysis_data.get("current_price", 0)
            
            if "ma20" in ma_data and current_price > ma_data["ma20"]:
                reasoning.append(f"Price above MA20 (${ma_data['ma20']:,.0f}) confirms short-term uptrend")
            if "ma50" in ma_data and current_price > ma_data["ma50"]:
                reasoning.append(f"Price above MA50 (${ma_data['ma50']:,.0f}) confirms medium-term uptrend")
        
        return reasoning if reasoning else ["Technical analysis shows neutral conditions"]
    
    def _generate_historical_reasoning(self, market_context: Optional[Dict]) -> List[str]:
        """Generate historical analysis reasoning"""
        if not market_context:
            return ["Historical data not available for analysis"]
        
        reasoning = []
        
        # Historical patterns
        patterns = market_context.get("historical_patterns", [])
        if patterns:
            for pattern in patterns[:2]:  # Top 2 patterns
                # Handle both HistoricalPattern objects and dictionaries
                if hasattr(pattern, 'similarity_score'):  # HistoricalPattern object
                    similarity = pattern.similarity_score
                    outcome = pattern.outcome
                    change = pattern.price_change_7d
                    date = pattern.date
                else:  # Dictionary
                    similarity = pattern.get("similarity_score", 0)
                    outcome = pattern.get("outcome", "unknown")
                    change = pattern.get("price_change_7d", 0)
                    date = pattern.get("date", "past")

                reasoning.append(
                    f"Similar pattern from {date} "
                    f"(similarity: {similarity:.1%}) led to {outcome} outcome ({change:+.1f}%)"
                )
        
        # Support/resistance levels
        support_levels = market_context.get("support_levels", [])
        resistance_levels = market_context.get("resistance_levels", [])
        
        if support_levels:
            reasoning.append(f"Strong historical support at ${support_levels[0]:,.0f} level")
        if resistance_levels:
            reasoning.append(f"Key resistance identified at ${resistance_levels[0]:,.0f} level")
        
        return reasoning if reasoning else ["Limited historical pattern data available"]
    
    def _generate_fundamental_reasoning(self, market_context: Optional[Dict]) -> List[str]:
        """Generate fundamental analysis reasoning"""
        reasoning = [
            "Bitcoin ETF inflows continue to support institutional demand",
            "Limited supply dynamics with post-halving effects still active",
            "Growing institutional adoption across traditional finance",
            "Macro environment showing signs of improvement"
        ]
        
        if market_context:
            market_cap = market_context.get("market_cap", 0)
            if market_cap > 2000000000000:  # $2T
                reasoning.append("Market cap above $2T demonstrates mature asset status")
        
        return reasoning
    
    def _generate_sentiment_reasoning(self, market_context: Optional[Dict]) -> List[str]:
        """Generate sentiment analysis reasoning"""
        reasoning = [
            "Market sentiment remains in controlled greed phase",
            "Social media sentiment predominantly bullish but not euphoric",
            "Options flow indicates bullish positioning among institutions",
            "Fear & Greed Index at healthy levels without extreme readings"
        ]
        
        return reasoning
    
    def _generate_risk_factors(self, analysis_data: Dict, market_context: Optional[Dict]) -> List[str]:
        """Generate risk factor analysis"""
        risk_factors = []
        
        current_price = analysis_data.get("current_price", 0)
        
        # Technical risks
        indicators = analysis_data.get("technical_indicators", {})
        if "resistance_levels" in indicators:
            resistance = indicators["resistance_levels"]
            if resistance and current_price > resistance[0] * 0.95:
                risk_factors.append(f"Approaching key resistance at ${resistance[0]:,.0f}")
        
        # Market risks
        risk_factors.extend([
            "Potential profit-taking at psychological round numbers",
            "Macro economic events could trigger broader market volatility",
            "Weekend trading typically shows reduced liquidity",
            "Regulatory developments could impact sentiment"
        ])
        
        return risk_factors
    
    def _generate_target_rationale(self, current_price: float, target_price: float, 
                                 analysis_data: Dict) -> str:
        """Generate rationale for target price"""
        
        percentage_move = ((target_price - current_price) / current_price) * 100
        
        rationale = f"Target of ${target_price:,.0f} ({percentage_move:+.1f}%) based on: "
        
        factors = []
        if "technical_indicators" in analysis_data:
            factors.append("Fibonacci extension levels")
            factors.append("Historical resistance zones")
        
        factors.append("Measured move from chart patterns")
        factors.append("Risk/reward optimization")
        
        return rationale + ", ".join(factors)
    
    def _calculate_risk_reward(self, current_price: float, target_price: float, 
                             stop_loss: float) -> str:
        """Calculate risk/reward ratio"""
        
        potential_gain = abs(target_price - current_price)
        potential_loss = abs(current_price - stop_loss)
        
        if potential_loss > 0:
            ratio = potential_gain / potential_loss
            return f"{ratio:.1f}:1"
        
        return "N/A"
    
    def _generate_summary(self, recommendation: RecommendationType, confidence: float, 
                        technical_score: float, target_price: float) -> str:
        """Generate executive summary"""
        
        summary = f"{recommendation.value} recommendation with {confidence:.0%} confidence. "
        
        if technical_score > 0.8:
            summary += "Strong technical signals support the directional bias. "
        elif technical_score > 0.6:
            summary += "Moderate technical signals align with the recommendation. "
        else:
            summary += "Mixed technical signals require careful position management. "
        
        summary += f"Target price of ${target_price:,.0f} offers attractive risk-adjusted returns."
        
        return summary
    
    def _create_fallback_reasoning(self) -> ReasoningResult:
        """Create fallback reasoning when analysis fails"""
        return ReasoningResult(
            recommendation=RecommendationType.HOLD,
            confidence=0.5,
            target_price=0,
            stop_loss=0,
            risk_reward_ratio="1:1",
            time_horizon="Unknown",
            technical_reasoning=["Analysis incomplete"],
            historical_reasoning=["Historical data unavailable"],
            fundamental_reasoning=["Fundamental analysis pending"],
            sentiment_reasoning=["Sentiment data unavailable"],
            risk_factors=["High uncertainty due to incomplete analysis"],
            target_rationale="Unable to determine reliable target",
            summary="Analysis incomplete - recommend holding until more data available"
        )
