"""
Historical Data Service
Integrates Binance API with AI Chart Analysis for enhanced predictions
"""

import asyncio
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging
import numpy as np
from dataclasses import dataclass

from .binance_data_provider import BinanceDataProvider, BinanceKline

logger = logging.getLogger(__name__)

@dataclass
class HistoricalPattern:
    """Historical pattern match result"""
    date: str
    similarity_score: float
    pattern_type: str
    outcome: str
    price_change_7d: float
    price_change_30d: float
    volume_ratio: float
    confidence: float

@dataclass
class MarketContext:
    """Market context for enhanced AI analysis"""
    current_price: float
    price_change_24h: float
    price_change_7d: float
    price_change_30d: float
    volume_24h: float
    volume_avg_30d: float
    volatility_30d: float
    support_levels: List[float]
    resistance_levels: List[float]
    historical_patterns: List[HistoricalPattern]
    market_phase: str  # bull, bear, sideways
    trend_strength: float

class HistoricalDataService:
    """
    Service for fetching and analyzing historical cryptocurrency data
    Enhances AI chart analysis with historical context
    """
    
    def __init__(self):
        self.binance = None
        self.cache = {}  # Simple in-memory cache
        
    async def __aenter__(self):
        self.binance = BinanceDataProvider()
        await self.binance.__aenter__()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.binance:
            await self.binance.__aexit__(exc_type, exc_val, exc_tb)
    
    async def get_market_context(self, symbol: str = "BTCUSDT", 
                               timeframe: str = "1d") -> MarketContext:
        """
        Get comprehensive market context for AI analysis
        """
        try:
            logger.info(f"Building market context for {symbol}")
            
            # Get current market data
            current_ticker = await self.binance.get_24hr_ticker(symbol)
            
            # Get historical data for analysis
            historical_data = await self.binance.get_klines(
                symbol=symbol,
                interval=timeframe,
                limit=365  # 1 year of daily data
            )
            
            # Convert to DataFrame for analysis
            df = self.binance.klines_to_dataframe(historical_data)
            
            # Calculate market metrics
            current_price = current_ticker['last_price']
            price_change_24h = current_ticker['price_change_percent']
            
            # Calculate longer-term changes
            price_change_7d = self._calculate_price_change(df, 7)
            price_change_30d = self._calculate_price_change(df, 30)
            
            # Volume analysis
            volume_24h = current_ticker['volume']
            volume_avg_30d = df['volume'].tail(30).mean()
            
            # Volatility calculation
            volatility_30d = self._calculate_volatility(df, 30)
            
            # Support and resistance levels
            support_levels, resistance_levels = self._find_support_resistance(df)
            
            # Find historical patterns
            historical_patterns = await self._find_historical_patterns(df, symbol)
            
            # Determine market phase
            market_phase, trend_strength = self._determine_market_phase(df)
            
            context = MarketContext(
                current_price=current_price,
                price_change_24h=price_change_24h,
                price_change_7d=price_change_7d,
                price_change_30d=price_change_30d,
                volume_24h=volume_24h,
                volume_avg_30d=volume_avg_30d,
                volatility_30d=volatility_30d,
                support_levels=support_levels,
                resistance_levels=resistance_levels,
                historical_patterns=historical_patterns,
                market_phase=market_phase,
                trend_strength=trend_strength
            )
            
            logger.info(f"Market context built successfully for {symbol}")
            return context
            
        except Exception as e:
            logger.error(f"Error building market context: {str(e)}")
            raise
    
    async def get_lifetime_data(self, symbol: str = "BTCUSDT", 
                              interval: str = "1d") -> pd.DataFrame:
        """
        Get lifetime historical data for Bitcoin (or other crypto)
        """
        try:
            logger.info(f"Fetching lifetime data for {symbol}")
            
            # For Bitcoin, start from 2017 when Binance data is reliable
            start_date = "2017-01-01"
            end_date = datetime.now().strftime("%Y-%m-%d")
            
            # Get all historical data
            historical_klines = await self.binance.get_historical_data_range(
                symbol=symbol,
                interval=interval,
                start_date=start_date,
                end_date=end_date
            )
            
            # Convert to DataFrame
            df = self.binance.klines_to_dataframe(historical_klines)
            
            logger.info(f"Fetched {len(df)} data points from {start_date} to {end_date}")
            return df
            
        except Exception as e:
            logger.error(f"Error fetching lifetime data: {str(e)}")
            raise
    
    def _calculate_price_change(self, df: pd.DataFrame, days: int) -> float:
        """Calculate price change over specified days"""
        if len(df) < days:
            return 0.0
        
        current_price = df['close'].iloc[-1]
        past_price = df['close'].iloc[-days]
        return ((current_price - past_price) / past_price) * 100
    
    def _calculate_volatility(self, df: pd.DataFrame, days: int) -> float:
        """Calculate price volatility over specified days"""
        if len(df) < days:
            return 0.0
        
        recent_data = df['close'].tail(days)
        daily_returns = recent_data.pct_change().dropna()
        return daily_returns.std() * np.sqrt(365) * 100  # Annualized volatility
    
    def _find_support_resistance(self, df: pd.DataFrame, 
                                lookback: int = 50) -> Tuple[List[float], List[float]]:
        """Find support and resistance levels"""
        try:
            recent_data = df.tail(lookback)
            
            # Find local minima (support) and maxima (resistance)
            highs = recent_data['high'].values
            lows = recent_data['low'].values
            
            # Simple approach: find significant levels
            support_levels = []
            resistance_levels = []
            
            # Find support levels (local minima)
            for i in range(2, len(lows) - 2):
                if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and 
                    lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                    support_levels.append(float(lows[i]))
            
            # Find resistance levels (local maxima)
            for i in range(2, len(highs) - 2):
                if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and 
                    highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                    resistance_levels.append(float(highs[i]))
            
            # Sort and return most significant levels
            support_levels = sorted(support_levels, reverse=True)[:3]
            resistance_levels = sorted(resistance_levels)[:3]
            
            return support_levels, resistance_levels
            
        except Exception as e:
            logger.error(f"Error finding support/resistance: {str(e)}")
            return [], []
    
    async def _find_historical_patterns(self, df: pd.DataFrame, 
                                      symbol: str) -> List[HistoricalPattern]:
        """Find similar historical patterns"""
        try:
            patterns = []
            
            # Simple pattern matching based on recent price action
            recent_returns = df['close'].pct_change().tail(10).values
            
            # Look for similar patterns in historical data
            for i in range(20, len(df) - 30):  # Leave room for outcome analysis
                historical_returns = df['close'].pct_change().iloc[i-10:i].values
                
                # Calculate similarity (correlation)
                if len(historical_returns) == len(recent_returns):
                    correlation = np.corrcoef(recent_returns, historical_returns)[0, 1]
                    
                    if correlation > 0.7:  # High similarity threshold
                        # Analyze outcome
                        start_price = df['close'].iloc[i]
                        price_7d = df['close'].iloc[min(i+7, len(df)-1)]
                        price_30d = df['close'].iloc[min(i+30, len(df)-1)]
                        
                        change_7d = ((price_7d - start_price) / start_price) * 100
                        change_30d = ((price_30d - start_price) / start_price) * 100
                        
                        # Determine outcome
                        if change_7d > 5:
                            outcome = "bullish"
                        elif change_7d < -5:
                            outcome = "bearish"
                        else:
                            outcome = "sideways"
                        
                        pattern = HistoricalPattern(
                            date=df.index[i].strftime('%Y-%m-%d'),
                            similarity_score=correlation,
                            pattern_type="price_action",
                            outcome=outcome,
                            price_change_7d=change_7d,
                            price_change_30d=change_30d,
                            volume_ratio=df['volume'].iloc[i] / df['volume'].iloc[i-10:i].mean(),
                            confidence=correlation
                        )
                        patterns.append(pattern)
            
            # Sort by similarity and return top matches
            patterns.sort(key=lambda x: x.similarity_score, reverse=True)
            return patterns[:5]  # Top 5 matches
            
        except Exception as e:
            logger.error(f"Error finding historical patterns: {str(e)}")
            return []
    
    def _determine_market_phase(self, df: pd.DataFrame) -> Tuple[str, float]:
        """Determine current market phase and trend strength"""
        try:
            # Calculate moving averages
            df_copy = df.copy()
            df_copy['ma_20'] = df_copy['close'].rolling(20).mean()
            df_copy['ma_50'] = df_copy['close'].rolling(50).mean()
            df_copy['ma_200'] = df_copy['close'].rolling(200).mean()
            
            current_price = df_copy['close'].iloc[-1]
            ma_20 = df_copy['ma_20'].iloc[-1]
            ma_50 = df_copy['ma_50'].iloc[-1]
            ma_200 = df_copy['ma_200'].iloc[-1]
            
            # Determine trend
            if current_price > ma_20 > ma_50 > ma_200:
                market_phase = "bull"
                trend_strength = 0.8
            elif current_price < ma_20 < ma_50 < ma_200:
                market_phase = "bear"
                trend_strength = 0.8
            else:
                market_phase = "sideways"
                trend_strength = 0.4
            
            # Calculate trend strength based on price momentum
            price_momentum = df_copy['close'].pct_change(20).iloc[-1]
            trend_strength = min(abs(price_momentum) * 10, 1.0)
            
            return market_phase, trend_strength
            
        except Exception as e:
            logger.error(f"Error determining market phase: {str(e)}")
            return "sideways", 0.5
    
    async def enhance_ai_prompt(self, base_prompt: str, symbol: str = "BTCUSDT") -> str:
        """
        Enhance AI vision prompt with historical context
        """
        try:
            context = await self.get_market_context(symbol)
            
            enhanced_prompt = f"""
{base_prompt}

HISTORICAL MARKET CONTEXT:
- Current Price: ${context.current_price:,.2f}
- 24h Change: {context.price_change_24h:+.2f}%
- 7d Change: {context.price_change_7d:+.2f}%
- 30d Change: {context.price_change_30d:+.2f}%
- Market Phase: {context.market_phase.upper()} (Strength: {context.trend_strength:.2f})
- 30d Volatility: {context.volatility_30d:.2f}%

SUPPORT/RESISTANCE LEVELS:
- Support: {', '.join([f'${level:,.0f}' for level in context.support_levels])}
- Resistance: {', '.join([f'${level:,.0f}' for level in context.resistance_levels])}

HISTORICAL PATTERNS:
{self._format_historical_patterns(context.historical_patterns)}

Please analyze the chart considering this historical context and market data.
Focus on how current patterns compare to historical outcomes.
"""
            return enhanced_prompt
            
        except Exception as e:
            logger.error(f"Error enhancing AI prompt: {str(e)}")
            return base_prompt
    
    def _format_historical_patterns(self, patterns: List[HistoricalPattern]) -> str:
        """Format historical patterns for AI prompt"""
        if not patterns:
            return "- No significant historical patterns found"
        
        formatted = []
        for i, pattern in enumerate(patterns[:3], 1):
            formatted.append(
                f"- Pattern {i}: {pattern.date} (Similarity: {pattern.similarity_score:.2f}) "
                f"→ {pattern.outcome.upper()} (7d: {pattern.price_change_7d:+.1f}%)"
            )
        
        return '\n'.join(formatted)
