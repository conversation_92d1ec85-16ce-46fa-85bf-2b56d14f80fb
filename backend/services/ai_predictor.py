"""
AI Prediction Service
Generates trading recommendations based on chart data and technical analysis
"""

import logging
from typing import Dict, Any
import random

from models.chart_data import (
    ChartData, TechnicalIndicators, PredictionResult, 
    PositionRecommendation, RiskAssessment
)

logger = logging.getLogger(__name__)

class AIPredictor:
    def __init__(self):
        self.model_loaded = False
        self.confidence_threshold = 0.6
        
    async def predict(self, chart_data: ChartData, technical_indicators: TechnicalIndicators) -> PredictionResult:
        """
        Generate AI-powered trading prediction
        """
        try:
            # Analyze multiple factors
            trend_score = self._analyze_trend(chart_data, technical_indicators)
            momentum_score = self._analyze_momentum(technical_indicators)
            volume_score = self._analyze_volume_signal(technical_indicators)
            pattern_score = self._analyze_patterns(chart_data)
            
            # Combine scores
            overall_score = self._combine_scores(trend_score, momentum_score, volume_score, pattern_score)
            
            # Generate recommendation
            recommendation = self._score_to_recommendation(overall_score)
            confidence = self._calculate_confidence(overall_score, technical_indicators)
            
            # Calculate target price and risk assessment
            target_price = self._calculate_target_price(chart_data, overall_score)
            risk_assessment = self._assess_risk(chart_data, technical_indicators, overall_score)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(
                trend_score, momentum_score, volume_score, pattern_score, 
                technical_indicators, chart_data
            )
            
            return PredictionResult(
                recommendation=recommendation,
                confidence=confidence,
                target_price=target_price,
                time_horizon="24h",
                reasoning=reasoning,
                risk_assessment=risk_assessment
            )
            
        except Exception as e:
            logger.error(f"Error in AI prediction: {str(e)}")
            return self._default_prediction()
    
    def _analyze_trend(self, chart_data: ChartData, technical_indicators: TechnicalIndicators) -> float:
        """Analyze trend direction and strength"""
        score = 0.0
        
        # Pattern-based trend analysis
        if chart_data.detected_patterns.trend_direction == "bullish":
            score += 0.3 * (chart_data.detected_patterns.trend_strength or 0.5)
        elif chart_data.detected_patterns.trend_direction == "bearish":
            score -= 0.3 * (chart_data.detected_patterns.trend_strength or 0.5)
        
        # Moving average analysis
        if technical_indicators.moving_averages:
            mas = technical_indicators.moving_averages
            if "ma_20" in mas and "ma_50" in mas:
                if mas["ma_20"] > mas["ma_50"]:
                    score += 0.2
                else:
                    score -= 0.2
        
        return max(-1.0, min(1.0, score))
    
    def _analyze_momentum(self, technical_indicators: TechnicalIndicators) -> float:
        """Analyze momentum indicators"""
        score = 0.0
        
        # RSI analysis
        if technical_indicators.rsi:
            rsi = technical_indicators.rsi
            if rsi > 70:
                score -= 0.3  # Overbought
            elif rsi < 30:
                score += 0.3  # Oversold
            elif 40 <= rsi <= 60:
                score += 0.1  # Neutral momentum
        
        # MACD analysis
        if technical_indicators.macd:
            macd_data = technical_indicators.macd
            if macd_data.get("macd", 0) > macd_data.get("signal", 0):
                score += 0.2
            else:
                score -= 0.2
        
        return max(-1.0, min(1.0, score))
    
    def _analyze_volume_signal(self, technical_indicators: TechnicalIndicators) -> float:
        """Analyze volume patterns"""
        score = 0.0
        
        if technical_indicators.volume_analysis:
            vol_data = technical_indicators.volume_analysis
            
            if vol_data.get("high_volume", False):
                score += 0.2
            
            if vol_data.get("volume_trend") == "increasing":
                score += 0.1
            elif vol_data.get("volume_trend") == "decreasing":
                score -= 0.1
        
        return max(-1.0, min(1.0, score))
    
    def _analyze_patterns(self, chart_data: ChartData) -> float:
        """Analyze chart patterns"""
        score = 0.0
        
        patterns = chart_data.detected_patterns
        
        # Bullish patterns
        bullish_patterns = ["hammer", "bullish_engulfing", "ascending_triangle", "cup_and_handle"]
        bearish_patterns = ["shooting_star", "bearish_engulfing", "descending_triangle", "head_and_shoulders"]
        
        for pattern in patterns.candlestick_patterns + patterns.chart_patterns:
            if pattern in bullish_patterns:
                score += 0.15
            elif pattern in bearish_patterns:
                score -= 0.15
        
        return max(-1.0, min(1.0, score))
    
    def _combine_scores(self, trend: float, momentum: float, volume: float, pattern: float) -> float:
        """Combine individual scores with weights"""
        weights = {
            "trend": 0.35,
            "momentum": 0.25,
            "volume": 0.20,
            "pattern": 0.20
        }
        
        combined = (
            trend * weights["trend"] +
            momentum * weights["momentum"] +
            volume * weights["volume"] +
            pattern * weights["pattern"]
        )
        
        return max(-1.0, min(1.0, combined))
    
    def _score_to_recommendation(self, score: float) -> PositionRecommendation:
        """Convert numerical score to recommendation"""
        if score >= 0.6:
            return PositionRecommendation.STRONG_BUY
        elif score >= 0.2:
            return PositionRecommendation.BUY
        elif score >= -0.2:
            return PositionRecommendation.HOLD
        elif score >= -0.6:
            return PositionRecommendation.SELL
        else:
            return PositionRecommendation.STRONG_SELL
    
    def _calculate_confidence(self, score: float, technical_indicators: TechnicalIndicators) -> float:
        """Calculate confidence level"""
        base_confidence = abs(score)
        
        # Adjust based on data quality
        if technical_indicators.rsi and technical_indicators.macd:
            base_confidence += 0.1
        
        if technical_indicators.volume_analysis and technical_indicators.volume_analysis.get("status") != "no_volume_data":
            base_confidence += 0.1
        
        return max(0.1, min(1.0, base_confidence))
    
    def _calculate_target_price(self, chart_data: ChartData, score: float) -> float:
        """Calculate target price based on analysis"""
        if not chart_data.extracted_prices:
            return 50000.0  # Default fallback
        
        current_price = chart_data.extracted_prices[-1].close or 50000.0
        
        # Calculate target based on score
        price_change_percent = score * 0.05  # Max 5% change
        target_price = current_price * (1 + price_change_percent)
        
        return round(target_price, 2)
    
    def _assess_risk(self, chart_data: ChartData, technical_indicators: TechnicalIndicators, score: float) -> RiskAssessment:
        """Assess risk and calculate stop-loss/take-profit levels"""
        if not chart_data.extracted_prices:
            current_price = 50000.0
        else:
            current_price = chart_data.extracted_prices[-1].close or 50000.0
        
        # Risk level based on volatility and confidence
        volatility = self._calculate_volatility(chart_data)
        
        if volatility > 0.05:
            risk_level = "high"
            stop_loss_percent = 0.08
            take_profit_percent = 0.12
        elif volatility > 0.03:
            risk_level = "medium"
            stop_loss_percent = 0.05
            take_profit_percent = 0.08
        else:
            risk_level = "low"
            stop_loss_percent = 0.03
            take_profit_percent = 0.05
        
        # Adjust based on recommendation direction
        if score > 0:  # Bullish
            stop_loss = current_price * (1 - stop_loss_percent)
            take_profit = current_price * (1 + take_profit_percent)
        else:  # Bearish
            stop_loss = current_price * (1 + stop_loss_percent)
            take_profit = current_price * (1 - take_profit_percent)
        
        risk_reward_ratio = abs(take_profit - current_price) / abs(current_price - stop_loss)
        
        return RiskAssessment(
            risk_level=risk_level,
            stop_loss=round(stop_loss, 2),
            take_profit=round(take_profit, 2),
            risk_reward_ratio=round(risk_reward_ratio, 2)
        )
    
    def _calculate_volatility(self, chart_data: ChartData) -> float:
        """Calculate price volatility"""
        if len(chart_data.extracted_prices) < 2:
            return 0.03  # Default volatility
        
        prices = [p.close for p in chart_data.extracted_prices if p.close]
        if len(prices) < 2:
            return 0.03
        
        returns = []
        for i in range(1, len(prices)):
            ret = (prices[i] - prices[i-1]) / prices[i-1]
            returns.append(ret)
        
        if not returns:
            return 0.03
        
        import statistics
        volatility = statistics.stdev(returns) if len(returns) > 1 else 0.03
        return volatility
    
    def _generate_reasoning(self, trend_score: float, momentum_score: float, 
                          volume_score: float, pattern_score: float,
                          technical_indicators: TechnicalIndicators, 
                          chart_data: ChartData) -> str:
        """Generate human-readable reasoning for the recommendation"""
        reasons = []
        
        # Trend analysis
        if trend_score > 0.2:
            reasons.append("Strong bullish trend detected")
        elif trend_score < -0.2:
            reasons.append("Strong bearish trend identified")
        else:
            reasons.append("Sideways trend observed")
        
        # RSI analysis
        if technical_indicators.rsi:
            rsi = technical_indicators.rsi
            if rsi > 70:
                reasons.append(f"RSI at {rsi} indicates overbought conditions")
            elif rsi < 30:
                reasons.append(f"RSI at {rsi} suggests oversold conditions")
        
        # Pattern analysis
        if chart_data.detected_patterns.candlestick_patterns:
            patterns = ", ".join(chart_data.detected_patterns.candlestick_patterns)
            reasons.append(f"Detected patterns: {patterns}")
        
        # Volume analysis
        if volume_score > 0.1:
            reasons.append("Volume supports the price movement")
        elif volume_score < -0.1:
            reasons.append("Volume shows weakness in the trend")
        
        return ". ".join(reasons) + "."
    
    def _default_prediction(self) -> PredictionResult:
        """Return default prediction in case of errors"""
        return PredictionResult(
            recommendation=PositionRecommendation.HOLD,
            confidence=0.5,
            target_price=50000.0,
            time_horizon="24h",
            reasoning="Unable to perform complete analysis due to insufficient data.",
            risk_assessment=RiskAssessment(
                risk_level="medium",
                stop_loss=47500.0,
                take_profit=52500.0,
                risk_reward_ratio=1.0
            )
        )
