"""
Image Processing Service
Handles chart image processing, OCR, and data extraction
"""

import cv2
import numpy as np
from PIL import Image
import pytesseract
import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from models.chart_data import ChartAnalysisRequest, ChartData, PriceData, PatternRecognition

logger = logging.getLogger(__name__)

class ImageProcessor:
    def __init__(self):
        self.supported_formats = ['.jpg', '.jpeg', '.png', '.bmp', '.tiff']
        
    async def process_chart_image(self, request: ChartAnalysisRequest) -> ChartData:
        """
        Main method to process chart image and extract data
        """
        try:
            # Load and preprocess image
            image = self._load_image(request.file_path)
            processed_image = self._preprocess_image(image)
            
            # Extract chart data
            price_data = await self._extract_price_data(processed_image)
            patterns = await self._detect_patterns(processed_image)
            metadata = self._extract_metadata(processed_image, request)
            
            return ChartData(
                extracted_prices=price_data,
                detected_patterns=patterns,
                chart_metadata=metadata,
                processing_status="success"
            )
            
        except Exception as e:
            logger.error(f"Error processing image: {str(e)}")
            return ChartData(
                processing_status=f"error: {str(e)}"
            )
    
    def _load_image(self, file_path: str) -> np.ndarray:
        """Load image from file path"""
        if not Path(file_path).exists():
            raise FileNotFoundError(f"Image file not found: {file_path}")
        
        # Load image using OpenCV
        image = cv2.imread(file_path)
        if image is None:
            raise ValueError(f"Could not load image: {file_path}")
        
        return image
    
    def _preprocess_image(self, image: np.ndarray) -> np.ndarray:
        """
        Preprocess image for better analysis
        """
        # Convert to RGB
        image_rgb = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        
        # Resize if too large (maintain aspect ratio)
        height, width = image_rgb.shape[:2]
        if width > 1920 or height > 1080:
            scale = min(1920/width, 1080/height)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image_rgb = cv2.resize(image_rgb, (new_width, new_height))
        
        # Enhance contrast
        lab = cv2.cvtColor(image_rgb, cv2.COLOR_RGB2LAB)
        l, a, b = cv2.split(lab)
        clahe = cv2.createCLAHE(clipLimit=2.0, tileGridSize=(8,8))
        l = clahe.apply(l)
        enhanced = cv2.merge([l, a, b])
        enhanced = cv2.cvtColor(enhanced, cv2.COLOR_LAB2RGB)
        
        return enhanced
    
    async def _extract_price_data(self, image: np.ndarray) -> List[PriceData]:
        """
        Extract price data from chart image using computer vision
        """
        # This is a placeholder implementation
        # In a real implementation, you would:
        # 1. Detect chart area
        # 2. Identify price axis and time axis
        # 3. Extract candlestick/line data points
        # 4. Convert pixel coordinates to price values
        
        logger.info("Extracting price data from chart image")
        
        # Placeholder: return sample data
        sample_data = [
            PriceData(open=50000, high=51000, low=49500, close=50500, volume=1000),
            PriceData(open=50500, high=52000, low=50000, close=51500, volume=1200),
            PriceData(open=51500, high=51800, low=50800, close=51200, volume=900),
        ]
        
        return sample_data
    
    async def _detect_patterns(self, image: np.ndarray) -> PatternRecognition:
        """
        Detect chart patterns using computer vision
        """
        logger.info("Detecting chart patterns")
        
        # Placeholder implementation
        # Real implementation would use:
        # 1. Edge detection for trend lines
        # 2. Pattern matching for common formations
        # 3. Candlestick pattern recognition
        
        return PatternRecognition(
            candlestick_patterns=["doji", "hammer"],
            chart_patterns=["ascending_triangle"],
            trend_direction="bullish",
            trend_strength=0.7
        )
    
    def _extract_metadata(self, image: np.ndarray, request: ChartAnalysisRequest) -> Dict[str, Any]:
        """
        Extract metadata from image and request
        """
        height, width = image.shape[:2]
        
        return {
            "image_dimensions": {"width": width, "height": height},
            "coin_symbol": request.coin_symbol,
            "timeframe": request.timeframe,
            "chart_type": request.chart_type,
            "file_path": request.file_path
        }
    
    def _detect_chart_area(self, image: np.ndarray) -> tuple:
        """
        Detect the main chart area in the image
        """
        # Convert to grayscale
        gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
        
        # Apply edge detection
        edges = cv2.Canny(gray, 50, 150, apertureSize=3)
        
        # Find contours
        contours, _ = cv2.findContours(edges, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        
        # Find the largest rectangular contour (likely the chart area)
        largest_area = 0
        chart_contour = None
        
        for contour in contours:
            area = cv2.contourArea(contour)
            if area > largest_area:
                # Approximate contour to polygon
                epsilon = 0.02 * cv2.arcLength(contour, True)
                approx = cv2.approxPolyDP(contour, epsilon, True)
                
                # Check if it's roughly rectangular (4 corners)
                if len(approx) >= 4:
                    largest_area = area
                    chart_contour = approx
        
        if chart_contour is not None:
            x, y, w, h = cv2.boundingRect(chart_contour)
            return (x, y, x + w, y + h)
        
        # Fallback: return center 80% of image
        h, w = image.shape[:2]
        margin_x, margin_y = int(w * 0.1), int(h * 0.1)
        return (margin_x, margin_y, w - margin_x, h - margin_y)
