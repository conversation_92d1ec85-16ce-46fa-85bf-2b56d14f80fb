# 📁 Backend Archive

This directory contains legacy backend files that were used during development and testing phases.

## 🗂️ **Archived Files:**

### **legacy_backends/**
- **main.py** - Original basic backend (replaced by enhanced version)
- **simple_main.py** - Minimal test backend for development
- **demo_ai_vision.py** - AI vision testing and demonstration script

## 📝 **Why These Files Were Archived:**

### **main.py (Original)**
- ❌ Basic AI analysis only
- ❌ Limited reasoning capabilities
- ❌ No multi-factor analysis
- ❌ Simple results format
- 🔄 **Replaced by**: Enhanced main.py with full features

### **simple_main.py**
- ✅ Good for: Basic testing and development
- ❌ Missing: Production features
- 🔄 **Status**: Development tool only

### **demo_ai_vision.py**
- ✅ Good for: Testing AI vision capabilities
- ❌ Missing: Full application features
- 🔄 **Status**: Demo/testing script

## 🎯 **Current Active Backend:**

**File**: `../main.py` (formerly enhanced_main.py)

**Features**:
- ✅ Multi-factor analysis scoring
- ✅ Detailed AI reasoning engine
- ✅ Market context integration
- ✅ Professional results format
- ✅ Real-time progress updates
- ✅ OpenAI GPT-4o Vision processing
- ✅ Historical pattern matching
- ✅ Risk assessment and target rationale

## 🔄 **Migration History:**

1. **Phase 1**: `main.py` (basic backend)
2. **Phase 2**: `simple_main.py` (testing)
3. **Phase 3**: `demo_ai_vision.py` (AI vision testing)
4. **Phase 4**: `enhanced_main.py` (full features)
5. **Phase 5**: Cleanup - `enhanced_main.py` → `main.py`

## 📚 **How to Use Archived Files:**

If you need to reference or test with the old backends:

```bash
# Copy an archived file back to main directory
cp archive/legacy_backends/simple_main.py ../test_main.py

# Run archived backend for testing
cd ..
python3 test_main.py
```

## ⚠️ **Important Notes:**

- **Don't use archived files for production**
- **Current main.py has all the latest features**
- **Archived files may have outdated dependencies**
- **Use archived files for reference only**

## 🚀 **Current Startup Commands:**

```bash
# Production backend
uvicorn main:app --reload

# Development script
./start-dev.sh

# Docker
./start.sh
```

---

**The current backend (`../main.py`) is the enhanced version with all features!** 🎯
