"""
Test script for Binance API integration
"""

import asyncio
import sys
import os
from datetime import datetime, timedelta

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.binance_data_provider import BinanceDataProvider

async def test_binance_api():
    """Test Binance API functionality"""
    
    print("🚀 Testing Binance API Integration")
    print("=" * 50)
    
    async with BinanceDataProvider() as binance:
        
        # Test 1: Get recent Bitcoin data
        print("\n📊 Test 1: Recent Bitcoin Daily Data (Last 30 days)")
        print("-" * 50)
        try:
            recent_klines = await binance.get_klines(
                symbol="BTCUSDT",
                interval="1d",
                limit=30
            )
            
            print(f"✅ Successfully fetched {len(recent_klines)} daily klines")
            
            # Show first and last few entries
            print(f"\n📈 First entry: {recent_klines[0].date_str}")
            print(f"   Open: ${recent_klines[0].open_price:,.2f}")
            print(f"   High: ${recent_klines[0].high_price:,.2f}")
            print(f"   Low: ${recent_klines[0].low_price:,.2f}")
            print(f"   Close: ${recent_klines[0].close_price:,.2f}")
            print(f"   Volume: {recent_klines[0].volume:,.2f} BTC")
            
            print(f"\n📈 Latest entry: {recent_klines[-1].date_str}")
            print(f"   Open: ${recent_klines[-1].open_price:,.2f}")
            print(f"   High: ${recent_klines[-1].high_price:,.2f}")
            print(f"   Low: ${recent_klines[-1].low_price:,.2f}")
            print(f"   Close: ${recent_klines[-1].close_price:,.2f}")
            print(f"   Volume: {recent_klines[-1].volume:,.2f} BTC")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 2: Get current price
        print("\n💰 Test 2: Current Bitcoin Price")
        print("-" * 50)
        try:
            current_price = await binance.get_current_price("BTCUSDT")
            print(f"✅ Current BTC Price: ${current_price['price']:,.2f}")
            print(f"   Symbol: {current_price['symbol']}")
            print(f"   Timestamp: {datetime.fromtimestamp(current_price['timestamp']/1000)}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 3: Get 24hr ticker
        print("\n📊 Test 3: 24hr Ticker Statistics")
        print("-" * 50)
        try:
            ticker = await binance.get_24hr_ticker("BTCUSDT")
            print(f"✅ 24hr Statistics:")
            print(f"   Price Change: ${ticker['price_change']:,.2f} ({ticker['price_change_percent']:.2f}%)")
            print(f"   High: ${ticker['high_price']:,.2f}")
            print(f"   Low: ${ticker['low_price']:,.2f}")
            print(f"   Volume: {ticker['volume']:,.2f} BTC")
            print(f"   Quote Volume: ${ticker['quote_volume']:,.2f} USDT")
            print(f"   Trades: {ticker['count']:,}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 4: Get hourly data
        print("\n⏰ Test 4: Recent Hourly Data (Last 24 hours)")
        print("-" * 50)
        try:
            hourly_klines = await binance.get_klines(
                symbol="BTCUSDT",
                interval="1h",
                limit=24
            )
            
            print(f"✅ Successfully fetched {len(hourly_klines)} hourly klines")
            print(f"   Time range: {hourly_klines[0].date_str} to {hourly_klines[-1].date_str}")
            
            # Calculate price change over 24h
            price_change = hourly_klines[-1].close_price - hourly_klines[0].open_price
            price_change_pct = (price_change / hourly_klines[0].open_price) * 100
            
            print(f"   24h Change: ${price_change:,.2f} ({price_change_pct:+.2f}%)")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 5: Historical data range
        print("\n📅 Test 5: Historical Data Range (Last 7 days)")
        print("-" * 50)
        try:
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
            
            historical_klines = await binance.get_historical_data_range(
                symbol="BTCUSDT",
                interval="1d",
                start_date=start_date,
                end_date=end_date
            )
            
            print(f"✅ Successfully fetched {len(historical_klines)} historical klines")
            print(f"   Date range: {start_date} to {end_date}")
            
            if historical_klines:
                # Calculate weekly performance
                weekly_change = historical_klines[-1].close_price - historical_klines[0].open_price
                weekly_change_pct = (weekly_change / historical_klines[0].open_price) * 100
                
                print(f"   Weekly Change: ${weekly_change:,.2f} ({weekly_change_pct:+.2f}%)")
                
                # Find highest and lowest prices
                high_price = max(kline.high_price for kline in historical_klines)
                low_price = min(kline.low_price for kline in historical_klines)
                
                print(f"   Weekly High: ${high_price:,.2f}")
                print(f"   Weekly Low: ${low_price:,.2f}")
                print(f"   Volatility: {((high_price - low_price) / low_price * 100):.2f}%")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        # Test 6: Convert to DataFrame
        print("\n📊 Test 6: Data Analysis with Pandas")
        print("-" * 50)
        try:
            # Get some data for analysis
            analysis_klines = await binance.get_klines(
                symbol="BTCUSDT",
                interval="1d",
                limit=100
            )
            
            # Convert to DataFrame
            df = binance.klines_to_dataframe(analysis_klines)
            
            print(f"✅ Created DataFrame with {len(df)} rows")
            print(f"   Columns: {list(df.columns)}")
            print(f"   Date range: {df.index[0]} to {df.index[-1]}")
            
            # Basic statistics
            print(f"\n📊 Price Statistics (Last 100 days):")
            print(f"   Average Close: ${df['close'].mean():,.2f}")
            print(f"   Median Close: ${df['close'].median():,.2f}")
            print(f"   Std Deviation: ${df['close'].std():,.2f}")
            print(f"   Min Close: ${df['close'].min():,.2f}")
            print(f"   Max Close: ${df['close'].max():,.2f}")
            
            # Volume statistics
            print(f"\n📊 Volume Statistics:")
            print(f"   Average Volume: {df['volume'].mean():,.2f} BTC")
            print(f"   Total Volume: {df['volume'].sum():,.2f} BTC")
            
        except Exception as e:
            print(f"❌ Error: {e}")
    
    print("\n" + "=" * 50)
    print("🎉 Binance API Testing Complete!")

async def test_specific_endpoint():
    """Test the specific endpoint you provided"""
    print("\n🎯 Testing Your Specific Endpoint")
    print("=" * 50)
    print("URL: https://api.binance.com/api/v3/klines?symbol=BTCUSDT&interval=1d&limit=1000")
    
    async with BinanceDataProvider() as binance:
        try:
            # Test your exact parameters
            klines = await binance.get_klines(
                symbol="BTCUSDT",
                interval="1d",
                limit=1000
            )
            
            print(f"✅ Successfully fetched {len(klines)} daily klines")
            print(f"   Date range: {klines[0].date_str} to {klines[-1].date_str}")
            
            # Calculate total time span
            days_span = (klines[-1].open_time - klines[0].open_time) / (1000 * 60 * 60 * 24)
            print(f"   Time span: {days_span:.0f} days ({days_span/365:.1f} years)")
            
            # Show some key data points
            print(f"\n📊 Key Data Points:")
            print(f"   Oldest: {klines[0].date_str} - ${klines[0].close_price:,.2f}")
            print(f"   Newest: {klines[-1].date_str} - ${klines[-1].close_price:,.2f}")
            
            # Calculate total return
            total_return = ((klines[-1].close_price - klines[0].close_price) / klines[0].close_price) * 100
            print(f"   Total Return: {total_return:+.2f}%")
            
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    # Run the tests
    asyncio.run(test_binance_api())
    asyncio.run(test_specific_endpoint())
