"""
Generate a comprehensive analysis report showing the complete pipeline
"""

import json
import asyncio
from pathlib import Path
from services.image_processor import ImageProcessor
from services.technical_analysis import TechnicalAnalyzer
from services.ai_predictor import AIPredictor
from models.chart_data import ChartAnalysisRequest

async def generate_analysis_report(image_path: str):
    """
    Generate a detailed report of how the chart analysis works
    """
    
    print("📊 COMPREHENSIVE CHART ANALYSIS REPORT")
    print("=" * 80)
    
    # Initialize services
    image_processor = ImageProcessor()
    technical_analyzer = TechnicalAnalyzer()
    ai_predictor = AIPredictor()
    
    # Create request
    request = ChartAnalysisRequest(
        file_path=image_path,
        coin_symbol="BTC",
        timeframe="1h",
        chart_type="candlestick"
    )
    
    print("🔍 ANALYSIS PIPELINE BREAKDOWN")
    print("-" * 50)
    
    # Step 1: Image Processing Pipeline
    print("\n1️⃣ IMAGE PROCESSING PIPELINE")
    print("   📥 User uploads chart image")
    print("   🔄 Image preprocessing:")
    print("      • Resize if too large (max 1920x1080)")
    print("      • Convert color space (BGR → RGB)")
    print("      • Enhance contrast using CLAHE")
    print("      • Noise reduction")
    
    # Step 2: Computer Vision Analysis
    print("\n2️⃣ COMPUTER VISION ANALYSIS")
    print("   🔍 Chart element detection:")
    print("      • Edge detection using Canny algorithm")
    print("      • Line detection using Hough transform")
    print("      • Classify lines as horizontal/vertical")
    print("      • Identify chart area boundaries")
    print("      • Detect grid patterns")
    
    # Step 3: Candlestick Extraction
    print("\n3️⃣ CANDLESTICK EXTRACTION")
    print("   🕯️ Color-based detection:")
    print("      • Convert to HSV color space")
    print("      • Define green/red color ranges")
    print("      • Create color masks")
    print("      • Find contours for each color")
    print("      • Filter by size to remove noise")
    print("      • Sort by time (x-position)")
    
    # Step 4: Price Data Conversion
    print("\n4️⃣ PRICE DATA CONVERSION")
    print("   💰 Pixel-to-price mapping:")
    print("      • Map Y coordinates to price levels")
    print("      • Determine OHLC from candlestick geometry")
    print("      • Estimate volume from candle width")
    print("      • Apply realistic price scaling")
    
    # Step 5: Pattern Recognition
    print("\n5️⃣ PATTERN RECOGNITION")
    print("   🔍 Candlestick patterns:")
    print("      • Doji: Small body relative to width")
    print("      • Hammer: Small body at top, long lower shadow")
    print("      • Engulfing: Large candle engulfs previous")
    print("      • Shooting star: Small body at bottom")
    print("   📊 Chart patterns:")
    print("      • Triangles: Converging trend lines")
    print("      • Channels: Parallel support/resistance")
    print("      • Trend analysis: Linear regression on positions")
    
    # Step 6: Technical Analysis
    print("\n6️⃣ TECHNICAL ANALYSIS")
    print("   📈 Indicator calculations:")
    print("      • RSI: 14-period with proper smoothing")
    print("      • MACD: 12/26 EMA with 9-period signal")
    print("      • Moving Averages: Multiple timeframes")
    print("      • Bollinger Bands: 20-period with 2 std dev")
    print("      • Volume analysis: Trends and anomalies")
    print("      • Support/Resistance: Local extrema detection")
    
    # Step 7: AI Prediction Engine
    print("\n7️⃣ AI PREDICTION ENGINE")
    print("   🤖 Multi-factor analysis:")
    print("      • Trend Score (40%): Pattern + MA analysis")
    print("      • Momentum Score (25%): RSI + MACD signals")
    print("      • Volume Score (20%): Volume trends")
    print("      • Pattern Score (15%): Detected formations")
    print("   🎯 Recommendation logic:")
    print("      • Score > 0.6: Strong Buy")
    print("      • Score > 0.2: Buy")
    print("      • Score -0.2 to 0.2: Hold")
    print("      • Score < -0.2: Sell")
    print("      • Score < -0.6: Strong Sell")
    
    # Step 8: Risk Assessment
    print("\n8️⃣ RISK ASSESSMENT")
    print("   🛡️ Risk calculation:")
    print("      • Volatility analysis from price data")
    print("      • Dynamic stop-loss/take-profit levels")
    print("      • Risk/reward ratio calculation")
    print("      • Confidence scoring based on data quality")
    
    # Now run actual analysis
    print("\n" + "=" * 80)
    print("🔬 ACTUAL ANALYSIS RESULTS")
    print("=" * 80)
    
    # Process the chart
    chart_data = await image_processor.process_chart_image(request)
    technical_indicators = await technical_analyzer.analyze(chart_data)
    predictions = await ai_predictor.predict(chart_data, technical_indicators)
    
    # Show detailed results
    print(f"\n📊 EXTRACTED DATA:")
    print(f"   • Price points: {len(chart_data.extracted_prices)}")
    print(f"   • Price range: ${min(p.low for p in chart_data.extracted_prices):.2f} - ${max(p.high for p in chart_data.extracted_prices):.2f}")
    print(f"   • Candlestick patterns: {chart_data.detected_patterns.candlestick_patterns}")
    print(f"   • Chart patterns: {chart_data.detected_patterns.chart_patterns}")
    print(f"   • Trend: {chart_data.detected_patterns.trend_direction} (strength: {chart_data.detected_patterns.trend_strength})")
    
    print(f"\n📈 TECHNICAL INDICATORS:")
    print(f"   • RSI: {technical_indicators.rsi:.2f}")
    if technical_indicators.macd:
        print(f"   • MACD: {technical_indicators.macd['macd']:.4f}")
        print(f"   • MACD Signal: {technical_indicators.macd['signal']:.4f}")
    if technical_indicators.moving_averages:
        print(f"   • MA20: ${technical_indicators.moving_averages.get('ma_20', 0):.2f}")
        print(f"   • MA50: ${technical_indicators.moving_averages.get('ma_50', 0):.2f}")
    
    print(f"\n🤖 AI PREDICTION:")
    print(f"   • Recommendation: {predictions.recommendation.value.upper()}")
    print(f"   • Confidence: {predictions.confidence:.1%}")
    print(f"   • Target Price: ${predictions.target_price:.2f}")
    print(f"   • Risk Level: {predictions.risk_assessment.risk_level}")
    print(f"   • Stop Loss: ${predictions.risk_assessment.stop_loss:.2f}")
    print(f"   • Take Profit: ${predictions.risk_assessment.take_profit:.2f}")
    
    print(f"\n💭 REASONING:")
    print(f"   {predictions.reasoning}")
    
    # Show scoring breakdown
    print(f"\n🔢 SCORING BREAKDOWN:")
    trend_score = ai_predictor._analyze_trend(chart_data, technical_indicators)
    momentum_score = ai_predictor._analyze_momentum(technical_indicators)
    volume_score = ai_predictor._analyze_volume_signal(technical_indicators)
    pattern_score = ai_predictor._analyze_patterns(chart_data)
    
    print(f"   • Trend Score: {trend_score:.3f}")
    print(f"   • Momentum Score: {momentum_score:.3f}")
    print(f"   • Volume Score: {volume_score:.3f}")
    print(f"   • Pattern Score: {pattern_score:.3f}")
    
    overall_score = ai_predictor._combine_scores(trend_score, momentum_score, volume_score, pattern_score)
    print(f"   • Overall Score: {overall_score:.3f}")
    
    print("\n" + "=" * 80)
    print("✅ ANALYSIS COMPLETE")
    print("=" * 80)
    
    print("\n🔍 KEY INSIGHTS:")
    print("   • The AI successfully detected and analyzed the chart structure")
    print("   • Computer vision identified candlesticks and chart patterns")
    print("   • Technical indicators were calculated from extracted price data")
    print("   • Multi-factor AI model provided weighted recommendation")
    print("   • Risk assessment included dynamic stop-loss/take-profit levels")
    
    print("\n📈 ACCURACY FACTORS:")
    print("   • Chart quality: Higher resolution = better detection")
    print("   • Color contrast: Clear green/red candles improve accuracy")
    print("   • Chart type: Candlestick charts work best")
    print("   • Timeframe: More data points = better analysis")
    print("   • Grid lines: Help with precise area detection")

if __name__ == "__main__":
    test_image = "uploads/test_chart.png"
    if Path(test_image).exists():
        asyncio.run(generate_analysis_report(test_image))
    else:
        print("❌ Test image not found")
        print("Available images:")
        for img in Path("uploads").glob("*.png"):
            print(f"   📁 {img}")
