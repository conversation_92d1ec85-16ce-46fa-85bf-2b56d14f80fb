#!/usr/bin/env python3
"""
Test script to fetch Bitcoin historical data and store it in files
"""

import asyncio
import json
import pandas as pd
from datetime import datetime, timedelta
import os
import sys

# Add the backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from services.historical_data_service import HistoricalDataService
from services.binance_data_provider import BinanceDataProvider

async def test_bitcoin_historical_data():
    """
    Test function to fetch Bitcoin historical data and save to files
    """
    print("🚀 Starting Bitcoin Historical Data Test")
    print("=" * 50)
    
    try:
        # Test 1: Get market context (recent data with analysis)
        print("\n📊 Test 1: Getting Market Context...")
        async with HistoricalDataService() as historical_service:
            market_context = await historical_service.get_market_context("BTCUSDT")
            
            # Convert to dictionary for JSON serialization
            context_dict = market_context.__dict__.copy()
            context_dict['historical_patterns'] = [
                pattern.__dict__ for pattern in context_dict['historical_patterns']
            ]
            
            # Save market context
            with open('bitcoin_market_context.json', 'w') as f:
                json.dump(context_dict, f, indent=2, default=str)
            
            print(f"✅ Market Context saved to: bitcoin_market_context.json")
            print(f"   Current Price: ${market_context.current_price:,.2f}")
            print(f"   24h Change: {market_context.price_change_24h:+.2f}%")
            print(f"   Market Phase: {market_context.market_phase}")
            print(f"   Historical Patterns Found: {len(market_context.historical_patterns)}")
        
        # Test 2: Get lifetime historical data
        print("\n📈 Test 2: Getting Lifetime Historical Data...")
        async with HistoricalDataService() as historical_service:
            lifetime_df = await historical_service.get_lifetime_data("BTCUSDT", "1d")
            
            # Save to CSV
            lifetime_df.to_csv('bitcoin_lifetime_data.csv')
            
            print(f"✅ Lifetime Data saved to: bitcoin_lifetime_data.csv")
            print(f"   Data Points: {len(lifetime_df):,}")
            print(f"   Date Range: {lifetime_df.index[0]} to {lifetime_df.index[-1]}")
            print(f"   Price Range: ${lifetime_df['low'].min():,.2f} - ${lifetime_df['high'].max():,.2f}")
        
        # Test 3: Get recent detailed data (last 30 days)
        print("\n📅 Test 3: Getting Recent Detailed Data...")
        async with BinanceDataProvider() as binance:
            # Get hourly data for last 30 days
            recent_data = await binance.get_klines(
                symbol="BTCUSDT",
                interval="1h",
                limit=720  # 30 days * 24 hours
            )
            
            # Convert to DataFrame
            recent_df = binance.klines_to_dataframe(recent_data)
            
            # Save to CSV
            recent_df.to_csv('bitcoin_recent_hourly.csv')
            
            print(f"✅ Recent Hourly Data saved to: bitcoin_recent_hourly.csv")
            print(f"   Data Points: {len(recent_df):,}")
            print(f"   Date Range: {recent_df.index[0]} to {recent_df.index[-1]}")
            print(f"   Current Price: ${recent_df['close'].iloc[-1]:,.2f}")
        
        # Test 4: Create summary statistics
        print("\n📊 Test 4: Creating Summary Statistics...")
        
        # Calculate summary stats
        summary_stats = {
            "test_timestamp": datetime.now().isoformat(),
            "bitcoin_symbol": "BTCUSDT",
            "data_sources": {
                "market_context": {
                    "file": "bitcoin_market_context.json",
                    "current_price": float(market_context.current_price),
                    "market_phase": market_context.market_phase,
                    "patterns_found": len(market_context.historical_patterns)
                },
                "lifetime_data": {
                    "file": "bitcoin_lifetime_data.csv",
                    "data_points": len(lifetime_df),
                    "start_date": str(lifetime_df.index[0]),
                    "end_date": str(lifetime_df.index[-1]),
                    "min_price": float(lifetime_df['low'].min()),
                    "max_price": float(lifetime_df['high'].max())
                },
                "recent_hourly": {
                    "file": "bitcoin_recent_hourly.csv",
                    "data_points": len(recent_df),
                    "start_date": str(recent_df.index[0]),
                    "end_date": str(recent_df.index[-1]),
                    "current_price": float(recent_df['close'].iloc[-1])
                }
            },
            "key_metrics": {
                "all_time_high": float(lifetime_df['high'].max()),
                "all_time_low": float(lifetime_df['low'].min()),
                "current_vs_ath": ((market_context.current_price / lifetime_df['high'].max()) - 1) * 100,
                "volatility_30d": float(market_context.volatility_30d),
                "volume_24h": float(market_context.volume_24h)
            }
        }
        
        # Save summary
        with open('bitcoin_data_summary.json', 'w') as f:
            json.dump(summary_stats, f, indent=2)
        
        print(f"✅ Summary Statistics saved to: bitcoin_data_summary.json")
        
        # Display key insights
        print("\n🎯 Key Insights:")
        print(f"   All-Time High: ${summary_stats['key_metrics']['all_time_high']:,.2f}")
        print(f"   All-Time Low: ${summary_stats['key_metrics']['all_time_low']:,.2f}")
        print(f"   Current vs ATH: {summary_stats['key_metrics']['current_vs_ath']:+.1f}%")
        print(f"   30d Volatility: {summary_stats['key_metrics']['volatility_30d']:.1f}%")
        
        print("\n✅ Bitcoin Historical Data Test Completed Successfully!")
        print("📁 Files Created:")
        print("   - bitcoin_market_context.json (Market analysis)")
        print("   - bitcoin_lifetime_data.csv (Full historical data)")
        print("   - bitcoin_recent_hourly.csv (Recent hourly data)")
        print("   - bitcoin_data_summary.json (Summary statistics)")
        
    except Exception as e:
        print(f"❌ Error during test: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_bitcoin_historical_data())
