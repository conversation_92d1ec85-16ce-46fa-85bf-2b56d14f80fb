"""
Chart Reader Utility
Advanced chart reading and data extraction utilities
"""

import cv2
import numpy as np
from typing import List, Tuple, Dict, Any
import logging

logger = logging.getLogger(__name__)

class ChartReader:
    def __init__(self):
        self.chart_area = None
        self.price_axis = None
        self.time_axis = None
        
    def detect_chart_elements(self, image: np.ndarray) -> Dict[str, Any]:
        """
        Detect and locate chart elements like axes, grid lines, etc.
        """
        try:
            # Convert to grayscale
            gray = cv2.cvtColor(image, cv2.COLOR_RGB2GRAY)
            
            # Detect edges
            edges = cv2.Canny(gray, 50, 150, apertureSize=3)
            
            # Detect lines using Hough transform
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=100, 
                                   minLineLength=50, maxLineGap=10)
            
            # Classify lines as horizontal or vertical
            horizontal_lines = []
            vertical_lines = []
            
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    
                    # Calculate angle
                    angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi
                    
                    if abs(angle) < 10 or abs(angle) > 170:  # Horizontal
                        horizontal_lines.append(line[0])
                    elif abs(angle - 90) < 10 or abs(angle + 90) < 10:  # Vertical
                        vertical_lines.append(line[0])
            
            return {
                'horizontal_lines': horizontal_lines,
                'vertical_lines': vertical_lines,
                'chart_area': self._find_chart_area(horizontal_lines, vertical_lines, image.shape),
                'grid_detected': len(horizontal_lines) > 3 and len(vertical_lines) > 3
            }
            
        except Exception as e:
            logger.error(f"Error detecting chart elements: {str(e)}")
            return {}
    
    def _find_chart_area(self, h_lines: List, v_lines: List, image_shape: Tuple) -> Dict[str, int]:
        """
        Find the main chart area based on detected lines
        """
        height, width = image_shape[:2]
        
        if not h_lines or not v_lines:
            # Fallback to center 80% of image
            margin_x, margin_y = int(width * 0.1), int(height * 0.1)
            return {
                'x': margin_x,
                'y': margin_y,
                'width': width - 2 * margin_x,
                'height': height - 2 * margin_y
            }
        
        # Find bounding box of chart area
        min_x = min([min(line[0], line[2]) for line in v_lines])
        max_x = max([max(line[0], line[2]) for line in v_lines])
        min_y = min([min(line[1], line[3]) for line in h_lines])
        max_y = max([max(line[1], line[3]) for line in h_lines])
        
        return {
            'x': max(0, min_x),
            'y': max(0, min_y),
            'width': min(width, max_x - min_x),
            'height': min(height, max_y - min_y)
        }
    
    def extract_candlestick_data(self, image: np.ndarray, chart_area: Dict[str, int]) -> List[Dict]:
        """
        Extract candlestick data from chart image
        """
        try:
            # Crop to chart area
            x, y, w, h = chart_area['x'], chart_area['y'], chart_area['width'], chart_area['height']
            chart_roi = image[y:y+h, x:x+w]
            
            # Convert to HSV for better color detection
            hsv = cv2.cvtColor(chart_roi, cv2.COLOR_RGB2HSV)
            
            # Define color ranges for green and red candles
            green_lower = np.array([40, 50, 50])
            green_upper = np.array([80, 255, 255])
            red_lower = np.array([0, 50, 50])
            red_upper = np.array([20, 255, 255])
            
            # Create masks
            green_mask = cv2.inRange(hsv, green_lower, green_upper)
            red_mask = cv2.inRange(hsv, red_lower, red_upper)
            
            # Find contours
            green_contours, _ = cv2.findContours(green_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            red_contours, _ = cv2.findContours(red_mask, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            candlesticks = []
            
            # Process green candles
            for contour in green_contours:
                if cv2.contourArea(contour) > 10:  # Filter small noise
                    x, y, w, h = cv2.boundingRect(contour)
                    candlesticks.append({
                        'x': x,
                        'y': y,
                        'width': w,
                        'height': h,
                        'color': 'green',
                        'type': 'bullish'
                    })
            
            # Process red candles
            for contour in red_contours:
                if cv2.contourArea(contour) > 10:
                    x, y, w, h = cv2.boundingRect(contour)
                    candlesticks.append({
                        'x': x,
                        'y': y,
                        'width': w,
                        'height': h,
                        'color': 'red',
                        'type': 'bearish'
                    })
            
            # Sort by x position (time)
            candlesticks.sort(key=lambda c: c['x'])
            
            return candlesticks
            
        except Exception as e:
            logger.error(f"Error extracting candlestick data: {str(e)}")
            return []
    
    def detect_price_levels(self, image: np.ndarray, chart_area: Dict[str, int]) -> Dict[str, List[float]]:
        """
        Detect support and resistance levels from chart
        """
        try:
            # This is a simplified implementation
            # In practice, you would use more sophisticated algorithms
            
            x, y, w, h = chart_area['x'], chart_area['y'], chart_area['width'], chart_area['height']
            chart_roi = image[y:y+h, x:x+w]
            
            # Convert to grayscale
            gray = cv2.cvtColor(chart_roi, cv2.COLOR_RGB2GRAY)
            
            # Find horizontal lines (potential support/resistance)
            edges = cv2.Canny(gray, 50, 150)
            lines = cv2.HoughLinesP(edges, 1, np.pi/180, threshold=50, 
                                   minLineLength=w//4, maxLineGap=10)
            
            support_levels = []
            resistance_levels = []
            
            if lines is not None:
                for line in lines:
                    x1, y1, x2, y2 = line[0]
                    
                    # Check if line is roughly horizontal
                    angle = np.arctan2(y2 - y1, x2 - x1) * 180 / np.pi
                    if abs(angle) < 5:  # Nearly horizontal
                        y_pos = (y1 + y2) / 2
                        
                        # Classify as support or resistance based on position
                        if y_pos > h * 0.6:  # Lower part of chart
                            support_levels.append(y_pos)
                        elif y_pos < h * 0.4:  # Upper part of chart
                            resistance_levels.append(y_pos)
            
            return {
                'support': sorted(set(support_levels)),
                'resistance': sorted(set(resistance_levels), reverse=True)
            }
            
        except Exception as e:
            logger.error(f"Error detecting price levels: {str(e)}")
            return {'support': [], 'resistance': []}
    
    def extract_volume_data(self, image: np.ndarray, chart_area: Dict[str, int]) -> List[Dict]:
        """
        Extract volume bar data from chart
        """
        try:
            # Look for volume bars typically at the bottom of the chart
            x, y, w, h = chart_area['x'], chart_area['y'], chart_area['width'], chart_area['height']
            
            # Volume area is typically in the bottom 20% of the chart
            volume_y = y + int(h * 0.8)
            volume_h = int(h * 0.2)
            volume_roi = image[volume_y:volume_y+volume_h, x:x+w]
            
            # Convert to grayscale
            gray = cv2.cvtColor(volume_roi, cv2.COLOR_RGB2GRAY)
            
            # Threshold to find volume bars
            _, thresh = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY + cv2.THRESH_OTSU)
            
            # Find contours
            contours, _ = cv2.findContours(thresh, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
            
            volume_bars = []
            for contour in contours:
                if cv2.contourArea(contour) > 20:  # Filter small noise
                    x_bar, y_bar, w_bar, h_bar = cv2.boundingRect(contour)
                    volume_bars.append({
                        'x': x_bar,
                        'height': h_bar,
                        'relative_volume': h_bar / volume_h  # Normalized volume
                    })
            
            # Sort by x position
            volume_bars.sort(key=lambda v: v['x'])
            
            return volume_bars
            
        except Exception as e:
            logger.error(f"Error extracting volume data: {str(e)}")
            return []
