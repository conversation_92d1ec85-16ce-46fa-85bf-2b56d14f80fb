"""
Create a test chart image for demonstrating the AI analysis
"""

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
import random

def create_test_chart():
    """Create a realistic-looking crypto chart"""
    
    # Generate sample price data
    dates = pd.date_range(start='2024-01-01', periods=50, freq='H')
    
    # Generate realistic price movement
    base_price = 50000
    prices = [base_price]
    
    for i in range(49):
        # Random walk with some trend
        change = random.uniform(-0.02, 0.03)  # Slight bullish bias
        new_price = prices[-1] * (1 + change)
        prices.append(new_price)
    
    # Create OHLC data
    data = []
    for i, price in enumerate(prices):
        volatility = price * 0.015  # 1.5% volatility
        high = price + random.uniform(0, volatility)
        low = price - random.uniform(0, volatility)
        
        if random.random() > 0.5:  # Bullish candle
            open_price = low + (high - low) * random.uniform(0.1, 0.4)
            close_price = low + (high - low) * random.uniform(0.6, 0.9)
        else:  # Bearish candle
            open_price = low + (high - low) * random.uniform(0.6, 0.9)
            close_price = low + (high - low) * random.uniform(0.1, 0.4)
        
        volume = random.randint(800, 1500)
        
        data.append({
            'Date': dates[i],
            'Open': open_price,
            'High': high,
            'Low': low,
            'Close': close_price,
            'Volume': volume
        })
    
    df = pd.DataFrame(data)
    
    # Create the chart
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8), 
                                   gridspec_kw={'height_ratios': [3, 1]})
    
    # Price chart
    for i, row in df.iterrows():
        color = 'green' if row['Close'] > row['Open'] else 'red'
        
        # Draw candlestick
        ax1.plot([i, i], [row['Low'], row['High']], color='black', linewidth=1)
        
        body_height = abs(row['Close'] - row['Open'])
        body_bottom = min(row['Open'], row['Close'])
        
        rect = plt.Rectangle((i-0.3, body_bottom), 0.6, body_height, 
                           facecolor=color, edgecolor='black', linewidth=0.5)
        ax1.add_patch(rect)
    
    # Add moving averages
    df['MA20'] = df['Close'].rolling(window=20).mean()
    df['MA50'] = df['Close'].rolling(window=min(50, len(df))).mean()
    
    ax1.plot(df.index, df['MA20'], color='blue', linewidth=1, label='MA20', alpha=0.7)
    ax1.plot(df.index, df['MA50'], color='orange', linewidth=1, label='MA50', alpha=0.7)
    
    ax1.set_title('BTC/USD - 1H Chart', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Price (USD)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # Format y-axis to show prices
    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x:,.0f}'))
    
    # Volume chart
    colors = ['green' if df.iloc[i]['Close'] > df.iloc[i]['Open'] else 'red' 
              for i in range(len(df))]
    ax2.bar(df.index, df['Volume'], color=colors, alpha=0.7)
    ax2.set_ylabel('Volume', fontsize=12)
    ax2.set_xlabel('Time', fontsize=12)
    ax2.grid(True, alpha=0.3)
    
    # Add some technical indicators text
    current_price = df['Close'].iloc[-1]
    price_change = df['Close'].iloc[-1] - df['Close'].iloc[-2]
    price_change_pct = (price_change / df['Close'].iloc[-2]) * 100
    
    info_text = f"Current: ${current_price:,.2f}\n"
    info_text += f"Change: {price_change:+.2f} ({price_change_pct:+.2f}%)\n"
    info_text += f"24h High: ${df['High'].tail(24).max():,.2f}\n"
    info_text += f"24h Low: ${df['Low'].tail(24).min():,.2f}"
    
    ax1.text(0.02, 0.98, info_text, transform=ax1.transAxes, 
             verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.8))
    
    plt.tight_layout()
    plt.savefig('uploads/test_chart.png', dpi=150, bbox_inches='tight')
    plt.close()
    
    print("Test chart created: uploads/test_chart.png")
    return df

if __name__ == "__main__":
    # Create uploads directory if it doesn't exist
    import os
    os.makedirs('uploads', exist_ok=True)
    
    # Generate test chart
    df = create_test_chart()
    print(f"Generated chart with {len(df)} data points")
    print(f"Price range: ${df['Low'].min():,.2f} - ${df['High'].max():,.2f}")
