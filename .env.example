# Backend Configuration
PYTHONPATH=/app
ENVIRONMENT=development
DEBUG=true

# Database Configuration (Optional)
DATABASE_URL=****************************************************/chart_analysis

# Redis Configuration (Optional)
REDIS_URL=redis://redis:6379/0

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# File Upload Configuration
MAX_FILE_SIZE=10485760  # 10MB in bytes
UPLOAD_DIR=uploads
ALLOWED_EXTENSIONS=jpg,jpeg,png,gif,bmp,webp

# AI/ML Configuration
MODEL_PATH=ml_models
CONFIDENCE_THRESHOLD=0.6

# AI Vision API Keys (for accurate chart reading)
ANTHROPIC_API_KEY=your_claude_api_key_here
OPENAI_API_KEY=your_openai_api_key_here

# External API Keys (Optional)
COINMARKETCAP_API_KEY=your_api_key_here
ALPHA_VANTAGE_API_KEY=your_api_key_here

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=logs/app.log

# Security Configuration
SECRET_KEY=your_secret_key_here
ACCESS_TOKEN_EXPIRE_MINUTES=30
