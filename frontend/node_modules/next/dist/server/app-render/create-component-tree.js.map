{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "sourcesContent": ["import type { CacheNodeSeedData, PreloadCallbacks } from './types'\nimport React from 'react'\nimport {\n  isClientReference,\n  isUseCacheFunction,\n} from '../../lib/client-and-server-references'\nimport { getLayoutOrPageModule } from '../lib/app-dir-module'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport { interopDefault } from './interop-default'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport type { AppRenderContext, GetDynamicParamFromSegment } from './app-render'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { getLayerAssets } from './get-layer-assets'\nimport { hasLoadingComponentInTree } from './has-loading-component-in-tree'\nimport { validateRevalidate } from '../lib/patch-fetch'\nimport { PARALLEL_ROUTE_DEFAULT_PATH } from '../../client/components/parallel-route-default'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NextNodeServerSpan } from '../lib/trace/constants'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport type { LoadingModuleData } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { Params } from '../request/params'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { OUTLET_BOUNDARY_NAME } from '../../lib/metadata/metadata-constants'\nimport type { UseCachePageComponentProps } from '../use-cache/use-cache-wrapper'\n\n/**\n * Use the provided loader tree to create the React Component tree.\n */\nexport function createComponentTree(props: {\n  loaderTree: LoaderTree\n  parentParams: Params\n  rootLayoutIncluded: boolean\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  getMetadataReady: () => Promise<void>\n  getViewportReady: () => Promise<void>\n  ctx: AppRenderContext\n  missingSlots?: Set<string>\n  preloadCallbacks: PreloadCallbacks\n  authInterrupts: boolean\n  StreamingMetadataOutlet: React.ComponentType\n}): Promise<CacheNodeSeedData> {\n  return getTracer().trace(\n    NextNodeServerSpan.createComponentTree,\n    {\n      spanName: 'build component tree',\n    },\n    () => createComponentTreeInternal(props)\n  )\n}\n\nfunction errorMissingDefaultExport(\n  pagePath: string,\n  convention: string\n): never {\n  const normalizedPagePath = pagePath === '/' ? '' : pagePath\n  throw new Error(\n    `The default export is not a React Component in \"${normalizedPagePath}/${convention}\"`\n  )\n}\n\nconst cacheNodeKey = 'c'\n\nasync function createComponentTreeInternal({\n  loaderTree: tree,\n  parentParams,\n  rootLayoutIncluded,\n  injectedCSS,\n  injectedJS,\n  injectedFontPreloadTags,\n  getViewportReady,\n  getMetadataReady,\n  ctx,\n  missingSlots,\n  preloadCallbacks,\n  authInterrupts,\n  StreamingMetadataOutlet,\n}: {\n  loaderTree: LoaderTree\n  parentParams: Params\n  rootLayoutIncluded: boolean\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  getViewportReady: () => Promise<void>\n  getMetadataReady: () => Promise<void>\n  ctx: AppRenderContext\n  missingSlots?: Set<string>\n  preloadCallbacks: PreloadCallbacks\n  authInterrupts: boolean\n  StreamingMetadataOutlet: React.ComponentType | null\n}): Promise<CacheNodeSeedData> {\n  const {\n    renderOpts: { nextConfigOutput, experimental },\n    workStore,\n    componentMod: {\n      HTTPAccessFallbackBoundary,\n      LayoutRouter,\n      RenderFromTemplateContext,\n      OutletBoundary,\n      ClientPageRoot,\n      ClientSegmentRoot,\n      createServerSearchParamsForServerPage,\n      createPrerenderSearchParamsForClientPage,\n      createServerParamsForServerSegment,\n      createPrerenderParamsForClientSegment,\n      serverHooks: { DynamicServerError },\n      Postpone,\n    },\n    pagePath,\n    getDynamicParamFromSegment,\n    isPrefetch,\n    query,\n  } = ctx\n\n  const { page, layoutOrPagePath, segment, modules, parallelRoutes } =\n    parseLoaderTree(tree)\n\n  const {\n    layout,\n    template,\n    error,\n    loading,\n    'not-found': notFound,\n    forbidden,\n    unauthorized,\n  } = modules\n\n  const injectedCSSWithCurrentLayout = new Set(injectedCSS)\n  const injectedJSWithCurrentLayout = new Set(injectedJS)\n  const injectedFontPreloadTagsWithCurrentLayout = new Set(\n    injectedFontPreloadTags\n  )\n\n  const layerAssets = getLayerAssets({\n    preloadCallbacks,\n    ctx,\n    layoutOrPagePath,\n    injectedCSS: injectedCSSWithCurrentLayout,\n    injectedJS: injectedJSWithCurrentLayout,\n    injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n  })\n\n  const [Template, templateStyles, templateScripts] = template\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: template[1],\n        getComponent: template[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : [React.Fragment]\n\n  const [ErrorComponent, errorStyles, errorScripts] = error\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: error[1],\n        getComponent: error[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Loading, loadingStyles, loadingScripts] = loading\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: loading[1],\n        getComponent: loading[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const isLayout = typeof layout !== 'undefined'\n  const isPage = typeof page !== 'undefined'\n  const { mod: layoutOrPageMod, modType } = await getTracer().trace(\n    NextNodeServerSpan.getLayoutOrPageModule,\n    {\n      hideSpan: !(isLayout || isPage),\n      spanName: 'resolve segment modules',\n      attributes: {\n        'next.segment': segment,\n      },\n    },\n    () => getLayoutOrPageModule(tree)\n  )\n\n  /**\n   * Checks if the current segment is a root layout.\n   */\n  const rootLayoutAtThisLevel = isLayout && !rootLayoutIncluded\n  /**\n   * Checks if the current segment or any level above it has a root layout.\n   */\n  const rootLayoutIncludedAtThisLevelOrAbove =\n    rootLayoutIncluded || rootLayoutAtThisLevel\n\n  const [NotFound, notFoundStyles] = notFound\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: notFound[1],\n        getComponent: notFound[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Forbidden, forbiddenStyles] =\n    authInterrupts && forbidden\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: forbidden[1],\n          getComponent: forbidden[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  const [Unauthorized, unauthorizedStyles] =\n    authInterrupts && unauthorized\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: unauthorized[1],\n          getComponent: unauthorized[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  let dynamic = layoutOrPageMod?.dynamic\n\n  if (nextConfigOutput === 'export') {\n    if (!dynamic || dynamic === 'auto') {\n      dynamic = 'error'\n    } else if (dynamic === 'force-dynamic') {\n      // force-dynamic is always incompatible with 'export'. We must interrupt the build\n      throw new StaticGenBailoutError(\n        `Page with \\`dynamic = \"force-dynamic\"\\` couldn't be exported. \\`output: \"export\"\\` requires all pages be renderable statically because there is no runtime server to dynamically render routes in this output format. Learn more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports`\n      )\n    }\n  }\n\n  if (typeof dynamic === 'string') {\n    // the nested most config wins so we only force-static\n    // if it's configured above any parent that configured\n    // otherwise\n    if (dynamic === 'error') {\n      workStore.dynamicShouldError = true\n    } else if (dynamic === 'force-dynamic') {\n      workStore.forceDynamic = true\n\n      // TODO: (PPR) remove this bailout once PPR is the default\n      if (workStore.isStaticGeneration && !experimental.isRoutePPREnabled) {\n        // If the postpone API isn't available, we can't postpone the render and\n        // therefore we can't use the dynamic API.\n        const err = new DynamicServerError(\n          `Page with \\`dynamic = \"force-dynamic\"\\` won't be rendered statically.`\n        )\n        workStore.dynamicUsageDescription = err.message\n        workStore.dynamicUsageStack = err.stack\n        throw err\n      }\n    } else {\n      workStore.dynamicShouldError = false\n      workStore.forceStatic = dynamic === 'force-static'\n    }\n  }\n\n  if (typeof layoutOrPageMod?.fetchCache === 'string') {\n    workStore.fetchCache = layoutOrPageMod?.fetchCache\n  }\n\n  if (typeof layoutOrPageMod?.revalidate !== 'undefined') {\n    validateRevalidate(layoutOrPageMod?.revalidate, workStore.route)\n  }\n\n  if (typeof layoutOrPageMod?.revalidate === 'number') {\n    const defaultRevalidate = layoutOrPageMod.revalidate as number\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    if (workUnitStore) {\n      if (\n        workUnitStore.type === 'prerender' ||\n        workUnitStore.type === 'prerender-legacy' ||\n        workUnitStore.type === 'prerender-ppr' ||\n        workUnitStore.type === 'cache'\n      ) {\n        if (workUnitStore.revalidate > defaultRevalidate) {\n          workUnitStore.revalidate = defaultRevalidate\n        }\n      }\n    }\n\n    if (\n      !workStore.forceStatic &&\n      workStore.isStaticGeneration &&\n      defaultRevalidate === 0 &&\n      // If the postpone API isn't available, we can't postpone the render and\n      // therefore we can't use the dynamic API.\n      !experimental.isRoutePPREnabled\n    ) {\n      const dynamicUsageDescription = `revalidate: 0 configured ${segment}`\n      workStore.dynamicUsageDescription = dynamicUsageDescription\n\n      throw new DynamicServerError(dynamicUsageDescription)\n    }\n  }\n\n  const isStaticGeneration = workStore.isStaticGeneration\n\n  // Assume the segment we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // It's OK for this to be `true` when the data is actually fully static, but\n  // it's not OK for this to be `false` when the data possibly contains holes.\n  // Although the value here is overly pessimistic, for prefetches, it will be\n  // replaced by a more specific value when the data is later processed into\n  // per-segment responses (see collect-segment-data.tsx)\n  //\n  // For dynamic requests, this must always be `false` because dynamic responses\n  // are never partial.\n  const isPossiblyPartialResponse =\n    isStaticGeneration && experimental.isRoutePPREnabled === true\n\n  const LayoutOrPage: React.ComponentType<any> | undefined = layoutOrPageMod\n    ? interopDefault(layoutOrPageMod)\n    : undefined\n\n  /**\n   * The React Component to render.\n   */\n  let MaybeComponent = LayoutOrPage\n\n  if (process.env.NODE_ENV === 'development') {\n    const { isValidElementType } = require('next/dist/compiled/react-is')\n    if (\n      typeof MaybeComponent !== 'undefined' &&\n      !isValidElementType(MaybeComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, modType ?? 'page')\n    }\n\n    if (\n      typeof ErrorComponent !== 'undefined' &&\n      !isValidElementType(ErrorComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, 'error')\n    }\n\n    if (typeof Loading !== 'undefined' && !isValidElementType(Loading)) {\n      errorMissingDefaultExport(pagePath, 'loading')\n    }\n\n    if (typeof NotFound !== 'undefined' && !isValidElementType(NotFound)) {\n      errorMissingDefaultExport(pagePath, 'not-found')\n    }\n\n    if (typeof Forbidden !== 'undefined' && !isValidElementType(Forbidden)) {\n      errorMissingDefaultExport(pagePath, 'forbidden')\n    }\n\n    if (\n      typeof Unauthorized !== 'undefined' &&\n      !isValidElementType(Unauthorized)\n    ) {\n      errorMissingDefaultExport(pagePath, 'unauthorized')\n    }\n  }\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  // Create object holding the parent params and current params\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  // Resolve the segment param\n  const actualSegment = segmentParam ? segmentParam.treeSegment : segment\n\n  // Use the same condition to render metadataOutlet as metadata\n  const metadataOutlet = StreamingMetadataOutlet ? (\n    <StreamingMetadataOutlet />\n  ) : undefined\n\n  const notFoundElement = NotFound ? (\n    <>\n      <NotFound />\n      {notFoundStyles}\n    </>\n  ) : undefined\n\n  const forbiddenElement = Forbidden ? (\n    <>\n      <Forbidden />\n      {forbiddenStyles}\n    </>\n  ) : undefined\n\n  const unauthorizedElement = Unauthorized ? (\n    <>\n      <Unauthorized />\n      {unauthorizedStyles}\n    </>\n  ) : undefined\n\n  // TODO: Combine this `map` traversal with the loop below that turns the array\n  // into an object.\n  const parallelRouteMap = await Promise.all(\n    Object.keys(parallelRoutes).map(\n      async (\n        parallelRouteKey\n      ): Promise<[string, React.ReactNode, CacheNodeSeedData | null]> => {\n        const isChildrenRouteKey = parallelRouteKey === 'children'\n        const parallelRoute = parallelRoutes[parallelRouteKey]\n\n        const notFoundComponent = isChildrenRouteKey\n          ? notFoundElement\n          : undefined\n\n        const forbiddenComponent = isChildrenRouteKey\n          ? forbiddenElement\n          : undefined\n\n        const unauthorizedComponent = isChildrenRouteKey\n          ? unauthorizedElement\n          : undefined\n\n        // if we're prefetching and that there's a Loading component, we bail out\n        // otherwise we keep rendering for the prefetch.\n        // We also want to bail out if there's no Loading component in the tree.\n        let childCacheNodeSeedData: CacheNodeSeedData | null = null\n\n        if (\n          // Before PPR, the way instant navigations work in Next.js is we\n          // prefetch everything up to the first route segment that defines a\n          // loading.tsx boundary. (We do the same if there's no loading\n          // boundary in the entire tree, because we don't want to prefetch too\n          // much) The rest of the tree is deferred until the actual navigation.\n          // It does not take into account whether the data is dynamic — even if\n          // the tree is completely static, it will still defer everything\n          // inside the loading boundary.\n          //\n          // This behavior predates PPR and is only relevant if the\n          // PPR flag is not enabled.\n          isPrefetch &&\n          (Loading || !hasLoadingComponentInTree(parallelRoute)) &&\n          // The approach with PPR is different — loading.tsx behaves like a\n          // regular Suspense boundary and has no special behavior.\n          //\n          // With PPR, we prefetch as deeply as possible, and only defer when\n          // dynamic data is accessed. If so, we only defer the nearest parent\n          // Suspense boundary of the dynamic data access, regardless of whether\n          // the boundary is defined by loading.tsx or a normal <Suspense>\n          // component in userspace.\n          //\n          // NOTE: In practice this usually means we'll end up prefetching more\n          // than we were before PPR, which may or may not be considered a\n          // performance regression by some apps. The plan is to address this\n          // before General Availability of PPR by introducing granular\n          // per-segment fetching, so we can reuse as much of the tree as\n          // possible during both prefetches and dynamic navigations. But during\n          // the beta period, we should be clear about this trade off in our\n          // communications.\n          !experimental.isRoutePPREnabled\n        ) {\n          // Don't prefetch this child. This will trigger a lazy fetch by the\n          // client router.\n        } else {\n          // Create the child component\n\n          if (process.env.NODE_ENV === 'development' && missingSlots) {\n            // When we detect the default fallback (which triggers a 404), we collect the missing slots\n            // to provide more helpful debug information during development mode.\n            const parsedTree = parseLoaderTree(parallelRoute)\n            if (\n              parsedTree.layoutOrPagePath?.endsWith(PARALLEL_ROUTE_DEFAULT_PATH)\n            ) {\n              missingSlots.add(parallelRouteKey)\n            }\n          }\n\n          const seedData = await createComponentTreeInternal({\n            loaderTree: parallelRoute,\n            parentParams: currentParams,\n            rootLayoutIncluded: rootLayoutIncludedAtThisLevelOrAbove,\n            injectedCSS: injectedCSSWithCurrentLayout,\n            injectedJS: injectedJSWithCurrentLayout,\n            injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n            // `getMetadataReady` and `getViewportReady` are used to conditionally throw. In the case of parallel routes we will have more than one page\n            // but we only want to throw on the first one.\n            getMetadataReady: isChildrenRouteKey\n              ? getMetadataReady\n              : () => Promise.resolve(),\n            getViewportReady: isChildrenRouteKey\n              ? getViewportReady\n              : () => Promise.resolve(),\n            ctx,\n            missingSlots,\n            preloadCallbacks,\n            authInterrupts,\n            // `StreamingMetadataOutlet` is used to conditionally throw. In the case of parallel routes we will have more than one page\n            // but we only want to throw on the first one.\n            StreamingMetadataOutlet: isChildrenRouteKey\n              ? StreamingMetadataOutlet\n              : null,\n          })\n\n          childCacheNodeSeedData = seedData\n        }\n\n        // This is turned back into an object below.\n        return [\n          parallelRouteKey,\n          <LayoutRouter\n            parallelRouterKey={parallelRouteKey}\n            // TODO-APP: Add test for loading returning `undefined`. This currently can't be tested as the `webdriver()` tab will wait for the full page to load before returning.\n            error={ErrorComponent}\n            errorStyles={errorStyles}\n            errorScripts={errorScripts}\n            template={\n              <Template>\n                <RenderFromTemplateContext />\n              </Template>\n            }\n            templateStyles={templateStyles}\n            templateScripts={templateScripts}\n            notFound={notFoundComponent}\n            forbidden={forbiddenComponent}\n            unauthorized={unauthorizedComponent}\n          />,\n          childCacheNodeSeedData,\n        ]\n      }\n    )\n  )\n\n  // Convert the parallel route map into an object after all promises have been resolved.\n  let parallelRouteProps: { [key: string]: React.ReactNode } = {}\n  let parallelRouteCacheNodeSeedData: {\n    [key: string]: CacheNodeSeedData | null\n  } = {}\n  for (const parallelRoute of parallelRouteMap) {\n    const [parallelRouteKey, parallelRouteProp, flightData] = parallelRoute\n    parallelRouteProps[parallelRouteKey] = parallelRouteProp\n    parallelRouteCacheNodeSeedData[parallelRouteKey] = flightData\n  }\n\n  const loadingData: LoadingModuleData = Loading\n    ? [<Loading key=\"l\" />, loadingStyles, loadingScripts]\n    : null\n\n  // When the segment does not have a layout or page we still have to add the layout router to ensure the path holds the loading component\n  if (!MaybeComponent) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {layerAssets}\n        {parallelRouteProps.children}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n\n  const Component = MaybeComponent\n  // If force-dynamic is used and the current render supports postponing, we\n  // replace it with a node that will postpone the render. This ensures that the\n  // postpone is invoked during the react render phase and not during the next\n  // render phase.\n  // @TODO this does not actually do what it seems like it would or should do. The idea is that\n  // if we are rendering in a force-dynamic mode and we can postpone we should only make the segments\n  // that ask for force-dynamic to be dynamic, allowing other segments to still prerender. However\n  // because this comes after the children traversal and the static generation store is mutated every segment\n  // along the parent path of a force-dynamic segment will hit this condition effectively making the entire\n  // render force-dynamic. We should refactor this function so that we can correctly track which segments\n  // need to be dynamic\n  if (\n    workStore.isStaticGeneration &&\n    workStore.forceDynamic &&\n    experimental.isRoutePPREnabled\n  ) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        <Postpone\n          reason='dynamic = \"force-dynamic\" was used'\n          route={workStore.route}\n        />\n        {layerAssets}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      true,\n    ]\n  }\n\n  const isClientComponent = isClientReference(layoutOrPageMod)\n\n  if (\n    process.env.NODE_ENV === 'development' &&\n    'params' in parallelRouteProps\n  ) {\n    // @TODO consider making this an error and running the check in build as well\n    console.error(\n      `\"params\" is a reserved prop in Layouts and Pages and cannot be used as the name of a parallel route in ${segment}`\n    )\n  }\n\n  if (isPage) {\n    const PageComponent = Component\n    // Assign searchParams to props if this is a page\n    let pageElement: React.ReactNode\n    if (isClientComponent) {\n      if (isStaticGeneration) {\n        const promiseOfParams = createPrerenderParamsForClientSegment(\n          currentParams,\n          workStore\n        )\n        const promiseOfSearchParams =\n          createPrerenderSearchParamsForClientPage(workStore)\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n            promises={[promiseOfSearchParams, promiseOfParams]}\n          />\n        )\n      } else {\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n          />\n        )\n      }\n    } else {\n      // If we are passing params to a server component Page we need to track\n      // their usage in case the current render mode tracks dynamic API usage.\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      // TODO(useCache): Should we use this trick also if dynamicIO is enabled,\n      // instead of relying on the searchParams being a hanging promise?\n      if (!experimental.dynamicIO && isUseCacheFunction(PageComponent)) {\n        const UseCachePageComponent: React.ComponentType<UseCachePageComponentProps> =\n          PageComponent\n\n        // The \"use cache\" wrapper takes care of converting this into an\n        // erroring search params promise when passing it to the original\n        // function.\n        const searchParams = Promise.resolve({})\n\n        pageElement = (\n          <UseCachePageComponent\n            params={params}\n            searchParams={searchParams}\n            $$isPageComponent\n          />\n        )\n      } else {\n        // If we are passing searchParams to a server component Page we need to\n        // track their usage in case the current render mode tracks dynamic API\n        // usage.\n        const searchParams = createServerSearchParamsForServerPage(\n          query,\n          workStore\n        )\n\n        pageElement = (\n          <PageComponent params={params} searchParams={searchParams} />\n        )\n      }\n    }\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {pageElement}\n        {layerAssets}\n        <OutletBoundary>\n          <MetadataOutlet ready={getViewportReady} />\n          {/* Blocking metadata outlet */}\n          <MetadataOutlet ready={getMetadataReady} />\n          {/* Streaming metadata outlet */}\n          {metadataOutlet}\n        </OutletBoundary>\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  } else {\n    const SegmentComponent = Component\n\n    const isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot =\n      rootLayoutAtThisLevel &&\n      'children' in parallelRoutes &&\n      Object.keys(parallelRoutes).length > 1\n\n    let segmentNode: React.ReactNode\n\n    if (isClientComponent) {\n      let clientSegment: React.ReactNode\n\n      if (isStaticGeneration) {\n        const promiseOfParams = createPrerenderParamsForClientSegment(\n          currentParams,\n          workStore\n        )\n\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n            promise={promiseOfParams}\n          />\n        )\n      } else {\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n          />\n        )\n      }\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        let notfoundClientSegment: React.ReactNode\n        let forbiddenClientSegment: React.ReactNode\n        let unauthorizedClientSegment: React.ReactNode\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        notfoundClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: NotFound,\n          errorElement: notFoundElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        forbiddenClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Forbidden,\n          errorElement: forbiddenElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        unauthorizedClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Unauthorized,\n          errorElement: unauthorizedElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        if (\n          notfoundClientSegment ||\n          forbiddenClientSegment ||\n          unauthorizedClientSegment\n        ) {\n          segmentNode = (\n            <HTTPAccessFallbackBoundary\n              key={cacheNodeKey}\n              notFound={notfoundClientSegment}\n              forbidden={forbiddenClientSegment}\n              unauthorized={unauthorizedClientSegment}\n            >\n              {layerAssets}\n              {clientSegment}\n            </HTTPAccessFallbackBoundary>\n          )\n        } else {\n          segmentNode = (\n            <React.Fragment key={cacheNodeKey}>\n              {layerAssets}\n              {clientSegment}\n            </React.Fragment>\n          )\n        }\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {clientSegment}\n          </React.Fragment>\n        )\n      }\n    } else {\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      let serverSegment = (\n        <SegmentComponent {...parallelRouteProps} params={params} />\n      )\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        segmentNode = (\n          <HTTPAccessFallbackBoundary\n            key={cacheNodeKey}\n            notFound={\n              NotFound ? (\n                <>\n                  {layerAssets}\n                  <SegmentComponent params={params}>\n                    {notFoundStyles}\n                    <NotFound />\n                  </SegmentComponent>\n                </>\n              ) : undefined\n            }\n          >\n            {layerAssets}\n            {serverSegment}\n          </HTTPAccessFallbackBoundary>\n        )\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {serverSegment}\n          </React.Fragment>\n        )\n      }\n    }\n    // For layouts we just render the component\n    return [\n      actualSegment,\n      segmentNode,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n}\n\nasync function MetadataOutlet({\n  ready,\n}: {\n  ready: () => Promise<void> & { status?: string; value?: unknown }\n}) {\n  const r = ready()\n  // We can avoid a extra microtask by unwrapping the instrumented promise directly if available.\n  if (r.status === 'rejected') {\n    throw r.value\n  } else if (r.status !== 'fulfilled') {\n    await r\n  }\n  return null\n}\nMetadataOutlet.displayName = OUTLET_BOUNDARY_NAME\n\nfunction createErrorBoundaryClientSegmentRoot({\n  ErrorBoundaryComponent,\n  errorElement,\n  ClientSegmentRoot,\n  layerAssets,\n  SegmentComponent,\n  currentParams,\n}: {\n  ErrorBoundaryComponent: React.ComponentType<any> | undefined\n  errorElement: React.ReactNode\n  ClientSegmentRoot: React.ComponentType<any>\n  layerAssets: React.ReactNode\n  SegmentComponent: React.ComponentType<any>\n  currentParams: Params\n}) {\n  if (ErrorBoundaryComponent) {\n    const notFoundParallelRouteProps = {\n      children: errorElement,\n    }\n    return (\n      <>\n        {layerAssets}\n        <ClientSegmentRoot\n          Component={SegmentComponent}\n          slots={notFoundParallelRouteProps}\n          params={currentParams}\n        />\n      </>\n    )\n  }\n  return null\n}\n\nexport function getRootParams(\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  return getRootParamsImpl({}, loaderTree, getDynamicParamFromSegment)\n}\n\nfunction getRootParamsImpl(\n  parentParams: Params,\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  const {\n    segment,\n    modules: { layout },\n    parallelRoutes,\n  } = parseLoaderTree(loaderTree)\n\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const isRootLayout = typeof layout !== 'undefined'\n\n  if (isRootLayout) {\n    return currentParams\n  } else if (!parallelRoutes.children) {\n    // This should really be an error but there are bugs in Turbopack that cause\n    // the _not-found LoaderTree to not have any layouts. For rootParams sake\n    // this is somewhat irrelevant when you are not customizing the 404 page.\n    // If you are customizing 404\n    // TODO update rootParams to make all params optional if `/app/not-found.tsx` is defined\n    return currentParams\n  } else {\n    return getRootParamsImpl(\n      currentParams,\n      // We stop looking for root params as soon as we hit the first layout\n      // and it is not possible to use parallel route children above the root layout\n      // so every parallelRoutes object that this function can visit will necessarily\n      // have a single `children` prop and no others.\n      parallelRoutes.children,\n      getDynamicParamFromSegment\n    )\n  }\n}\n"], "names": ["createComponentTree", "getRootParams", "props", "getTracer", "trace", "NextNodeServerSpan", "spanName", "createComponentTreeInternal", "errorMissingDefaultExport", "pagePath", "convention", "normalizedPagePath", "Error", "cacheNodeKey", "loaderTree", "tree", "parentParams", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "getViewportReady", "getMetadataReady", "ctx", "missingSlots", "preloadCallbacks", "authInterrupts", "StreamingMetadataOutlet", "renderOpts", "nextConfigOutput", "experimental", "workStore", "componentMod", "HTTPAccessFallbackBoundary", "LayoutRouter", "RenderFromTemplateContext", "OutletBoundary", "ClientPageRoot", "ClientSegmentRoot", "createServerSearchParamsForServerPage", "createPrerenderSearchParamsForClientPage", "createServerParamsForServerSegment", "createPrerenderParamsForClientSegment", "serverHooks", "DynamicServerError", "Postpone", "getDynamicParamFromSegment", "isPrefetch", "query", "page", "layoutOrPagePath", "segment", "modules", "parallelRoutes", "parseLoaderTree", "layout", "template", "error", "loading", "notFound", "forbidden", "unauthorized", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "getLayerAssets", "Template", "templateStyles", "templateScripts", "createComponentStylesAndScripts", "filePath", "getComponent", "React", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "mod", "layoutOrPageMod", "modType", "getLayoutOrPageModule", "hideSpan", "attributes", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "Forbidden", "forbiddenStyles", "Unauthorized", "unauthorizedStyles", "dynamic", "StaticGenBailoutError", "dynamicShouldError", "forceDynamic", "isStaticGeneration", "isRoutePPREnabled", "err", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceStatic", "fetchCache", "revalidate", "validateRevalidate", "route", "defaultRevalidate", "workUnitStore", "workUnitAsyncStorage", "getStore", "type", "isPossiblyPartialResponse", "LayoutOrPage", "interopDefault", "undefined", "MaybeComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "metadataOutlet", "notFoundElement", "forbiddenElement", "unauthorizedElement", "parallelRouteMap", "Promise", "all", "Object", "keys", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "parallelRoute", "notFoundComponent", "forbiddenComponent", "unauthorizedComponent", "childCacheNodeSeedData", "hasLoadingComponentInTree", "parsedTree", "endsWith", "PARALLEL_ROUTE_DEFAULT_PATH", "add", "seedData", "resolve", "parallel<PERSON><PERSON>er<PERSON>ey", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "loadingData", "children", "Component", "reason", "isClientComponent", "isClientReference", "console", "PageComponent", "pageElement", "promiseOfParams", "promiseOfSearchParams", "searchParams", "params", "promises", "dynamicIO", "isUseCacheFunction", "UseCachePageComponent", "$$isPageComponent", "MetadataOutlet", "ready", "SegmentComponent", "isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot", "length", "segmentNode", "clientSegment", "slots", "promise", "notfoundClientSegment", "forbiddenClientSegment", "unauthorizedClientSegment", "createErrorBoundaryClientSegmentRoot", "ErrorBoundaryComponent", "errorElement", "serverSegment", "r", "status", "displayName", "OUTLET_BOUNDARY_NAME", "notFoundParallelRouteProps", "getRootParamsImpl", "isRootLayout"], "mappings": ";;;;;;;;;;;;;;;IA4BgBA,mBAAmB;eAAnBA;;IAk3BAC,aAAa;eAAbA;;;;8DA74BE;2CAIX;8BAC+B;gCAEP;iCACC;iDAEgB;gCACjB;2CACW;4BACP;sCACS;wBAClB;2BACS;yCACG;8CAGD;mCACA;;;;;;AAM9B,SAASD,oBAAoBE,KAcnC;IACC,OAAOC,IAAAA,iBAAS,IAAGC,KAAK,CACtBC,6BAAkB,CAACL,mBAAmB,EACtC;QACEM,UAAU;IACZ,GACA,IAAMC,4BAA4BL;AAEtC;AAEA,SAASM,0BACPC,QAAgB,EAChBC,UAAkB;IAElB,MAAMC,qBAAqBF,aAAa,MAAM,KAAKA;IACnD,MAAM,qBAEL,CAFK,IAAIG,MACR,CAAC,gDAAgD,EAAED,mBAAmB,CAAC,EAAED,WAAW,CAAC,CAAC,GADlF,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMG,eAAe;AAErB,eAAeN,4BAA4B,EACzCO,YAAYC,IAAI,EAChBC,YAAY,EACZC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,gBAAgB,EAChBC,gBAAgB,EAChBC,GAAG,EACHC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,uBAAuB,EAexB;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAE,EAC9CC,SAAS,EACTC,cAAc,EACZC,0BAA0B,EAC1BC,YAAY,EACZC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,qCAAqC,EACrCC,wCAAwC,EACxCC,kCAAkC,EAClCC,qCAAqC,EACrCC,aAAa,EAAEC,kBAAkB,EAAE,EACnCC,QAAQ,EACT,EACDpC,QAAQ,EACRqC,0BAA0B,EAC1BC,UAAU,EACVC,KAAK,EACN,GAAGzB;IAEJ,MAAM,EAAE0B,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,EAAE,GAChEC,IAAAA,gCAAe,EAACvC;IAElB,MAAM,EACJwC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACP,aAAaC,QAAQ,EACrBC,SAAS,EACTC,YAAY,EACb,GAAGT;IAEJ,MAAMU,+BAA+B,IAAIC,IAAI7C;IAC7C,MAAM8C,8BAA8B,IAAID,IAAI5C;IAC5C,MAAM8C,2CAA2C,IAAIF,IACnD3C;IAGF,MAAM8C,cAAcC,IAAAA,8BAAc,EAAC;QACjC1C;QACAF;QACA2B;QACAhC,aAAa4C;QACb3C,YAAY6C;QACZ5C,yBAAyB6C;IAC3B;IAEA,MAAM,CAACG,UAAUC,gBAAgBC,gBAAgB,GAAGd,WAChD,MAAMe,IAAAA,gEAA+B,EAAC;QACpChD;QACAiD,UAAUhB,QAAQ,CAAC,EAAE;QACrBiB,cAAcjB,QAAQ,CAAC,EAAE;QACzBtC,aAAa4C;QACb3C,YAAY6C;IACd,KACA;QAACU,cAAK,CAACC,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGrB,QAChD,MAAMc,IAAAA,gEAA+B,EAAC;QACpChD;QACAiD,UAAUf,KAAK,CAAC,EAAE;QAClBgB,cAAchB,KAAK,CAAC,EAAE;QACtBvC,aAAa4C;QACb3C,YAAY6C;IACd,KACA,EAAE;IAEN,MAAM,CAACe,SAASC,eAAeC,eAAe,GAAGvB,UAC7C,MAAMa,IAAAA,gEAA+B,EAAC;QACpChD;QACAiD,UAAUd,OAAO,CAAC,EAAE;QACpBe,cAAcf,OAAO,CAAC,EAAE;QACxBxC,aAAa4C;QACb3C,YAAY6C;IACd,KACA,EAAE;IAEN,MAAMkB,WAAW,OAAO3B,WAAW;IACnC,MAAM4B,SAAS,OAAOlC,SAAS;IAC/B,MAAM,EAAEmC,KAAKC,eAAe,EAAEC,OAAO,EAAE,GAAG,MAAMnF,IAAAA,iBAAS,IAAGC,KAAK,CAC/DC,6BAAkB,CAACkF,qBAAqB,EACxC;QACEC,UAAU,CAAEN,CAAAA,YAAYC,MAAK;QAC7B7E,UAAU;QACVmF,YAAY;YACV,gBAAgBtC;QAClB;IACF,GACA,IAAMoC,IAAAA,mCAAqB,EAACxE;IAG9B;;GAEC,GACD,MAAM2E,wBAAwBR,YAAY,CAACjE;IAC3C;;GAEC,GACD,MAAM0E,uCACJ1E,sBAAsByE;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAGlC,WAC/B,MAAMY,IAAAA,gEAA+B,EAAC;QACpChD;QACAiD,UAAUb,QAAQ,CAAC,EAAE;QACrBc,cAAcd,QAAQ,CAAC,EAAE;QACzBzC,aAAa4C;QACb3C,YAAY6C;IACd,KACA,EAAE;IAEN,MAAM,CAAC8B,WAAWC,gBAAgB,GAChCrE,kBAAkBkC,YACd,MAAMW,IAAAA,gEAA+B,EAAC;QACpChD;QACAiD,UAAUZ,SAAS,CAAC,EAAE;QACtBa,cAAcb,SAAS,CAAC,EAAE;QAC1B1C,aAAa4C;QACb3C,YAAY6C;IACd,KACA,EAAE;IAER,MAAM,CAACgC,cAAcC,mBAAmB,GACtCvE,kBAAkBmC,eACd,MAAMU,IAAAA,gEAA+B,EAAC;QACpChD;QACAiD,UAAUX,YAAY,CAAC,EAAE;QACzBY,cAAcZ,YAAY,CAAC,EAAE;QAC7B3C,aAAa4C;QACb3C,YAAY6C;IACd,KACA,EAAE;IAER,IAAIkC,UAAUb,mCAAAA,gBAAiBa,OAAO;IAEtC,IAAIrE,qBAAqB,UAAU;QACjC,IAAI,CAACqE,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC,kFAAkF;YAClF,MAAM,qBAEL,CAFK,IAAIC,8CAAqB,CAC7B,CAAC,gTAAgT,CAAC,GAD9S,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAI,OAAOD,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvBnE,UAAUqE,kBAAkB,GAAG;QACjC,OAAO,IAAIF,YAAY,iBAAiB;YACtCnE,UAAUsE,YAAY,GAAG;YAEzB,0DAA0D;YAC1D,IAAItE,UAAUuE,kBAAkB,IAAI,CAACxE,aAAayE,iBAAiB,EAAE;gBACnE,wEAAwE;gBACxE,0CAA0C;gBAC1C,MAAMC,MAAM,qBAEX,CAFW,IAAI5D,mBACd,CAAC,qEAAqE,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAb,UAAU0E,uBAAuB,GAAGD,IAAIE,OAAO;gBAC/C3E,UAAU4E,iBAAiB,GAAGH,IAAII,KAAK;gBACvC,MAAMJ;YACR;QACF,OAAO;YACLzE,UAAUqE,kBAAkB,GAAG;YAC/BrE,UAAU8E,WAAW,GAAGX,YAAY;QACtC;IACF;IAEA,IAAI,QAAOb,mCAAAA,gBAAiByB,UAAU,MAAK,UAAU;QACnD/E,UAAU+E,UAAU,GAAGzB,mCAAAA,gBAAiByB,UAAU;IACpD;IAEA,IAAI,QAAOzB,mCAAAA,gBAAiB0B,UAAU,MAAK,aAAa;QACtDC,IAAAA,8BAAkB,EAAC3B,mCAAAA,gBAAiB0B,UAAU,EAAEhF,UAAUkF,KAAK;IACjE;IAEA,IAAI,QAAO5B,mCAAAA,gBAAiB0B,UAAU,MAAK,UAAU;QACnD,MAAMG,oBAAoB7B,gBAAgB0B,UAAU;QAEpD,MAAMI,gBAAgBC,kDAAoB,CAACC,QAAQ;QAEnD,IAAIF,eAAe;YACjB,IACEA,cAAcG,IAAI,KAAK,eACvBH,cAAcG,IAAI,KAAK,sBACvBH,cAAcG,IAAI,KAAK,mBACvBH,cAAcG,IAAI,KAAK,SACvB;gBACA,IAAIH,cAAcJ,UAAU,GAAGG,mBAAmB;oBAChDC,cAAcJ,UAAU,GAAGG;gBAC7B;YACF;QACF;QAEA,IACE,CAACnF,UAAU8E,WAAW,IACtB9E,UAAUuE,kBAAkB,IAC5BY,sBAAsB,KACtB,wEAAwE;QACxE,0CAA0C;QAC1C,CAACpF,aAAayE,iBAAiB,EAC/B;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAEtD,SAAS;YACrEpB,UAAU0E,uBAAuB,GAAGA;YAEpC,MAAM,qBAA+C,CAA/C,IAAI7D,mBAAmB6D,0BAAvB,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;QACtD;IACF;IAEA,MAAMH,qBAAqBvE,UAAUuE,kBAAkB;IAEvD,0EAA0E;IAC1E,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IAC5E,0EAA0E;IAC1E,uDAAuD;IACvD,EAAE;IACF,8EAA8E;IAC9E,qBAAqB;IACrB,MAAMiB,4BACJjB,sBAAsBxE,aAAayE,iBAAiB,KAAK;IAE3D,MAAMiB,eAAqDnC,kBACvDoC,IAAAA,8BAAc,EAACpC,mBACfqC;IAEJ;;GAEC,GACD,IAAIC,iBAAiBH;IAErB,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,OAAOL,mBAAmB,eAC1B,CAACI,mBAAmBJ,iBACpB;YACAnH,0BAA0BC,UAAU6E,WAAW;QACjD;QAEA,IACE,OAAOV,mBAAmB,eAC1B,CAACmD,mBAAmBnD,iBACpB;YACApE,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOsE,YAAY,eAAe,CAACgD,mBAAmBhD,UAAU;YAClEvE,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOmF,aAAa,eAAe,CAACmC,mBAAmBnC,WAAW;YACpEpF,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOqF,cAAc,eAAe,CAACiC,mBAAmBjC,YAAY;YACtEtF,0BAA0BC,UAAU;QACtC;QAEA,IACE,OAAOuF,iBAAiB,eACxB,CAAC+B,mBAAmB/B,eACpB;YACAxF,0BAA0BC,UAAU;QACtC;IACF;IAEA,iCAAiC;IACjC,MAAMwH,eAAenF,2BAA2BK;IAEhD,6DAA6D;IAC7D,IAAI+E,gBAAwBlH;IAC5B,IAAIiH,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGlH,YAAY;YACf,CAACiH,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,4BAA4B;IAC5B,MAAME,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAGnF;IAEhE,8DAA8D;IAC9D,MAAMoF,iBAAiB5G,wCACrB,qBAACA,+BACC+F;IAEJ,MAAMc,kBAAkB5C,yBACtB;;0BACE,qBAACA;YACAC;;SAED6B;IAEJ,MAAMe,mBAAmB3C,0BACvB;;0BACE,qBAACA;YACAC;;SAED2B;IAEJ,MAAMgB,sBAAsB1C,6BAC1B;;0BACE,qBAACA;YACAC;;SAEDyB;IAEJ,8EAA8E;IAC9E,kBAAkB;IAClB,MAAMiB,mBAAmB,MAAMC,QAAQC,GAAG,CACxCC,OAAOC,IAAI,CAAC1F,gBAAgB2F,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,gBAAgB9F,cAAc,CAAC4F,iBAAiB;QAEtD,MAAMG,oBAAoBF,qBACtBV,kBACAd;QAEJ,MAAM2B,qBAAqBH,qBACvBT,mBACAf;QAEJ,MAAM4B,wBAAwBJ,qBAC1BR,sBACAhB;QAEJ,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAI6B,yBAAmD;QAEvD,IACE,gEAAgE;QAChE,mEAAmE;QACnE,8DAA8D;QAC9D,qEAAqE;QACrE,sEAAsE;QACtE,sEAAsE;QACtE,gEAAgE;QAChE,+BAA+B;QAC/B,EAAE;QACF,yDAAyD;QACzD,2BAA2B;QAC3BxG,cACCgC,CAAAA,WAAW,CAACyE,IAAAA,oDAAyB,EAACL,cAAa,KACpD,kEAAkE;QAClE,yDAAyD;QACzD,EAAE;QACF,mEAAmE;QACnE,oEAAoE;QACpE,sEAAsE;QACtE,gEAAgE;QAChE,0BAA0B;QAC1B,EAAE;QACF,qEAAqE;QACrE,gEAAgE;QAChE,mEAAmE;QACnE,6DAA6D;QAC7D,+DAA+D;QAC/D,sEAAsE;QACtE,kEAAkE;QAClE,kBAAkB;QAClB,CAACrH,aAAayE,iBAAiB,EAC/B;QACA,mEAAmE;QACnE,iBAAiB;QACnB,OAAO;YACL,6BAA6B;YAE7B,IAAIqB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiBtG,cAAc;oBAKxDiI;gBAJF,2FAA2F;gBAC3F,qEAAqE;gBACrE,MAAMA,aAAanG,IAAAA,gCAAe,EAAC6F;gBACnC,KACEM,+BAAAA,WAAWvG,gBAAgB,qBAA3BuG,6BAA6BC,QAAQ,CAACC,iDAA2B,GACjE;oBACAnI,aAAaoI,GAAG,CAACX;gBACnB;YACF;YAEA,MAAMY,WAAW,MAAMtJ,4BAA4B;gBACjDO,YAAYqI;gBACZnI,cAAckH;gBACdjH,oBAAoB0E;gBACpBzE,aAAa4C;gBACb3C,YAAY6C;gBACZ5C,yBAAyB6C;gBACzB,4IAA4I;gBAC5I,8CAA8C;gBAC9C3C,kBAAkB4H,qBACd5H,mBACA,IAAMsH,QAAQkB,OAAO;gBACzBzI,kBAAkB6H,qBACd7H,mBACA,IAAMuH,QAAQkB,OAAO;gBACzBvI;gBACAC;gBACAC;gBACAC;gBACA,2HAA2H;gBAC3H,8CAA8C;gBAC9CC,yBAAyBuH,qBACrBvH,0BACA;YACN;YAEA4H,yBAAyBM;QAC3B;QAEA,4CAA4C;QAC5C,OAAO;YACLZ;0BACA,qBAAC/G;gBACC6H,mBAAmBd;gBACnB,sKAAsK;gBACtKxF,OAAOmB;gBACPC,aAAaA;gBACbC,cAAcA;gBACdtB,wBACE,qBAACY;8BACC,cAAA,qBAACjC;;gBAGLkC,gBAAgBA;gBAChBC,iBAAiBA;gBACjBX,UAAUyF;gBACVxF,WAAWyF;gBACXxF,cAAcyF;;YAEhBC;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAIS,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAMd,iBAAiBR,iBAAkB;QAC5C,MAAM,CAACM,kBAAkBiB,mBAAmBC,WAAW,GAAGhB;QAC1Da,kBAAkB,CAACf,iBAAiB,GAAGiB;QACvCD,8BAA8B,CAAChB,iBAAiB,GAAGkB;IACrD;IAEA,MAAMC,cAAiCrF,UACnC;sBAAC,qBAACA,aAAY;QAAQC;QAAeC;KAAe,GACpD;IAEJ,wIAAwI;IACxI,IAAI,CAAC0C,gBAAgB;QACnB,OAAO;YACLU;0BACA,sBAAC3D,cAAK,CAACC,QAAQ;;oBACZT;oBACA8F,mBAAmBK,QAAQ;;eAFTxJ;YAIrBoJ;YACAG;YACA7C;SACD;IACH;IAEA,MAAM+C,YAAY3C;IAClB,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,6FAA6F;IAC7F,mGAAmG;IACnG,gGAAgG;IAChG,2GAA2G;IAC3G,yGAAyG;IACzG,uGAAuG;IACvG,qBAAqB;IACrB,IACE5F,UAAUuE,kBAAkB,IAC5BvE,UAAUsE,YAAY,IACtBvE,aAAayE,iBAAiB,EAC9B;QACA,OAAO;YACL8B;0BACA,sBAAC3D,cAAK,CAACC,QAAQ;;kCACb,qBAAC9B;wBACC0H,QAAO;wBACPtD,OAAOlF,UAAUkF,KAAK;;oBAEvB/C;;eALkBrD;YAOrBoJ;YACAG;YACA;SACD;IACH;IAEA,MAAMI,oBAAoBC,IAAAA,4CAAiB,EAACpF;IAE5C,IACEuC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,YAAYkC,oBACZ;QACA,6EAA6E;QAC7EU,QAAQjH,KAAK,CACX,CAAC,uGAAuG,EAAEN,SAAS;IAEvH;IAEA,IAAIgC,QAAQ;QACV,MAAMwF,gBAAgBL;QACtB,iDAAiD;QACjD,IAAIM;QACJ,IAAIJ,mBAAmB;YACrB,IAAIlE,oBAAoB;gBACtB,MAAMuE,kBAAkBnI,sCACtBwF,eACAnG;gBAEF,MAAM+I,wBACJtI,yCAAyCT;gBAC3C6I,4BACE,qBAACvI;oBACCiI,WAAWK;oBACXI,cAAc/H;oBACdgI,QAAQ9C;oBACR+C,UAAU;wBAACH;wBAAuBD;qBAAgB;;YAGxD,OAAO;gBACLD,4BACE,qBAACvI;oBACCiI,WAAWK;oBACXI,cAAc/H;oBACdgI,QAAQ9C;;YAGd;QACF,OAAO;YACL,uEAAuE;YACvE,wEAAwE;YACxE,MAAM8C,SAASvI,mCACbyF,eACAnG;YAGF,yEAAyE;YACzE,kEAAkE;YAClE,IAAI,CAACD,aAAaoJ,SAAS,IAAIC,IAAAA,6CAAkB,EAACR,gBAAgB;gBAChE,MAAMS,wBACJT;gBAEF,gEAAgE;gBAChE,iEAAiE;gBACjE,YAAY;gBACZ,MAAMI,eAAenC,QAAQkB,OAAO,CAAC,CAAC;gBAEtCc,4BACE,qBAACQ;oBACCJ,QAAQA;oBACRD,cAAcA;oBACdM,iBAAiB;;YAGvB,OAAO;gBACL,uEAAuE;gBACvE,uEAAuE;gBACvE,SAAS;gBACT,MAAMN,eAAexI,sCACnBS,OACAjB;gBAGF6I,4BACE,qBAACD;oBAAcK,QAAQA;oBAAQD,cAAcA;;YAEjD;QACF;QACA,OAAO;YACL1C;0BACA,sBAAC3D,cAAK,CAACC,QAAQ;;oBACZiG;oBACA1G;kCACD,sBAAC9B;;0CACC,qBAACkJ;gCAAeC,OAAOlK;;0CAEvB,qBAACiK;gCAAeC,OAAOjK;;4BAEtBiH;;;;eARgB1H;YAWrBoJ;YACAG;YACA7C;SACD;IACH,OAAO;QACL,MAAMiE,mBAAmBlB;QAEzB,MAAMmB,oDACJ/F,yBACA,cAAcrC,kBACdyF,OAAOC,IAAI,CAAC1F,gBAAgBqI,MAAM,GAAG;QAEvC,IAAIC;QAEJ,IAAInB,mBAAmB;YACrB,IAAIoB;YAEJ,IAAItF,oBAAoB;gBACtB,MAAMuE,kBAAkBnI,sCACtBwF,eACAnG;gBAGF6J,8BACE,qBAACtJ;oBACCgI,WAAWkB;oBACXK,OAAO7B;oBACPgB,QAAQ9C;oBACR4D,SAASjB;;YAGf,OAAO;gBACLe,8BACE,qBAACtJ;oBACCgI,WAAWkB;oBACXK,OAAO7B;oBACPgB,QAAQ9C;;YAGd;YAEA,IAAIuD,mDAAmD;gBACrD,IAAIM;gBACJ,IAAIC;gBACJ,IAAIC;gBACJ,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCF,wBAAwBG,qCAAqC;oBAC3DC,wBAAwBvG;oBACxBwG,cAAc5D;oBACdlG;oBACA4B;oBACAsH;oBACAtD;gBACF;gBACA8D,yBAAyBE,qCAAqC;oBAC5DC,wBAAwBrG;oBACxBsG,cAAc3D;oBACdnG;oBACA4B;oBACAsH;oBACAtD;gBACF;gBACA+D,4BAA4BC,qCAAqC;oBAC/DC,wBAAwBnG;oBACxBoG,cAAc1D;oBACdpG;oBACA4B;oBACAsH;oBACAtD;gBACF;gBACA,IACE6D,yBACAC,0BACAC,2BACA;oBACAN,4BACE,sBAAC1J;wBAEC0B,UAAUoI;wBACVnI,WAAWoI;wBACXnI,cAAcoI;;4BAEb/H;4BACA0H;;uBANI/K;gBASX,OAAO;oBACL8K,4BACE,sBAACjH,cAAK,CAACC,QAAQ;;4BACZT;4BACA0H;;uBAFkB/K;gBAKzB;YACF,OAAO;gBACL8K,4BACE,sBAACjH,cAAK,CAACC,QAAQ;;wBACZT;wBACA0H;;mBAFkB/K;YAKzB;QACF,OAAO;YACL,MAAMmK,SAASvI,mCACbyF,eACAnG;YAGF,IAAIsK,8BACF,qBAACb;gBAAkB,GAAGxB,kBAAkB;gBAAEgB,QAAQA;;YAGpD,IAAIS,mDAAmD;gBACrD,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCE,4BACE,sBAAC1J;oBAEC0B,UACEiC,yBACE;;4BACG1B;0CACD,sBAACsH;gCAAiBR,QAAQA;;oCACvBnF;kDACD,qBAACD;;;;yBAGH8B;;wBAGLxD;wBACAmI;;mBAdIxL;YAiBX,OAAO;gBACL8K,4BACE,sBAACjH,cAAK,CAACC,QAAQ;;wBACZT;wBACAmI;;mBAFkBxL;YAKzB;QACF;QACA,2CAA2C;QAC3C,OAAO;YACLwH;YACAsD;YACA1B;YACAG;YACA7C;SACD;IACH;AACF;AAEA,eAAe+D,eAAe,EAC5BC,KAAK,EAGN;IACC,MAAMe,IAAIf;IACV,+FAA+F;IAC/F,IAAIe,EAAEC,MAAM,KAAK,YAAY;QAC3B,MAAMD,EAAEnE,KAAK;IACf,OAAO,IAAImE,EAAEC,MAAM,KAAK,aAAa;QACnC,MAAMD;IACR;IACA,OAAO;AACT;AACAhB,eAAekB,WAAW,GAAGC,uCAAoB;AAEjD,SAASP,qCAAqC,EAC5CC,sBAAsB,EACtBC,YAAY,EACZ9J,iBAAiB,EACjB4B,WAAW,EACXsH,gBAAgB,EAChBtD,aAAa,EAQd;IACC,IAAIiE,wBAAwB;QAC1B,MAAMO,6BAA6B;YACjCrC,UAAU+B;QACZ;QACA,qBACE;;gBACGlI;8BACD,qBAAC5B;oBACCgI,WAAWkB;oBACXK,OAAOa;oBACP1B,QAAQ9C;;;;IAIhB;IACA,OAAO;AACT;AAEO,SAASjI,cACda,UAAsB,EACtBgC,0BAAsD;IAEtD,OAAO6J,kBAAkB,CAAC,GAAG7L,YAAYgC;AAC3C;AAEA,SAAS6J,kBACP3L,YAAoB,EACpBF,UAAsB,EACtBgC,0BAAsD;IAEtD,MAAM,EACJK,OAAO,EACPC,SAAS,EAAEG,MAAM,EAAE,EACnBF,cAAc,EACf,GAAGC,IAAAA,gCAAe,EAACxC;IAEpB,MAAMmH,eAAenF,2BAA2BK;IAEhD,IAAI+E,gBAAwBlH;IAC5B,IAAIiH,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGlH,YAAY;YACf,CAACiH,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAMyE,eAAe,OAAOrJ,WAAW;IAEvC,IAAIqJ,cAAc;QAChB,OAAO1E;IACT,OAAO,IAAI,CAAC7E,eAAegH,QAAQ,EAAE;QACnC,4EAA4E;QAC5E,yEAAyE;QACzE,yEAAyE;QACzE,6BAA6B;QAC7B,wFAAwF;QACxF,OAAOnC;IACT,OAAO;QACL,OAAOyE,kBACLzE,eACA,qEAAqE;QACrE,8EAA8E;QAC9E,+EAA+E;QAC/E,+CAA+C;QAC/C7E,eAAegH,QAAQ,EACvBvH;IAEJ;AACF"}