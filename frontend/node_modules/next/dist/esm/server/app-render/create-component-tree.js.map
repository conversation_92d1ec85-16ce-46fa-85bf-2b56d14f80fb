{"version": 3, "sources": ["../../../src/server/app-render/create-component-tree.tsx"], "sourcesContent": ["import type { CacheNodeSeedData, PreloadCallbacks } from './types'\nimport React from 'react'\nimport {\n  isClientReference,\n  isUseCacheFunction,\n} from '../../lib/client-and-server-references'\nimport { getLayoutOrPageModule } from '../lib/app-dir-module'\nimport type { LoaderTree } from '../lib/app-dir-module'\nimport { interopDefault } from './interop-default'\nimport { parseLoaderTree } from './parse-loader-tree'\nimport type { AppRenderContext, GetDynamicParamFromSegment } from './app-render'\nimport { createComponentStylesAndScripts } from './create-component-styles-and-scripts'\nimport { getLayerAssets } from './get-layer-assets'\nimport { hasLoadingComponentInTree } from './has-loading-component-in-tree'\nimport { validateRevalidate } from '../lib/patch-fetch'\nimport { PARALLEL_ROUTE_DEFAULT_PATH } from '../../client/components/parallel-route-default'\nimport { getTracer } from '../lib/trace/tracer'\nimport { NextNodeServerSpan } from '../lib/trace/constants'\nimport { StaticGenBailoutError } from '../../client/components/static-generation-bailout'\nimport type { LoadingModuleData } from '../../shared/lib/app-router-context.shared-runtime'\nimport type { Params } from '../request/params'\nimport { workUnitAsyncStorage } from './work-unit-async-storage.external'\nimport { OUTLET_BOUNDARY_NAME } from '../../lib/metadata/metadata-constants'\nimport type { UseCachePageComponentProps } from '../use-cache/use-cache-wrapper'\n\n/**\n * Use the provided loader tree to create the React Component tree.\n */\nexport function createComponentTree(props: {\n  loaderTree: LoaderTree\n  parentParams: Params\n  rootLayoutIncluded: boolean\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  getMetadataReady: () => Promise<void>\n  getViewportReady: () => Promise<void>\n  ctx: AppRenderContext\n  missingSlots?: Set<string>\n  preloadCallbacks: PreloadCallbacks\n  authInterrupts: boolean\n  StreamingMetadataOutlet: React.ComponentType\n}): Promise<CacheNodeSeedData> {\n  return getTracer().trace(\n    NextNodeServerSpan.createComponentTree,\n    {\n      spanName: 'build component tree',\n    },\n    () => createComponentTreeInternal(props)\n  )\n}\n\nfunction errorMissingDefaultExport(\n  pagePath: string,\n  convention: string\n): never {\n  const normalizedPagePath = pagePath === '/' ? '' : pagePath\n  throw new Error(\n    `The default export is not a React Component in \"${normalizedPagePath}/${convention}\"`\n  )\n}\n\nconst cacheNodeKey = 'c'\n\nasync function createComponentTreeInternal({\n  loaderTree: tree,\n  parentParams,\n  rootLayoutIncluded,\n  injectedCSS,\n  injectedJS,\n  injectedFontPreloadTags,\n  getViewportReady,\n  getMetadataReady,\n  ctx,\n  missingSlots,\n  preloadCallbacks,\n  authInterrupts,\n  StreamingMetadataOutlet,\n}: {\n  loaderTree: LoaderTree\n  parentParams: Params\n  rootLayoutIncluded: boolean\n  injectedCSS: Set<string>\n  injectedJS: Set<string>\n  injectedFontPreloadTags: Set<string>\n  getViewportReady: () => Promise<void>\n  getMetadataReady: () => Promise<void>\n  ctx: AppRenderContext\n  missingSlots?: Set<string>\n  preloadCallbacks: PreloadCallbacks\n  authInterrupts: boolean\n  StreamingMetadataOutlet: React.ComponentType | null\n}): Promise<CacheNodeSeedData> {\n  const {\n    renderOpts: { nextConfigOutput, experimental },\n    workStore,\n    componentMod: {\n      HTTPAccessFallbackBoundary,\n      LayoutRouter,\n      RenderFromTemplateContext,\n      OutletBoundary,\n      ClientPageRoot,\n      ClientSegmentRoot,\n      createServerSearchParamsForServerPage,\n      createPrerenderSearchParamsForClientPage,\n      createServerParamsForServerSegment,\n      createPrerenderParamsForClientSegment,\n      serverHooks: { DynamicServerError },\n      Postpone,\n    },\n    pagePath,\n    getDynamicParamFromSegment,\n    isPrefetch,\n    query,\n  } = ctx\n\n  const { page, layoutOrPagePath, segment, modules, parallelRoutes } =\n    parseLoaderTree(tree)\n\n  const {\n    layout,\n    template,\n    error,\n    loading,\n    'not-found': notFound,\n    forbidden,\n    unauthorized,\n  } = modules\n\n  const injectedCSSWithCurrentLayout = new Set(injectedCSS)\n  const injectedJSWithCurrentLayout = new Set(injectedJS)\n  const injectedFontPreloadTagsWithCurrentLayout = new Set(\n    injectedFontPreloadTags\n  )\n\n  const layerAssets = getLayerAssets({\n    preloadCallbacks,\n    ctx,\n    layoutOrPagePath,\n    injectedCSS: injectedCSSWithCurrentLayout,\n    injectedJS: injectedJSWithCurrentLayout,\n    injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n  })\n\n  const [Template, templateStyles, templateScripts] = template\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: template[1],\n        getComponent: template[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : [React.Fragment]\n\n  const [ErrorComponent, errorStyles, errorScripts] = error\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: error[1],\n        getComponent: error[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Loading, loadingStyles, loadingScripts] = loading\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: loading[1],\n        getComponent: loading[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const isLayout = typeof layout !== 'undefined'\n  const isPage = typeof page !== 'undefined'\n  const { mod: layoutOrPageMod, modType } = await getTracer().trace(\n    NextNodeServerSpan.getLayoutOrPageModule,\n    {\n      hideSpan: !(isLayout || isPage),\n      spanName: 'resolve segment modules',\n      attributes: {\n        'next.segment': segment,\n      },\n    },\n    () => getLayoutOrPageModule(tree)\n  )\n\n  /**\n   * Checks if the current segment is a root layout.\n   */\n  const rootLayoutAtThisLevel = isLayout && !rootLayoutIncluded\n  /**\n   * Checks if the current segment or any level above it has a root layout.\n   */\n  const rootLayoutIncludedAtThisLevelOrAbove =\n    rootLayoutIncluded || rootLayoutAtThisLevel\n\n  const [NotFound, notFoundStyles] = notFound\n    ? await createComponentStylesAndScripts({\n        ctx,\n        filePath: notFound[1],\n        getComponent: notFound[0],\n        injectedCSS: injectedCSSWithCurrentLayout,\n        injectedJS: injectedJSWithCurrentLayout,\n      })\n    : []\n\n  const [Forbidden, forbiddenStyles] =\n    authInterrupts && forbidden\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: forbidden[1],\n          getComponent: forbidden[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  const [Unauthorized, unauthorizedStyles] =\n    authInterrupts && unauthorized\n      ? await createComponentStylesAndScripts({\n          ctx,\n          filePath: unauthorized[1],\n          getComponent: unauthorized[0],\n          injectedCSS: injectedCSSWithCurrentLayout,\n          injectedJS: injectedJSWithCurrentLayout,\n        })\n      : []\n\n  let dynamic = layoutOrPageMod?.dynamic\n\n  if (nextConfigOutput === 'export') {\n    if (!dynamic || dynamic === 'auto') {\n      dynamic = 'error'\n    } else if (dynamic === 'force-dynamic') {\n      // force-dynamic is always incompatible with 'export'. We must interrupt the build\n      throw new StaticGenBailoutError(\n        `Page with \\`dynamic = \"force-dynamic\"\\` couldn't be exported. \\`output: \"export\"\\` requires all pages be renderable statically because there is no runtime server to dynamically render routes in this output format. Learn more: https://nextjs.org/docs/app/building-your-application/deploying/static-exports`\n      )\n    }\n  }\n\n  if (typeof dynamic === 'string') {\n    // the nested most config wins so we only force-static\n    // if it's configured above any parent that configured\n    // otherwise\n    if (dynamic === 'error') {\n      workStore.dynamicShouldError = true\n    } else if (dynamic === 'force-dynamic') {\n      workStore.forceDynamic = true\n\n      // TODO: (PPR) remove this bailout once PPR is the default\n      if (workStore.isStaticGeneration && !experimental.isRoutePPREnabled) {\n        // If the postpone API isn't available, we can't postpone the render and\n        // therefore we can't use the dynamic API.\n        const err = new DynamicServerError(\n          `Page with \\`dynamic = \"force-dynamic\"\\` won't be rendered statically.`\n        )\n        workStore.dynamicUsageDescription = err.message\n        workStore.dynamicUsageStack = err.stack\n        throw err\n      }\n    } else {\n      workStore.dynamicShouldError = false\n      workStore.forceStatic = dynamic === 'force-static'\n    }\n  }\n\n  if (typeof layoutOrPageMod?.fetchCache === 'string') {\n    workStore.fetchCache = layoutOrPageMod?.fetchCache\n  }\n\n  if (typeof layoutOrPageMod?.revalidate !== 'undefined') {\n    validateRevalidate(layoutOrPageMod?.revalidate, workStore.route)\n  }\n\n  if (typeof layoutOrPageMod?.revalidate === 'number') {\n    const defaultRevalidate = layoutOrPageMod.revalidate as number\n\n    const workUnitStore = workUnitAsyncStorage.getStore()\n\n    if (workUnitStore) {\n      if (\n        workUnitStore.type === 'prerender' ||\n        workUnitStore.type === 'prerender-legacy' ||\n        workUnitStore.type === 'prerender-ppr' ||\n        workUnitStore.type === 'cache'\n      ) {\n        if (workUnitStore.revalidate > defaultRevalidate) {\n          workUnitStore.revalidate = defaultRevalidate\n        }\n      }\n    }\n\n    if (\n      !workStore.forceStatic &&\n      workStore.isStaticGeneration &&\n      defaultRevalidate === 0 &&\n      // If the postpone API isn't available, we can't postpone the render and\n      // therefore we can't use the dynamic API.\n      !experimental.isRoutePPREnabled\n    ) {\n      const dynamicUsageDescription = `revalidate: 0 configured ${segment}`\n      workStore.dynamicUsageDescription = dynamicUsageDescription\n\n      throw new DynamicServerError(dynamicUsageDescription)\n    }\n  }\n\n  const isStaticGeneration = workStore.isStaticGeneration\n\n  // Assume the segment we're rendering contains only partial data if PPR is\n  // enabled and this is a statically generated response. This is used by the\n  // client Segment Cache after a prefetch to determine if it can skip the\n  // second request to fill in the dynamic data.\n  //\n  // It's OK for this to be `true` when the data is actually fully static, but\n  // it's not OK for this to be `false` when the data possibly contains holes.\n  // Although the value here is overly pessimistic, for prefetches, it will be\n  // replaced by a more specific value when the data is later processed into\n  // per-segment responses (see collect-segment-data.tsx)\n  //\n  // For dynamic requests, this must always be `false` because dynamic responses\n  // are never partial.\n  const isPossiblyPartialResponse =\n    isStaticGeneration && experimental.isRoutePPREnabled === true\n\n  const LayoutOrPage: React.ComponentType<any> | undefined = layoutOrPageMod\n    ? interopDefault(layoutOrPageMod)\n    : undefined\n\n  /**\n   * The React Component to render.\n   */\n  let MaybeComponent = LayoutOrPage\n\n  if (process.env.NODE_ENV === 'development') {\n    const { isValidElementType } = require('next/dist/compiled/react-is')\n    if (\n      typeof MaybeComponent !== 'undefined' &&\n      !isValidElementType(MaybeComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, modType ?? 'page')\n    }\n\n    if (\n      typeof ErrorComponent !== 'undefined' &&\n      !isValidElementType(ErrorComponent)\n    ) {\n      errorMissingDefaultExport(pagePath, 'error')\n    }\n\n    if (typeof Loading !== 'undefined' && !isValidElementType(Loading)) {\n      errorMissingDefaultExport(pagePath, 'loading')\n    }\n\n    if (typeof NotFound !== 'undefined' && !isValidElementType(NotFound)) {\n      errorMissingDefaultExport(pagePath, 'not-found')\n    }\n\n    if (typeof Forbidden !== 'undefined' && !isValidElementType(Forbidden)) {\n      errorMissingDefaultExport(pagePath, 'forbidden')\n    }\n\n    if (\n      typeof Unauthorized !== 'undefined' &&\n      !isValidElementType(Unauthorized)\n    ) {\n      errorMissingDefaultExport(pagePath, 'unauthorized')\n    }\n  }\n\n  // Handle dynamic segment params.\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  // Create object holding the parent params and current params\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  // Resolve the segment param\n  const actualSegment = segmentParam ? segmentParam.treeSegment : segment\n\n  // Use the same condition to render metadataOutlet as metadata\n  const metadataOutlet = StreamingMetadataOutlet ? (\n    <StreamingMetadataOutlet />\n  ) : undefined\n\n  const notFoundElement = NotFound ? (\n    <>\n      <NotFound />\n      {notFoundStyles}\n    </>\n  ) : undefined\n\n  const forbiddenElement = Forbidden ? (\n    <>\n      <Forbidden />\n      {forbiddenStyles}\n    </>\n  ) : undefined\n\n  const unauthorizedElement = Unauthorized ? (\n    <>\n      <Unauthorized />\n      {unauthorizedStyles}\n    </>\n  ) : undefined\n\n  // TODO: Combine this `map` traversal with the loop below that turns the array\n  // into an object.\n  const parallelRouteMap = await Promise.all(\n    Object.keys(parallelRoutes).map(\n      async (\n        parallelRouteKey\n      ): Promise<[string, React.ReactNode, CacheNodeSeedData | null]> => {\n        const isChildrenRouteKey = parallelRouteKey === 'children'\n        const parallelRoute = parallelRoutes[parallelRouteKey]\n\n        const notFoundComponent = isChildrenRouteKey\n          ? notFoundElement\n          : undefined\n\n        const forbiddenComponent = isChildrenRouteKey\n          ? forbiddenElement\n          : undefined\n\n        const unauthorizedComponent = isChildrenRouteKey\n          ? unauthorizedElement\n          : undefined\n\n        // if we're prefetching and that there's a Loading component, we bail out\n        // otherwise we keep rendering for the prefetch.\n        // We also want to bail out if there's no Loading component in the tree.\n        let childCacheNodeSeedData: CacheNodeSeedData | null = null\n\n        if (\n          // Before PPR, the way instant navigations work in Next.js is we\n          // prefetch everything up to the first route segment that defines a\n          // loading.tsx boundary. (We do the same if there's no loading\n          // boundary in the entire tree, because we don't want to prefetch too\n          // much) The rest of the tree is deferred until the actual navigation.\n          // It does not take into account whether the data is dynamic — even if\n          // the tree is completely static, it will still defer everything\n          // inside the loading boundary.\n          //\n          // This behavior predates PPR and is only relevant if the\n          // PPR flag is not enabled.\n          isPrefetch &&\n          (Loading || !hasLoadingComponentInTree(parallelRoute)) &&\n          // The approach with PPR is different — loading.tsx behaves like a\n          // regular Suspense boundary and has no special behavior.\n          //\n          // With PPR, we prefetch as deeply as possible, and only defer when\n          // dynamic data is accessed. If so, we only defer the nearest parent\n          // Suspense boundary of the dynamic data access, regardless of whether\n          // the boundary is defined by loading.tsx or a normal <Suspense>\n          // component in userspace.\n          //\n          // NOTE: In practice this usually means we'll end up prefetching more\n          // than we were before PPR, which may or may not be considered a\n          // performance regression by some apps. The plan is to address this\n          // before General Availability of PPR by introducing granular\n          // per-segment fetching, so we can reuse as much of the tree as\n          // possible during both prefetches and dynamic navigations. But during\n          // the beta period, we should be clear about this trade off in our\n          // communications.\n          !experimental.isRoutePPREnabled\n        ) {\n          // Don't prefetch this child. This will trigger a lazy fetch by the\n          // client router.\n        } else {\n          // Create the child component\n\n          if (process.env.NODE_ENV === 'development' && missingSlots) {\n            // When we detect the default fallback (which triggers a 404), we collect the missing slots\n            // to provide more helpful debug information during development mode.\n            const parsedTree = parseLoaderTree(parallelRoute)\n            if (\n              parsedTree.layoutOrPagePath?.endsWith(PARALLEL_ROUTE_DEFAULT_PATH)\n            ) {\n              missingSlots.add(parallelRouteKey)\n            }\n          }\n\n          const seedData = await createComponentTreeInternal({\n            loaderTree: parallelRoute,\n            parentParams: currentParams,\n            rootLayoutIncluded: rootLayoutIncludedAtThisLevelOrAbove,\n            injectedCSS: injectedCSSWithCurrentLayout,\n            injectedJS: injectedJSWithCurrentLayout,\n            injectedFontPreloadTags: injectedFontPreloadTagsWithCurrentLayout,\n            // `getMetadataReady` and `getViewportReady` are used to conditionally throw. In the case of parallel routes we will have more than one page\n            // but we only want to throw on the first one.\n            getMetadataReady: isChildrenRouteKey\n              ? getMetadataReady\n              : () => Promise.resolve(),\n            getViewportReady: isChildrenRouteKey\n              ? getViewportReady\n              : () => Promise.resolve(),\n            ctx,\n            missingSlots,\n            preloadCallbacks,\n            authInterrupts,\n            // `StreamingMetadataOutlet` is used to conditionally throw. In the case of parallel routes we will have more than one page\n            // but we only want to throw on the first one.\n            StreamingMetadataOutlet: isChildrenRouteKey\n              ? StreamingMetadataOutlet\n              : null,\n          })\n\n          childCacheNodeSeedData = seedData\n        }\n\n        // This is turned back into an object below.\n        return [\n          parallelRouteKey,\n          <LayoutRouter\n            parallelRouterKey={parallelRouteKey}\n            // TODO-APP: Add test for loading returning `undefined`. This currently can't be tested as the `webdriver()` tab will wait for the full page to load before returning.\n            error={ErrorComponent}\n            errorStyles={errorStyles}\n            errorScripts={errorScripts}\n            template={\n              <Template>\n                <RenderFromTemplateContext />\n              </Template>\n            }\n            templateStyles={templateStyles}\n            templateScripts={templateScripts}\n            notFound={notFoundComponent}\n            forbidden={forbiddenComponent}\n            unauthorized={unauthorizedComponent}\n          />,\n          childCacheNodeSeedData,\n        ]\n      }\n    )\n  )\n\n  // Convert the parallel route map into an object after all promises have been resolved.\n  let parallelRouteProps: { [key: string]: React.ReactNode } = {}\n  let parallelRouteCacheNodeSeedData: {\n    [key: string]: CacheNodeSeedData | null\n  } = {}\n  for (const parallelRoute of parallelRouteMap) {\n    const [parallelRouteKey, parallelRouteProp, flightData] = parallelRoute\n    parallelRouteProps[parallelRouteKey] = parallelRouteProp\n    parallelRouteCacheNodeSeedData[parallelRouteKey] = flightData\n  }\n\n  const loadingData: LoadingModuleData = Loading\n    ? [<Loading key=\"l\" />, loadingStyles, loadingScripts]\n    : null\n\n  // When the segment does not have a layout or page we still have to add the layout router to ensure the path holds the loading component\n  if (!MaybeComponent) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {layerAssets}\n        {parallelRouteProps.children}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n\n  const Component = MaybeComponent\n  // If force-dynamic is used and the current render supports postponing, we\n  // replace it with a node that will postpone the render. This ensures that the\n  // postpone is invoked during the react render phase and not during the next\n  // render phase.\n  // @TODO this does not actually do what it seems like it would or should do. The idea is that\n  // if we are rendering in a force-dynamic mode and we can postpone we should only make the segments\n  // that ask for force-dynamic to be dynamic, allowing other segments to still prerender. However\n  // because this comes after the children traversal and the static generation store is mutated every segment\n  // along the parent path of a force-dynamic segment will hit this condition effectively making the entire\n  // render force-dynamic. We should refactor this function so that we can correctly track which segments\n  // need to be dynamic\n  if (\n    workStore.isStaticGeneration &&\n    workStore.forceDynamic &&\n    experimental.isRoutePPREnabled\n  ) {\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        <Postpone\n          reason='dynamic = \"force-dynamic\" was used'\n          route={workStore.route}\n        />\n        {layerAssets}\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      true,\n    ]\n  }\n\n  const isClientComponent = isClientReference(layoutOrPageMod)\n\n  if (\n    process.env.NODE_ENV === 'development' &&\n    'params' in parallelRouteProps\n  ) {\n    // @TODO consider making this an error and running the check in build as well\n    console.error(\n      `\"params\" is a reserved prop in Layouts and Pages and cannot be used as the name of a parallel route in ${segment}`\n    )\n  }\n\n  if (isPage) {\n    const PageComponent = Component\n    // Assign searchParams to props if this is a page\n    let pageElement: React.ReactNode\n    if (isClientComponent) {\n      if (isStaticGeneration) {\n        const promiseOfParams = createPrerenderParamsForClientSegment(\n          currentParams,\n          workStore\n        )\n        const promiseOfSearchParams =\n          createPrerenderSearchParamsForClientPage(workStore)\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n            promises={[promiseOfSearchParams, promiseOfParams]}\n          />\n        )\n      } else {\n        pageElement = (\n          <ClientPageRoot\n            Component={PageComponent}\n            searchParams={query}\n            params={currentParams}\n          />\n        )\n      }\n    } else {\n      // If we are passing params to a server component Page we need to track\n      // their usage in case the current render mode tracks dynamic API usage.\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      // TODO(useCache): Should we use this trick also if dynamicIO is enabled,\n      // instead of relying on the searchParams being a hanging promise?\n      if (!experimental.dynamicIO && isUseCacheFunction(PageComponent)) {\n        const UseCachePageComponent: React.ComponentType<UseCachePageComponentProps> =\n          PageComponent\n\n        // The \"use cache\" wrapper takes care of converting this into an\n        // erroring search params promise when passing it to the original\n        // function.\n        const searchParams = Promise.resolve({})\n\n        pageElement = (\n          <UseCachePageComponent\n            params={params}\n            searchParams={searchParams}\n            $$isPageComponent\n          />\n        )\n      } else {\n        // If we are passing searchParams to a server component Page we need to\n        // track their usage in case the current render mode tracks dynamic API\n        // usage.\n        const searchParams = createServerSearchParamsForServerPage(\n          query,\n          workStore\n        )\n\n        pageElement = (\n          <PageComponent params={params} searchParams={searchParams} />\n        )\n      }\n    }\n    return [\n      actualSegment,\n      <React.Fragment key={cacheNodeKey}>\n        {pageElement}\n        {layerAssets}\n        <OutletBoundary>\n          <MetadataOutlet ready={getViewportReady} />\n          {/* Blocking metadata outlet */}\n          <MetadataOutlet ready={getMetadataReady} />\n          {/* Streaming metadata outlet */}\n          {metadataOutlet}\n        </OutletBoundary>\n      </React.Fragment>,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  } else {\n    const SegmentComponent = Component\n\n    const isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot =\n      rootLayoutAtThisLevel &&\n      'children' in parallelRoutes &&\n      Object.keys(parallelRoutes).length > 1\n\n    let segmentNode: React.ReactNode\n\n    if (isClientComponent) {\n      let clientSegment: React.ReactNode\n\n      if (isStaticGeneration) {\n        const promiseOfParams = createPrerenderParamsForClientSegment(\n          currentParams,\n          workStore\n        )\n\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n            promise={promiseOfParams}\n          />\n        )\n      } else {\n        clientSegment = (\n          <ClientSegmentRoot\n            Component={SegmentComponent}\n            slots={parallelRouteProps}\n            params={currentParams}\n          />\n        )\n      }\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        let notfoundClientSegment: React.ReactNode\n        let forbiddenClientSegment: React.ReactNode\n        let unauthorizedClientSegment: React.ReactNode\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        notfoundClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: NotFound,\n          errorElement: notFoundElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        forbiddenClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Forbidden,\n          errorElement: forbiddenElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        unauthorizedClientSegment = createErrorBoundaryClientSegmentRoot({\n          ErrorBoundaryComponent: Unauthorized,\n          errorElement: unauthorizedElement,\n          ClientSegmentRoot,\n          layerAssets,\n          SegmentComponent,\n          currentParams,\n        })\n        if (\n          notfoundClientSegment ||\n          forbiddenClientSegment ||\n          unauthorizedClientSegment\n        ) {\n          segmentNode = (\n            <HTTPAccessFallbackBoundary\n              key={cacheNodeKey}\n              notFound={notfoundClientSegment}\n              forbidden={forbiddenClientSegment}\n              unauthorized={unauthorizedClientSegment}\n            >\n              {layerAssets}\n              {clientSegment}\n            </HTTPAccessFallbackBoundary>\n          )\n        } else {\n          segmentNode = (\n            <React.Fragment key={cacheNodeKey}>\n              {layerAssets}\n              {clientSegment}\n            </React.Fragment>\n          )\n        }\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {clientSegment}\n          </React.Fragment>\n        )\n      }\n    } else {\n      const params = createServerParamsForServerSegment(\n        currentParams,\n        workStore\n      )\n\n      let serverSegment = (\n        <SegmentComponent {...parallelRouteProps} params={params} />\n      )\n\n      if (isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot) {\n        // TODO-APP: This is a hack to support unmatched parallel routes, which will throw `notFound()`.\n        // This ensures that a `HTTPAccessFallbackBoundary` is available for when that happens,\n        // but it's not ideal, as it needlessly invokes the `NotFound` component and renders the `RootLayout` twice.\n        // We should instead look into handling the fallback behavior differently in development mode so that it doesn't\n        // rely on the `NotFound` behavior.\n        segmentNode = (\n          <HTTPAccessFallbackBoundary\n            key={cacheNodeKey}\n            notFound={\n              NotFound ? (\n                <>\n                  {layerAssets}\n                  <SegmentComponent params={params}>\n                    {notFoundStyles}\n                    <NotFound />\n                  </SegmentComponent>\n                </>\n              ) : undefined\n            }\n          >\n            {layerAssets}\n            {serverSegment}\n          </HTTPAccessFallbackBoundary>\n        )\n      } else {\n        segmentNode = (\n          <React.Fragment key={cacheNodeKey}>\n            {layerAssets}\n            {serverSegment}\n          </React.Fragment>\n        )\n      }\n    }\n    // For layouts we just render the component\n    return [\n      actualSegment,\n      segmentNode,\n      parallelRouteCacheNodeSeedData,\n      loadingData,\n      isPossiblyPartialResponse,\n    ]\n  }\n}\n\nasync function MetadataOutlet({\n  ready,\n}: {\n  ready: () => Promise<void> & { status?: string; value?: unknown }\n}) {\n  const r = ready()\n  // We can avoid a extra microtask by unwrapping the instrumented promise directly if available.\n  if (r.status === 'rejected') {\n    throw r.value\n  } else if (r.status !== 'fulfilled') {\n    await r\n  }\n  return null\n}\nMetadataOutlet.displayName = OUTLET_BOUNDARY_NAME\n\nfunction createErrorBoundaryClientSegmentRoot({\n  ErrorBoundaryComponent,\n  errorElement,\n  ClientSegmentRoot,\n  layerAssets,\n  SegmentComponent,\n  currentParams,\n}: {\n  ErrorBoundaryComponent: React.ComponentType<any> | undefined\n  errorElement: React.ReactNode\n  ClientSegmentRoot: React.ComponentType<any>\n  layerAssets: React.ReactNode\n  SegmentComponent: React.ComponentType<any>\n  currentParams: Params\n}) {\n  if (ErrorBoundaryComponent) {\n    const notFoundParallelRouteProps = {\n      children: errorElement,\n    }\n    return (\n      <>\n        {layerAssets}\n        <ClientSegmentRoot\n          Component={SegmentComponent}\n          slots={notFoundParallelRouteProps}\n          params={currentParams}\n        />\n      </>\n    )\n  }\n  return null\n}\n\nexport function getRootParams(\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  return getRootParamsImpl({}, loaderTree, getDynamicParamFromSegment)\n}\n\nfunction getRootParamsImpl(\n  parentParams: Params,\n  loaderTree: LoaderTree,\n  getDynamicParamFromSegment: GetDynamicParamFromSegment\n): Params {\n  const {\n    segment,\n    modules: { layout },\n    parallelRoutes,\n  } = parseLoaderTree(loaderTree)\n\n  const segmentParam = getDynamicParamFromSegment(segment)\n\n  let currentParams: Params = parentParams\n  if (segmentParam && segmentParam.value !== null) {\n    currentParams = {\n      ...parentParams,\n      [segmentParam.param]: segmentParam.value,\n    }\n  }\n\n  const isRootLayout = typeof layout !== 'undefined'\n\n  if (isRootLayout) {\n    return currentParams\n  } else if (!parallelRoutes.children) {\n    // This should really be an error but there are bugs in Turbopack that cause\n    // the _not-found LoaderTree to not have any layouts. For rootParams sake\n    // this is somewhat irrelevant when you are not customizing the 404 page.\n    // If you are customizing 404\n    // TODO update rootParams to make all params optional if `/app/not-found.tsx` is defined\n    return currentParams\n  } else {\n    return getRootParamsImpl(\n      currentParams,\n      // We stop looking for root params as soon as we hit the first layout\n      // and it is not possible to use parallel route children above the root layout\n      // so every parallelRoutes object that this function can visit will necessarily\n      // have a single `children` prop and no others.\n      parallelRoutes.children,\n      getDynamicParamFromSegment\n    )\n  }\n}\n"], "names": ["React", "isClientReference", "isUseCacheFunction", "getLayoutOrPageModule", "interopDefault", "parseLoaderTree", "createComponentStylesAndScripts", "getLayerAssets", "hasLoadingComponentInTree", "validateRevalidate", "PARALLEL_ROUTE_DEFAULT_PATH", "getTracer", "NextNodeServerSpan", "StaticGenBailoutError", "workUnitAsyncStorage", "OUTLET_BOUNDARY_NAME", "createComponentTree", "props", "trace", "spanName", "createComponentTreeInternal", "errorMissingDefaultExport", "pagePath", "convention", "normalizedPagePath", "Error", "cacheNodeKey", "loaderTree", "tree", "parentParams", "rootLayoutIncluded", "injectedCSS", "injectedJS", "injectedFontPreloadTags", "getViewportReady", "getMetadataReady", "ctx", "missingSlots", "preloadCallbacks", "authInterrupts", "StreamingMetadataOutlet", "renderOpts", "nextConfigOutput", "experimental", "workStore", "componentMod", "HTTPAccessFallbackBoundary", "LayoutRouter", "RenderFromTemplateContext", "OutletBoundary", "ClientPageRoot", "ClientSegmentRoot", "createServerSearchParamsForServerPage", "createPrerenderSearchParamsForClientPage", "createServerParamsForServerSegment", "createPrerenderParamsForClientSegment", "serverHooks", "DynamicServerError", "Postpone", "getDynamicParamFromSegment", "isPrefetch", "query", "page", "layoutOrPagePath", "segment", "modules", "parallelRoutes", "layout", "template", "error", "loading", "notFound", "forbidden", "unauthorized", "injectedCSSWithCurrentLayout", "Set", "injectedJSWithCurrentLayout", "injectedFontPreloadTagsWithCurrentLayout", "layerAssets", "Template", "templateStyles", "templateScripts", "filePath", "getComponent", "Fragment", "ErrorComponent", "errorStyles", "errorScripts", "Loading", "loadingStyles", "loadingScripts", "isLayout", "isPage", "mod", "layoutOrPageMod", "modType", "hideSpan", "attributes", "rootLayoutAtThisLevel", "rootLayoutIncludedAtThisLevelOrAbove", "NotFound", "notFoundStyles", "Forbidden", "forbiddenStyles", "Unauthorized", "unauthorizedStyles", "dynamic", "dynamicShouldError", "forceDynamic", "isStaticGeneration", "isRoutePPREnabled", "err", "dynamicUsageDescription", "message", "dynamicUsageStack", "stack", "forceStatic", "fetchCache", "revalidate", "route", "defaultRevalidate", "workUnitStore", "getStore", "type", "isPossiblyPartialResponse", "LayoutOrPage", "undefined", "MaybeComponent", "process", "env", "NODE_ENV", "isValidElementType", "require", "segmentParam", "currentParams", "value", "param", "actualSegment", "treeSegment", "metadataOutlet", "notFoundElement", "forbiddenElement", "unauthorizedElement", "parallelRouteMap", "Promise", "all", "Object", "keys", "map", "parallelRouteKey", "isChildrenRoute<PERSON>ey", "parallelRoute", "notFoundComponent", "forbiddenComponent", "unauthorizedComponent", "childCacheNodeSeedData", "parsedTree", "endsWith", "add", "seedData", "resolve", "parallel<PERSON><PERSON>er<PERSON>ey", "parallelRouteProps", "parallelRouteCacheNodeSeedData", "parallelRouteProp", "flightData", "loadingData", "children", "Component", "reason", "isClientComponent", "console", "PageComponent", "pageElement", "promiseOfParams", "promiseOfSearchParams", "searchParams", "params", "promises", "dynamicIO", "UseCachePageComponent", "$$isPageComponent", "MetadataOutlet", "ready", "SegmentComponent", "isRootLayoutWithChildrenSlotAndAtLeastOneMoreSlot", "length", "segmentNode", "clientSegment", "slots", "promise", "notfoundClientSegment", "forbiddenClientSegment", "unauthorizedClientSegment", "createErrorBoundaryClientSegmentRoot", "ErrorBoundaryComponent", "errorElement", "serverSegment", "r", "status", "displayName", "notFoundParallelRouteProps", "getRootParams", "getRootParamsImpl", "isRootLayout"], "mappings": ";AACA,OAAOA,WAAW,QAAO;AACzB,SACEC,iBAAiB,EACjBC,kBAAkB,QACb,yCAAwC;AAC/C,SAASC,qBAAqB,QAAQ,wBAAuB;AAE7D,SAASC,cAAc,QAAQ,oBAAmB;AAClD,SAASC,eAAe,QAAQ,sBAAqB;AAErD,SAASC,+BAA+B,QAAQ,wCAAuC;AACvF,SAASC,cAAc,QAAQ,qBAAoB;AACnD,SAASC,yBAAyB,QAAQ,kCAAiC;AAC3E,SAASC,kBAAkB,QAAQ,qBAAoB;AACvD,SAASC,2BAA2B,QAAQ,iDAAgD;AAC5F,SAASC,SAAS,QAAQ,sBAAqB;AAC/C,SAASC,kBAAkB,QAAQ,yBAAwB;AAC3D,SAASC,qBAAqB,QAAQ,oDAAmD;AAGzF,SAASC,oBAAoB,QAAQ,qCAAoC;AACzE,SAASC,oBAAoB,QAAQ,wCAAuC;AAG5E;;CAEC,GACD,OAAO,SAASC,oBAAoBC,KAcnC;IACC,OAAON,YAAYO,KAAK,CACtBN,mBAAmBI,mBAAmB,EACtC;QACEG,UAAU;IACZ,GACA,IAAMC,4BAA4BH;AAEtC;AAEA,SAASI,0BACPC,QAAgB,EAChBC,UAAkB;IAElB,MAAMC,qBAAqBF,aAAa,MAAM,KAAKA;IACnD,MAAM,qBAEL,CAFK,IAAIG,MACR,CAAC,gDAAgD,EAAED,mBAAmB,CAAC,EAAED,WAAW,CAAC,CAAC,GADlF,qBAAA;eAAA;oBAAA;sBAAA;IAEN;AACF;AAEA,MAAMG,eAAe;AAErB,eAAeN,4BAA4B,EACzCO,YAAYC,IAAI,EAChBC,YAAY,EACZC,kBAAkB,EAClBC,WAAW,EACXC,UAAU,EACVC,uBAAuB,EACvBC,gBAAgB,EAChBC,gBAAgB,EAChBC,GAAG,EACHC,YAAY,EACZC,gBAAgB,EAChBC,cAAc,EACdC,uBAAuB,EAexB;IACC,MAAM,EACJC,YAAY,EAAEC,gBAAgB,EAAEC,YAAY,EAAE,EAC9CC,SAAS,EACTC,cAAc,EACZC,0BAA0B,EAC1BC,YAAY,EACZC,yBAAyB,EACzBC,cAAc,EACdC,cAAc,EACdC,iBAAiB,EACjBC,qCAAqC,EACrCC,wCAAwC,EACxCC,kCAAkC,EAClCC,qCAAqC,EACrCC,aAAa,EAAEC,kBAAkB,EAAE,EACnCC,QAAQ,EACT,EACDpC,QAAQ,EACRqC,0BAA0B,EAC1BC,UAAU,EACVC,KAAK,EACN,GAAGzB;IAEJ,MAAM,EAAE0B,IAAI,EAAEC,gBAAgB,EAAEC,OAAO,EAAEC,OAAO,EAAEC,cAAc,EAAE,GAChE7D,gBAAgBuB;IAElB,MAAM,EACJuC,MAAM,EACNC,QAAQ,EACRC,KAAK,EACLC,OAAO,EACP,aAAaC,QAAQ,EACrBC,SAAS,EACTC,YAAY,EACb,GAAGR;IAEJ,MAAMS,+BAA+B,IAAIC,IAAI5C;IAC7C,MAAM6C,8BAA8B,IAAID,IAAI3C;IAC5C,MAAM6C,2CAA2C,IAAIF,IACnD1C;IAGF,MAAM6C,cAAcvE,eAAe;QACjC+B;QACAF;QACA2B;QACAhC,aAAa2C;QACb1C,YAAY4C;QACZ3C,yBAAyB4C;IAC3B;IAEA,MAAM,CAACE,UAAUC,gBAAgBC,gBAAgB,GAAGb,WAChD,MAAM9D,gCAAgC;QACpC8B;QACA8C,UAAUd,QAAQ,CAAC,EAAE;QACrBe,cAAcf,QAAQ,CAAC,EAAE;QACzBrC,aAAa2C;QACb1C,YAAY4C;IACd,KACA;QAAC5E,MAAMoF,QAAQ;KAAC;IAEpB,MAAM,CAACC,gBAAgBC,aAAaC,aAAa,GAAGlB,QAChD,MAAM/D,gCAAgC;QACpC8B;QACA8C,UAAUb,KAAK,CAAC,EAAE;QAClBc,cAAcd,KAAK,CAAC,EAAE;QACtBtC,aAAa2C;QACb1C,YAAY4C;IACd,KACA,EAAE;IAEN,MAAM,CAACY,SAASC,eAAeC,eAAe,GAAGpB,UAC7C,MAAMhE,gCAAgC;QACpC8B;QACA8C,UAAUZ,OAAO,CAAC,EAAE;QACpBa,cAAcb,OAAO,CAAC,EAAE;QACxBvC,aAAa2C;QACb1C,YAAY4C;IACd,KACA,EAAE;IAEN,MAAMe,WAAW,OAAOxB,WAAW;IACnC,MAAMyB,SAAS,OAAO9B,SAAS;IAC/B,MAAM,EAAE+B,KAAKC,eAAe,EAAEC,OAAO,EAAE,GAAG,MAAMpF,YAAYO,KAAK,CAC/DN,mBAAmBT,qBAAqB,EACxC;QACE6F,UAAU,CAAEL,CAAAA,YAAYC,MAAK;QAC7BzE,UAAU;QACV8E,YAAY;YACV,gBAAgBjC;QAClB;IACF,GACA,IAAM7D,sBAAsByB;IAG9B;;GAEC,GACD,MAAMsE,wBAAwBP,YAAY,CAAC7D;IAC3C;;GAEC,GACD,MAAMqE,uCACJrE,sBAAsBoE;IAExB,MAAM,CAACE,UAAUC,eAAe,GAAG9B,WAC/B,MAAMjE,gCAAgC;QACpC8B;QACA8C,UAAUX,QAAQ,CAAC,EAAE;QACrBY,cAAcZ,QAAQ,CAAC,EAAE;QACzBxC,aAAa2C;QACb1C,YAAY4C;IACd,KACA,EAAE;IAEN,MAAM,CAAC0B,WAAWC,gBAAgB,GAChChE,kBAAkBiC,YACd,MAAMlE,gCAAgC;QACpC8B;QACA8C,UAAUV,SAAS,CAAC,EAAE;QACtBW,cAAcX,SAAS,CAAC,EAAE;QAC1BzC,aAAa2C;QACb1C,YAAY4C;IACd,KACA,EAAE;IAER,MAAM,CAAC4B,cAAcC,mBAAmB,GACtClE,kBAAkBkC,eACd,MAAMnE,gCAAgC;QACpC8B;QACA8C,UAAUT,YAAY,CAAC,EAAE;QACzBU,cAAcV,YAAY,CAAC,EAAE;QAC7B1C,aAAa2C;QACb1C,YAAY4C;IACd,KACA,EAAE;IAER,IAAI8B,UAAUZ,mCAAAA,gBAAiBY,OAAO;IAEtC,IAAIhE,qBAAqB,UAAU;QACjC,IAAI,CAACgE,WAAWA,YAAY,QAAQ;YAClCA,UAAU;QACZ,OAAO,IAAIA,YAAY,iBAAiB;YACtC,kFAAkF;YAClF,MAAM,qBAEL,CAFK,IAAI7F,sBACR,CAAC,gTAAgT,CAAC,GAD9S,qBAAA;uBAAA;4BAAA;8BAAA;YAEN;QACF;IACF;IAEA,IAAI,OAAO6F,YAAY,UAAU;QAC/B,sDAAsD;QACtD,sDAAsD;QACtD,YAAY;QACZ,IAAIA,YAAY,SAAS;YACvB9D,UAAU+D,kBAAkB,GAAG;QACjC,OAAO,IAAID,YAAY,iBAAiB;YACtC9D,UAAUgE,YAAY,GAAG;YAEzB,0DAA0D;YAC1D,IAAIhE,UAAUiE,kBAAkB,IAAI,CAAClE,aAAamE,iBAAiB,EAAE;gBACnE,wEAAwE;gBACxE,0CAA0C;gBAC1C,MAAMC,MAAM,qBAEX,CAFW,IAAItD,mBACd,CAAC,qEAAqE,CAAC,GAD7D,qBAAA;2BAAA;gCAAA;kCAAA;gBAEZ;gBACAb,UAAUoE,uBAAuB,GAAGD,IAAIE,OAAO;gBAC/CrE,UAAUsE,iBAAiB,GAAGH,IAAII,KAAK;gBACvC,MAAMJ;YACR;QACF,OAAO;YACLnE,UAAU+D,kBAAkB,GAAG;YAC/B/D,UAAUwE,WAAW,GAAGV,YAAY;QACtC;IACF;IAEA,IAAI,QAAOZ,mCAAAA,gBAAiBuB,UAAU,MAAK,UAAU;QACnDzE,UAAUyE,UAAU,GAAGvB,mCAAAA,gBAAiBuB,UAAU;IACpD;IAEA,IAAI,QAAOvB,mCAAAA,gBAAiBwB,UAAU,MAAK,aAAa;QACtD7G,mBAAmBqF,mCAAAA,gBAAiBwB,UAAU,EAAE1E,UAAU2E,KAAK;IACjE;IAEA,IAAI,QAAOzB,mCAAAA,gBAAiBwB,UAAU,MAAK,UAAU;QACnD,MAAME,oBAAoB1B,gBAAgBwB,UAAU;QAEpD,MAAMG,gBAAgB3G,qBAAqB4G,QAAQ;QAEnD,IAAID,eAAe;YACjB,IACEA,cAAcE,IAAI,KAAK,eACvBF,cAAcE,IAAI,KAAK,sBACvBF,cAAcE,IAAI,KAAK,mBACvBF,cAAcE,IAAI,KAAK,SACvB;gBACA,IAAIF,cAAcH,UAAU,GAAGE,mBAAmB;oBAChDC,cAAcH,UAAU,GAAGE;gBAC7B;YACF;QACF;QAEA,IACE,CAAC5E,UAAUwE,WAAW,IACtBxE,UAAUiE,kBAAkB,IAC5BW,sBAAsB,KACtB,wEAAwE;QACxE,0CAA0C;QAC1C,CAAC7E,aAAamE,iBAAiB,EAC/B;YACA,MAAME,0BAA0B,CAAC,yBAAyB,EAAEhD,SAAS;YACrEpB,UAAUoE,uBAAuB,GAAGA;YAEpC,MAAM,qBAA+C,CAA/C,IAAIvD,mBAAmBuD,0BAAvB,qBAAA;uBAAA;4BAAA;8BAAA;YAA8C;QACtD;IACF;IAEA,MAAMH,qBAAqBjE,UAAUiE,kBAAkB;IAEvD,0EAA0E;IAC1E,2EAA2E;IAC3E,wEAAwE;IACxE,8CAA8C;IAC9C,EAAE;IACF,4EAA4E;IAC5E,4EAA4E;IAC5E,4EAA4E;IAC5E,0EAA0E;IAC1E,uDAAuD;IACvD,EAAE;IACF,8EAA8E;IAC9E,qBAAqB;IACrB,MAAMe,4BACJf,sBAAsBlE,aAAamE,iBAAiB,KAAK;IAE3D,MAAMe,eAAqD/B,kBACvD1F,eAAe0F,mBACfgC;IAEJ;;GAEC,GACD,IAAIC,iBAAiBF;IAErB,IAAIG,QAAQC,GAAG,CAACC,QAAQ,KAAK,eAAe;QAC1C,MAAM,EAAEC,kBAAkB,EAAE,GAAGC,QAAQ;QACvC,IACE,OAAOL,mBAAmB,eAC1B,CAACI,mBAAmBJ,iBACpB;YACA1G,0BAA0BC,UAAUyE,WAAW;QACjD;QAEA,IACE,OAAOV,mBAAmB,eAC1B,CAAC8C,mBAAmB9C,iBACpB;YACAhE,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOkE,YAAY,eAAe,CAAC2C,mBAAmB3C,UAAU;YAClEnE,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAO8E,aAAa,eAAe,CAAC+B,mBAAmB/B,WAAW;YACpE/E,0BAA0BC,UAAU;QACtC;QAEA,IAAI,OAAOgF,cAAc,eAAe,CAAC6B,mBAAmB7B,YAAY;YACtEjF,0BAA0BC,UAAU;QACtC;QAEA,IACE,OAAOkF,iBAAiB,eACxB,CAAC2B,mBAAmB3B,eACpB;YACAnF,0BAA0BC,UAAU;QACtC;IACF;IAEA,iCAAiC;IACjC,MAAM+G,eAAe1E,2BAA2BK;IAEhD,6DAA6D;IAC7D,IAAIsE,gBAAwBzG;IAC5B,IAAIwG,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGzG,YAAY;YACf,CAACwG,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,4BAA4B;IAC5B,MAAME,gBAAgBJ,eAAeA,aAAaK,WAAW,GAAG1E;IAEhE,8DAA8D;IAC9D,MAAM2E,iBAAiBnG,wCACrB,KAACA,+BACCsF;IAEJ,MAAMc,kBAAkBxC,yBACtB;;0BACE,KAACA;YACAC;;SAEDyB;IAEJ,MAAMe,mBAAmBvC,0BACvB;;0BACE,KAACA;YACAC;;SAEDuB;IAEJ,MAAMgB,sBAAsBtC,6BAC1B;;0BACE,KAACA;YACAC;;SAEDqB;IAEJ,8EAA8E;IAC9E,kBAAkB;IAClB,MAAMiB,mBAAmB,MAAMC,QAAQC,GAAG,CACxCC,OAAOC,IAAI,CAACjF,gBAAgBkF,GAAG,CAC7B,OACEC;QAEA,MAAMC,qBAAqBD,qBAAqB;QAChD,MAAME,gBAAgBrF,cAAc,CAACmF,iBAAiB;QAEtD,MAAMG,oBAAoBF,qBACtBV,kBACAd;QAEJ,MAAM2B,qBAAqBH,qBACvBT,mBACAf;QAEJ,MAAM4B,wBAAwBJ,qBAC1BR,sBACAhB;QAEJ,yEAAyE;QACzE,gDAAgD;QAChD,wEAAwE;QACxE,IAAI6B,yBAAmD;QAEvD,IACE,gEAAgE;QAChE,mEAAmE;QACnE,8DAA8D;QAC9D,qEAAqE;QACrE,sEAAsE;QACtE,sEAAsE;QACtE,gEAAgE;QAChE,+BAA+B;QAC/B,EAAE;QACF,yDAAyD;QACzD,2BAA2B;QAC3B/F,cACC4B,CAAAA,WAAW,CAAChF,0BAA0B+I,cAAa,KACpD,kEAAkE;QAClE,yDAAyD;QACzD,EAAE;QACF,mEAAmE;QACnE,oEAAoE;QACpE,sEAAsE;QACtE,gEAAgE;QAChE,0BAA0B;QAC1B,EAAE;QACF,qEAAqE;QACrE,gEAAgE;QAChE,mEAAmE;QACnE,6DAA6D;QAC7D,+DAA+D;QAC/D,sEAAsE;QACtE,kEAAkE;QAClE,kBAAkB;QAClB,CAAC5G,aAAamE,iBAAiB,EAC/B;QACA,mEAAmE;QACnE,iBAAiB;QACnB,OAAO;YACL,6BAA6B;YAE7B,IAAIkB,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBAAiB7F,cAAc;oBAKxDuH;gBAJF,2FAA2F;gBAC3F,qEAAqE;gBACrE,MAAMA,aAAavJ,gBAAgBkJ;gBACnC,KACEK,+BAAAA,WAAW7F,gBAAgB,qBAA3B6F,6BAA6BC,QAAQ,CAACnJ,8BACtC;oBACA2B,aAAayH,GAAG,CAACT;gBACnB;YACF;YAEA,MAAMU,WAAW,MAAM3I,4BAA4B;gBACjDO,YAAY4H;gBACZ1H,cAAcyG;gBACdxG,oBAAoBqE;gBACpBpE,aAAa2C;gBACb1C,YAAY4C;gBACZ3C,yBAAyB4C;gBACzB,4IAA4I;gBAC5I,8CAA8C;gBAC9C1C,kBAAkBmH,qBACdnH,mBACA,IAAM6G,QAAQgB,OAAO;gBACzB9H,kBAAkBoH,qBACdpH,mBACA,IAAM8G,QAAQgB,OAAO;gBACzB5H;gBACAC;gBACAC;gBACAC;gBACA,2HAA2H;gBAC3H,8CAA8C;gBAC9CC,yBAAyB8G,qBACrB9G,0BACA;YACN;YAEAmH,yBAAyBI;QAC3B;QAEA,4CAA4C;QAC5C,OAAO;YACLV;0BACA,KAACtG;gBACCkH,mBAAmBZ;gBACnB,sKAAsK;gBACtKhF,OAAOgB;gBACPC,aAAaA;gBACbC,cAAcA;gBACdnB,wBACE,KAACW;8BACC,cAAA,KAAC/B;;gBAGLgC,gBAAgBA;gBAChBC,iBAAiBA;gBACjBV,UAAUiF;gBACVhF,WAAWiF;gBACXhF,cAAciF;;YAEhBC;SACD;IACH;IAIJ,uFAAuF;IACvF,IAAIO,qBAAyD,CAAC;IAC9D,IAAIC,iCAEA,CAAC;IACL,KAAK,MAAMZ,iBAAiBR,iBAAkB;QAC5C,MAAM,CAACM,kBAAkBe,mBAAmBC,WAAW,GAAGd;QAC1DW,kBAAkB,CAACb,iBAAiB,GAAGe;QACvCD,8BAA8B,CAACd,iBAAiB,GAAGgB;IACrD;IAEA,MAAMC,cAAiC9E,UACnC;sBAAC,KAACA,aAAY;QAAQC;QAAeC;KAAe,GACpD;IAEJ,wIAAwI;IACxI,IAAI,CAACqC,gBAAgB;QACnB,OAAO;YACLU;0BACA,MAACzI,MAAMoF,QAAQ;;oBACZN;oBACAoF,mBAAmBK,QAAQ;;eAFT7I;YAIrByI;YACAG;YACA1C;SACD;IACH;IAEA,MAAM4C,YAAYzC;IAClB,0EAA0E;IAC1E,8EAA8E;IAC9E,4EAA4E;IAC5E,gBAAgB;IAChB,6FAA6F;IAC7F,mGAAmG;IACnG,gGAAgG;IAChG,2GAA2G;IAC3G,yGAAyG;IACzG,uGAAuG;IACvG,qBAAqB;IACrB,IACEnF,UAAUiE,kBAAkB,IAC5BjE,UAAUgE,YAAY,IACtBjE,aAAamE,iBAAiB,EAC9B;QACA,OAAO;YACL2B;0BACA,MAACzI,MAAMoF,QAAQ;;kCACb,KAAC1B;wBACC+G,QAAO;wBACPlD,OAAO3E,UAAU2E,KAAK;;oBAEvBzC;;eALkBpD;YAOrByI;YACAG;YACA;SACD;IACH;IAEA,MAAMI,oBAAoBzK,kBAAkB6F;IAE5C,IACEkC,QAAQC,GAAG,CAACC,QAAQ,KAAK,iBACzB,YAAYgC,oBACZ;QACA,6EAA6E;QAC7ES,QAAQtG,KAAK,CACX,CAAC,uGAAuG,EAAEL,SAAS;IAEvH;IAEA,IAAI4B,QAAQ;QACV,MAAMgF,gBAAgBJ;QACtB,iDAAiD;QACjD,IAAIK;QACJ,IAAIH,mBAAmB;YACrB,IAAI7D,oBAAoB;gBACtB,MAAMiE,kBAAkBvH,sCACtB+E,eACA1F;gBAEF,MAAMmI,wBACJ1H,yCAAyCT;gBAC3CiI,4BACE,KAAC3H;oBACCsH,WAAWI;oBACXI,cAAcnH;oBACdoH,QAAQ3C;oBACR4C,UAAU;wBAACH;wBAAuBD;qBAAgB;;YAGxD,OAAO;gBACLD,4BACE,KAAC3H;oBACCsH,WAAWI;oBACXI,cAAcnH;oBACdoH,QAAQ3C;;YAGd;QACF,OAAO;YACL,uEAAuE;YACvE,wEAAwE;YACxE,MAAM2C,SAAS3H,mCACbgF,eACA1F;YAGF,yEAAyE;YACzE,kEAAkE;YAClE,IAAI,CAACD,aAAawI,SAAS,IAAIjL,mBAAmB0K,gBAAgB;gBAChE,MAAMQ,wBACJR;gBAEF,gEAAgE;gBAChE,iEAAiE;gBACjE,YAAY;gBACZ,MAAMI,eAAehC,QAAQgB,OAAO,CAAC,CAAC;gBAEtCa,4BACE,KAACO;oBACCH,QAAQA;oBACRD,cAAcA;oBACdK,iBAAiB;;YAGvB,OAAO;gBACL,uEAAuE;gBACvE,uEAAuE;gBACvE,SAAS;gBACT,MAAML,eAAe5H,sCACnBS,OACAjB;gBAGFiI,4BACE,KAACD;oBAAcK,QAAQA;oBAAQD,cAAcA;;YAEjD;QACF;QACA,OAAO;YACLvC;0BACA,MAACzI,MAAMoF,QAAQ;;oBACZyF;oBACA/F;kCACD,MAAC7B;;0CACC,KAACqI;gCAAeC,OAAOrJ;;0CAEvB,KAACoJ;gCAAeC,OAAOpJ;;4BAEtBwG;;;;eARgBjH;YAWrByI;YACAG;YACA1C;SACD;IACH,OAAO;QACL,MAAM4D,mBAAmBhB;QAEzB,MAAMiB,oDACJvF,yBACA,cAAchC,kBACdgF,OAAOC,IAAI,CAACjF,gBAAgBwH,MAAM,GAAG;QAEvC,IAAIC;QAEJ,IAAIjB,mBAAmB;YACrB,IAAIkB;YAEJ,IAAI/E,oBAAoB;gBACtB,MAAMiE,kBAAkBvH,sCACtB+E,eACA1F;gBAGFgJ,8BACE,KAACzI;oBACCqH,WAAWgB;oBACXK,OAAO3B;oBACPe,QAAQ3C;oBACRwD,SAAShB;;YAGf,OAAO;gBACLc,8BACE,KAACzI;oBACCqH,WAAWgB;oBACXK,OAAO3B;oBACPe,QAAQ3C;;YAGd;YAEA,IAAImD,mDAAmD;gBACrD,IAAIM;gBACJ,IAAIC;gBACJ,IAAIC;gBACJ,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCF,wBAAwBG,qCAAqC;oBAC3DC,wBAAwB/F;oBACxBgG,cAAcxD;oBACdzF;oBACA2B;oBACA0G;oBACAlD;gBACF;gBACA0D,yBAAyBE,qCAAqC;oBAC5DC,wBAAwB7F;oBACxB8F,cAAcvD;oBACd1F;oBACA2B;oBACA0G;oBACAlD;gBACF;gBACA2D,4BAA4BC,qCAAqC;oBAC/DC,wBAAwB3F;oBACxB4F,cAActD;oBACd3F;oBACA2B;oBACA0G;oBACAlD;gBACF;gBACA,IACEyD,yBACAC,0BACAC,2BACA;oBACAN,4BACE,MAAC7I;wBAECyB,UAAUwH;wBACVvH,WAAWwH;wBACXvH,cAAcwH;;4BAEbnH;4BACA8G;;uBANIlK;gBASX,OAAO;oBACLiK,4BACE,MAAC3L,MAAMoF,QAAQ;;4BACZN;4BACA8G;;uBAFkBlK;gBAKzB;YACF,OAAO;gBACLiK,4BACE,MAAC3L,MAAMoF,QAAQ;;wBACZN;wBACA8G;;mBAFkBlK;YAKzB;QACF,OAAO;YACL,MAAMuJ,SAAS3H,mCACbgF,eACA1F;YAGF,IAAIyJ,8BACF,KAACb;gBAAkB,GAAGtB,kBAAkB;gBAAEe,QAAQA;;YAGpD,IAAIQ,mDAAmD;gBACrD,gGAAgG;gBAChG,uFAAuF;gBACvF,4GAA4G;gBAC5G,gHAAgH;gBAChH,mCAAmC;gBACnCE,4BACE,MAAC7I;oBAECyB,UACE6B,yBACE;;4BACGtB;0CACD,MAAC0G;gCAAiBP,QAAQA;;oCACvB5E;kDACD,KAACD;;;;yBAGH0B;;wBAGLhD;wBACAuH;;mBAdI3K;YAiBX,OAAO;gBACLiK,4BACE,MAAC3L,MAAMoF,QAAQ;;wBACZN;wBACAuH;;mBAFkB3K;YAKzB;QACF;QACA,2CAA2C;QAC3C,OAAO;YACL+G;YACAkD;YACAxB;YACAG;YACA1C;SACD;IACH;AACF;AAEA,eAAe0D,eAAe,EAC5BC,KAAK,EAGN;IACC,MAAMe,IAAIf;IACV,+FAA+F;IAC/F,IAAIe,EAAEC,MAAM,KAAK,YAAY;QAC3B,MAAMD,EAAE/D,KAAK;IACf,OAAO,IAAI+D,EAAEC,MAAM,KAAK,aAAa;QACnC,MAAMD;IACR;IACA,OAAO;AACT;AACAhB,eAAekB,WAAW,GAAGzL;AAE7B,SAASmL,qCAAqC,EAC5CC,sBAAsB,EACtBC,YAAY,EACZjJ,iBAAiB,EACjB2B,WAAW,EACX0G,gBAAgB,EAChBlD,aAAa,EAQd;IACC,IAAI6D,wBAAwB;QAC1B,MAAMM,6BAA6B;YACjClC,UAAU6B;QACZ;QACA,qBACE;;gBACGtH;8BACD,KAAC3B;oBACCqH,WAAWgB;oBACXK,OAAOY;oBACPxB,QAAQ3C;;;;IAIhB;IACA,OAAO;AACT;AAEA,OAAO,SAASoE,cACd/K,UAAsB,EACtBgC,0BAAsD;IAEtD,OAAOgJ,kBAAkB,CAAC,GAAGhL,YAAYgC;AAC3C;AAEA,SAASgJ,kBACP9K,YAAoB,EACpBF,UAAsB,EACtBgC,0BAAsD;IAEtD,MAAM,EACJK,OAAO,EACPC,SAAS,EAAEE,MAAM,EAAE,EACnBD,cAAc,EACf,GAAG7D,gBAAgBsB;IAEpB,MAAM0G,eAAe1E,2BAA2BK;IAEhD,IAAIsE,gBAAwBzG;IAC5B,IAAIwG,gBAAgBA,aAAaE,KAAK,KAAK,MAAM;QAC/CD,gBAAgB;YACd,GAAGzG,YAAY;YACf,CAACwG,aAAaG,KAAK,CAAC,EAAEH,aAAaE,KAAK;QAC1C;IACF;IAEA,MAAMqE,eAAe,OAAOzI,WAAW;IAEvC,IAAIyI,cAAc;QAChB,OAAOtE;IACT,OAAO,IAAI,CAACpE,eAAeqG,QAAQ,EAAE;QACnC,4EAA4E;QAC5E,yEAAyE;QACzE,yEAAyE;QACzE,6BAA6B;QAC7B,wFAAwF;QACxF,OAAOjC;IACT,OAAO;QACL,OAAOqE,kBACLrE,eACA,qEAAqE;QACrE,8EAA8E;QAC9E,+EAA+E;QAC/E,+CAA+C;QAC/CpE,eAAeqG,QAAQ,EACvB5G;IAEJ;AACF"}