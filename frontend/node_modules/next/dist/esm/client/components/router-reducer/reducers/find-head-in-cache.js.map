{"version": 3, "sources": ["../../../../../src/client/components/router-reducer/reducers/find-head-in-cache.ts"], "sourcesContent": ["import type { FlightRouterState } from '../../../../server/app-render/types'\nimport type { CacheNode } from '../../../../shared/lib/app-router-context.shared-runtime'\nimport { createRouterCacheKey } from '../create-router-cache-key'\n\nexport function findHeadInCache(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1]\n): [CacheNode, string] | null {\n  return findHeadInCacheImpl(cache, parallelRoutes, '')\n}\n\nfunction findHeadInCacheImpl(\n  cache: CacheNode,\n  parallelRoutes: FlightRouterState[1],\n  keyPrefix: string\n): [CacheNode, string] | null {\n  const isLastItem = Object.keys(parallelRoutes).length === 0\n  if (isLastItem) {\n    // Returns the entire Cache Node of the segment whose head we will render.\n    return [cache, keyPrefix]\n  }\n\n  // First try the 'children' parallel route if it exists\n  // when starting from the \"root\", this corresponds with the main page component\n  const parallelRoutesKeys = Object.keys(parallelRoutes).filter(\n    (key) => key !== 'children'\n  )\n\n  // if we are at the root, we need to check the children slot first\n  if ('children' in parallelRoutes) {\n    parallelRoutesKeys.unshift('children')\n  }\n\n  for (const key of parallelRoutesKeys) {\n    const [segment, childParallelRoutes] = parallelRoutes[key]\n    const childSegmentMap = cache.parallelRoutes.get(key)\n    if (!childSegmentMap) {\n      continue\n    }\n\n    const cacheKey = createRouterCacheKey(segment)\n\n    const cacheNode = childSegmentMap.get(cacheKey)\n    if (!cacheNode) {\n      continue\n    }\n\n    const item = findHeadInCacheImpl(\n      cacheNode,\n      childParallelRoutes,\n      keyPrefix + '/' + cacheKey\n    )\n    if (item) {\n      return item\n    }\n  }\n\n  return null\n}\n"], "names": ["createRouterCache<PERSON>ey", "findHeadInCache", "cache", "parallelRoutes", "findHeadInCacheImpl", "keyPrefix", "isLastItem", "Object", "keys", "length", "parallelRoutesKeys", "filter", "key", "unshift", "segment", "childPara<PERSON>l<PERSON><PERSON><PERSON>", "childSegmentMap", "get", "cache<PERSON>ey", "cacheNode", "item"], "mappings": "AAEA,SAASA,oBAAoB,QAAQ,6BAA4B;AAEjE,OAAO,SAASC,gBACdC,KAAgB,EAChBC,cAAoC;IAEpC,OAAOC,oBAAoBF,OAAOC,gBAAgB;AACpD;AAEA,SAASC,oBACPF,KAAgB,EAChBC,cAAoC,EACpCE,SAAiB;IAEjB,MAAMC,aAAaC,OAAOC,IAAI,CAACL,gBAAgBM,MAAM,KAAK;IAC1D,IAAIH,YAAY;QACd,0EAA0E;QAC1E,OAAO;YAACJ;YAAOG;SAAU;IAC3B;IAEA,uDAAuD;IACvD,+EAA+E;IAC/E,MAAMK,qBAAqBH,OAAOC,IAAI,CAACL,gBAAgBQ,MAAM,CAC3D,CAACC,MAAQA,QAAQ;IAGnB,kEAAkE;IAClE,IAAI,cAAcT,gBAAgB;QAChCO,mBAAmBG,OAAO,CAAC;IAC7B;IAEA,KAAK,MAAMD,OAAOF,mBAAoB;QACpC,MAAM,CAACI,SAASC,oBAAoB,GAAGZ,cAAc,CAACS,IAAI;QAC1D,MAAMI,kBAAkBd,MAAMC,cAAc,CAACc,GAAG,CAACL;QACjD,IAAI,CAACI,iBAAiB;YACpB;QACF;QAEA,MAAME,WAAWlB,qBAAqBc;QAEtC,MAAMK,YAAYH,gBAAgBC,GAAG,CAACC;QACtC,IAAI,CAACC,WAAW;YACd;QACF;QAEA,MAAMC,OAAOhB,oBACXe,WACAJ,qBACAV,YAAY,MAAMa;QAEpB,IAAIE,MAAM;YACR,OAAOA;QACT;IACF;IAEA,OAAO;AACT"}