{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/components/ImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\n\ninterface ImageUploadProps {\n  onAnalysisStart: () => void;\n  onAnalysisComplete: (result: any) => void;\n  isAnalyzing: boolean;\n}\n\nexport default function ImageUpload({ onAnalysisStart, onAnalysisComplete, isAnalyzing }: ImageUploadProps) {\n  const [uploadedImage, setUploadedImage] = useState<string | null>(null);\n  const [uploadError, setUploadError] = useState<string | null>(null);\n  const [analysisStage, setAnalysisStage] = useState<string>('');\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const file = acceptedFiles[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        setUploadError('Please upload a valid image file');\n        return;\n      }\n\n      // Validate file size (max 10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        setUploadError('File size must be less than 10MB');\n        return;\n      }\n\n      setUploadError(null);\n      \n      // Create preview\n      const reader = new FileReader();\n      reader.onload = () => {\n        setUploadedImage(reader.result as string);\n      };\n      reader.readAsDataURL(file);\n\n      // Start analysis\n      analyzeChart(file);\n    }\n  }, []);\n\n  const analyzeChart = async (file: File) => {\n    onAnalysisStart();\n    setAnalysisStage('Uploading image...');\n\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('coin_symbol', 'BTC'); // Default for now\n      formData.append('timeframe', '1h');\n      formData.append('chart_type', 'candlestick');\n\n      setAnalysisStage('Collecting market data...');\n\n      const response = await fetch('http://localhost:8000/api/v1/analyze-chart-enhanced', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        throw new Error(`Analysis failed: ${response.statusText}`);\n      }\n\n      setAnalysisStage('Processing with AI vision...');\n\n      // Check if response is streaming or has progress updates\n      const reader = response.body?.getReader();\n      if (reader) {\n        const decoder = new TextDecoder();\n        let result = '';\n\n        while (true) {\n          const { done, value } = await reader.read();\n          if (done) break;\n\n          const chunk = decoder.decode(value);\n          result += chunk;\n\n          // Check for progress updates in the stream\n          if (chunk.includes('stage:')) {\n            const stageMatch = chunk.match(/stage:\\s*([^,\\n]+)/);\n            if (stageMatch) {\n              setAnalysisStage(stageMatch[1]);\n            }\n          }\n        }\n\n        const finalResult = JSON.parse(result);\n        setAnalysisStage('Analysis complete!');\n        onAnalysisComplete(finalResult);\n      } else {\n        const result = await response.json();\n        setAnalysisStage('Analysis complete!');\n        onAnalysisComplete(result);\n      }\n    } catch (error) {\n      console.error('Analysis error:', error);\n      setUploadError(error instanceof Error ? error.message : 'Analysis failed');\n      setAnalysisStage('');\n      onAnalysisComplete(null);\n    }\n  };\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.bmp', '.webp']\n    },\n    multiple: false,\n    disabled: isAnalyzing\n  });\n\n  const removeImage = () => {\n    setUploadedImage(null);\n    setUploadError(null);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {!uploadedImage ? (\n        <div\n          {...getRootProps()}\n          className={`\n            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n            ${isDragActive \n              ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' \n              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'\n            }\n            ${isAnalyzing ? 'opacity-50 cursor-not-allowed' : ''}\n          `}\n        >\n          <input {...getInputProps()} />\n          <div className=\"space-y-4\">\n            <div className=\"mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n              </svg>\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {isDragActive ? 'Drop your chart here' : 'Upload Chart Image'}\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n                Drag and drop or click to select a crypto chart screenshot\n              </p>\n              <p className=\"text-xs text-gray-400 dark:text-gray-500 mt-2\">\n                Supports: JPG, PNG, GIF, BMP, WebP (max 10MB)\n              </p>\n            </div>\n          </div>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          <div className=\"relative\">\n            <img\n              src={uploadedImage}\n              alt=\"Uploaded chart\"\n              className=\"w-full h-64 object-contain bg-gray-50 dark:bg-gray-700 rounded-lg border\"\n            />\n            {!isAnalyzing && (\n              <button\n                onClick={removeImage}\n                className=\"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            )}\n            {isAnalyzing && (\n              <div className=\"absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center\">\n                <div className=\"text-center text-white\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2\"></div>\n                  <p className=\"text-sm font-medium\">AI Chart Analysis</p>\n                  <p className=\"text-xs mt-1 opacity-80\">\n                    {analysisStage || 'Processing...'}\n                  </p>\n                  <div className=\"mt-3 flex justify-center space-x-1\">\n                    <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n                    <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n                    <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n          \n          {!isAnalyzing && (\n            <div className=\"flex justify-center\">\n              <button\n                onClick={() => analyzeChart(new File([], 'reanalyze'))}\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n              >\n                Re-analyze Chart\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {uploadError && (\n        <div className=\"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-5 h-5 text-red-400 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <p className=\"text-sm text-red-700 dark:text-red-400\">{uploadError}</p>\n          </div>\n        </div>\n      )}\n\n      <div className=\"text-xs text-gray-500 dark:text-gray-400 space-y-1\">\n        <p>• Upload clear, high-quality chart screenshots for best results</p>\n        <p>• Ensure the chart shows price data, timeframe, and indicators clearly</p>\n        <p>• Supported exchanges: Binance, Coinbase, TradingView, and more</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAWe,SAAS,YAAY,EAAE,eAAe,EAAE,kBAAkB,EAAE,WAAW,EAAoB;;IACxG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAE3D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,CAAC;YAC1B,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,IAAI,MAAM;gBACR,qBAAqB;gBACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBACnC,eAAe;oBACf;gBACF;gBAEA,gCAAgC;gBAChC,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;oBAChC,eAAe;oBACf;gBACF;gBAEA,eAAe;gBAEf,iBAAiB;gBACjB,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM;uDAAG;wBACd,iBAAiB,OAAO,MAAM;oBAChC;;gBACA,OAAO,aAAa,CAAC;gBAErB,iBAAiB;gBACjB,aAAa;YACf;QACF;0CAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B;QACA,iBAAiB;QAEjB,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,eAAe,QAAQ,kBAAkB;YACzD,SAAS,MAAM,CAAC,aAAa;YAC7B,SAAS,MAAM,CAAC,cAAc;YAE9B,iBAAiB;YAEjB,MAAM,WAAW,MAAM,MAAM,uDAAuD;gBAClF,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,SAAS,UAAU,EAAE;YAC3D;YAEA,iBAAiB;YAEjB,yDAAyD;YACzD,MAAM,SAAS,SAAS,IAAI,EAAE;YAC9B,IAAI,QAAQ;gBACV,MAAM,UAAU,IAAI;gBACpB,IAAI,SAAS;gBAEb,MAAO,KAAM;oBACX,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,IAAI;oBACzC,IAAI,MAAM;oBAEV,MAAM,QAAQ,QAAQ,MAAM,CAAC;oBAC7B,UAAU;oBAEV,2CAA2C;oBAC3C,IAAI,MAAM,QAAQ,CAAC,WAAW;wBAC5B,MAAM,aAAa,MAAM,KAAK,CAAC;wBAC/B,IAAI,YAAY;4BACd,iBAAiB,UAAU,CAAC,EAAE;wBAChC;oBACF;gBACF;gBAEA,MAAM,cAAc,KAAK,KAAK,CAAC;gBAC/B,iBAAiB;gBACjB,mBAAmB;YACrB,OAAO;gBACL,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,iBAAiB;gBACjB,mBAAmB;YACrB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACxD,iBAAiB;YACjB,mBAAmB;QACrB;IACF;IAEA,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;gBAAQ;gBAAQ;aAAQ;QAC/D;QACA,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,cAAc;QAClB,iBAAiB;QACjB,eAAe;IACjB;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,CAAC,8BACA,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;YAEV,EAAE,eACE,mDACA,wFACH;YACD,EAAE,cAAc,kCAAkC,GAAG;UACvD,CAAC;;kCAED,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,eAAe,yBAAyB;;;;;;kDAE3C,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;kDAG7D,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;qCAOnE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;;4BAEX,CAAC,6BACA,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;4BAI1E,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,6LAAC;4CAAE,WAAU;sDACV,iBAAiB;;;;;;sDAEpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;oDAA8C,OAAO;wDAAE,gBAAgB;oDAAO;;;;;;8DAC7F,6LAAC;oDAAI,WAAU;oDAA8C,OAAO;wDAAE,gBAAgB;oDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOtG,CAAC,6BACA,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,aAAa,IAAI,KAAK,EAAE,EAAE;4BACzC,WAAU;sCACX;;;;;;;;;;;;;;;;;YAQR,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAA4B,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACnF,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAK7D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAE;;;;;;kCACH,6LAAC;kCAAE;;;;;;kCACH,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;AAIX;GAnNwB;;QAgGgC,2KAAA,CAAA,cAAW;;;KAhG3C", "debugId": null}}, {"offset": {"line": 457, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/components/AnalysisResults.tsx"], "sourcesContent": ["'use client';\n\ninterface AnalysisResultsProps {\n  result: any;\n}\n\nexport default function AnalysisResults({ result }: AnalysisResultsProps) {\n  if (!result) {\n    return (\n      <div className=\"text-center py-8 text-gray-500 dark:text-gray-400\">\n        <p>No analysis results available</p>\n      </div>\n    );\n  }\n\n  const getRecommendationColor = (recommendation: string) => {\n    switch (recommendation?.toLowerCase()) {\n      case 'strong_buy':\n        return 'bg-green-500 text-white';\n      case 'buy':\n        return 'bg-green-400 text-white';\n      case 'hold':\n        return 'bg-yellow-500 text-white';\n      case 'sell':\n        return 'bg-red-400 text-white';\n      case 'strong_sell':\n        return 'bg-red-500 text-white';\n      default:\n        return 'bg-gray-500 text-white';\n    }\n  };\n\n  const getRecommendationText = (recommendation: string) => {\n    switch (recommendation?.toLowerCase()) {\n      case 'strong_buy':\n        return 'Strong Buy';\n      case 'buy':\n        return 'Buy';\n      case 'hold':\n        return 'Hold';\n      case 'sell':\n        return 'Sell';\n      case 'strong_sell':\n        return 'Strong Sell';\n      default:\n        return 'Unknown';\n    }\n  };\n\n  const formatConfidence = (confidence: number) => {\n    return `${Math.round(confidence * 100)}%`;\n  };\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(price);\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Main Recommendation */}\n      <div className=\"text-center\">\n        <div className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold ${getRecommendationColor(result.predictions?.recommendation)}`}>\n          {getRecommendationText(result.predictions?.recommendation)}\n        </div>\n        <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-2\">\n          Confidence: {formatConfidence(result.predictions?.confidence || 0)}\n        </p>\n      </div>\n\n      {/* Key Metrics */}\n      <div className=\"grid grid-cols-2 gap-4\">\n        <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide\">Target Price</p>\n          <p className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {result.predictions?.target_price ? formatPrice(result.predictions.target_price) : 'N/A'}\n          </p>\n        </div>\n        <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n          <p className=\"text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide\">Time Horizon</p>\n          <p className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n            {result.predictions?.time_horizon || 'N/A'}\n          </p>\n        </div>\n      </div>\n\n      {/* Risk Assessment */}\n      {result.predictions?.risk_assessment && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Risk Assessment</h4>\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Risk Level:</span>\n              <span className={`text-sm font-medium ${\n                result.predictions.risk_assessment.risk_level === 'high' ? 'text-red-600' :\n                result.predictions.risk_assessment.risk_level === 'medium' ? 'text-yellow-600' :\n                'text-green-600'\n              }`}>\n                {result.predictions.risk_assessment.risk_level?.toUpperCase()}\n              </span>\n            </div>\n            {result.predictions.risk_assessment.stop_loss && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Stop Loss:</span>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {formatPrice(result.predictions.risk_assessment.stop_loss)}\n                </span>\n              </div>\n            )}\n            {result.predictions.risk_assessment.take_profit && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Take Profit:</span>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {formatPrice(result.predictions.risk_assessment.take_profit)}\n                </span>\n              </div>\n            )}\n            {result.predictions.risk_assessment.risk_reward_ratio && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Risk/Reward:</span>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  1:{result.predictions.risk_assessment.risk_reward_ratio}\n                </span>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Technical Indicators */}\n      {result.technical_indicators && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Technical Indicators</h4>\n          <div className=\"space-y-3\">\n            {result.technical_indicators.rsi && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">RSI:</span>\n                <span className={`text-sm font-medium ${\n                  result.technical_indicators.rsi > 70 ? 'text-red-600' :\n                  result.technical_indicators.rsi < 30 ? 'text-green-600' :\n                  'text-gray-900 dark:text-white'\n                }`}>\n                  {result.technical_indicators.rsi.toFixed(1)}\n                </span>\n              </div>\n            )}\n            \n            {result.technical_indicators.macd && (\n              <div className=\"space-y-1\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">MACD:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {result.technical_indicators.macd.macd?.toFixed(4)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Signal:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {result.technical_indicators.macd.signal?.toFixed(4)}\n                  </span>\n                </div>\n              </div>\n            )}\n\n            {result.technical_indicators.moving_averages && (\n              <div className=\"space-y-1\">\n                {Object.entries(result.technical_indicators.moving_averages).map(([key, value]) => (\n                  <div key={key} className=\"flex justify-between\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">{key.toUpperCase()}:</span>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {formatPrice(value as number)}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Detected Patterns */}\n      {result.chart_data?.detected_patterns && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Detected Patterns</h4>\n          <div className=\"space-y-2\">\n            {result.chart_data.detected_patterns.trend_direction && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Trend:</span>\n                <span className={`text-sm font-medium ${\n                  result.chart_data.detected_patterns.trend_direction === 'bullish' ? 'text-green-600' :\n                  result.chart_data.detected_patterns.trend_direction === 'bearish' ? 'text-red-600' :\n                  'text-gray-900 dark:text-white'\n                }`}>\n                  {result.chart_data.detected_patterns.trend_direction?.toUpperCase()}\n                </span>\n              </div>\n            )}\n            \n            {result.chart_data.detected_patterns.candlestick_patterns?.length > 0 && (\n              <div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Candlestick Patterns:</span>\n                <div className=\"mt-1 flex flex-wrap gap-1\">\n                  {result.chart_data.detected_patterns.candlestick_patterns.map((pattern: string, index: number) => (\n                    <span key={index} className=\"inline-block px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\">\n                      {pattern}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {result.chart_data.detected_patterns.chart_patterns?.length > 0 && (\n              <div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Chart Patterns:</span>\n                <div className=\"mt-1 flex flex-wrap gap-1\">\n                  {result.chart_data.detected_patterns.chart_patterns.map((pattern: string, index: number) => (\n                    <span key={index} className=\"inline-block px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded\">\n                      {pattern}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Multi-Factor Analysis Scores */}\n      {result.multi_factor_analysis && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Multi-Factor Analysis</h4>\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Technical Score:</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-blue-500 h-2 rounded-full\"\n                    style={{ width: `${(result.multi_factor_analysis.technical_score || 0) * 100}%` }}\n                  ></div>\n                </div>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {((result.multi_factor_analysis.technical_score || 0) * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Historical Score:</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-green-500 h-2 rounded-full\"\n                    style={{ width: `${(result.multi_factor_analysis.historical_score || 0) * 100}%` }}\n                  ></div>\n                </div>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {((result.multi_factor_analysis.historical_score || 0) * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Fundamental Score:</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-purple-500 h-2 rounded-full\"\n                    style={{ width: `${(result.multi_factor_analysis.fundamental_score || 0) * 100}%` }}\n                  ></div>\n                </div>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {((result.multi_factor_analysis.fundamental_score || 0) * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Sentiment Score:</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-yellow-500 h-2 rounded-full\"\n                    style={{ width: `${(result.multi_factor_analysis.sentiment_score || 0) * 100}%` }}\n                  ></div>\n                </div>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {((result.multi_factor_analysis.sentiment_score || 0) * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n\n            <div className=\"pt-2 border-t border-gray-200 dark:border-gray-600\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Final Score:</span>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n                    <div\n                      className=\"bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-3 rounded-full\"\n                      style={{ width: `${(result.multi_factor_analysis.final_score || 0) * 100}%` }}\n                    ></div>\n                  </div>\n                  <span className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                    {((result.multi_factor_analysis.final_score || 0) * 100).toFixed(0)}%\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Detailed AI Reasoning */}\n      {result.prediction?.reasoning && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">\n            🧠 Detailed AI Reasoning\n          </h4>\n\n          {/* Technical Reasoning */}\n          {result.prediction.reasoning.technical_factors && (\n            <div className=\"mb-4\">\n              <h5 className=\"text-xs font-medium text-blue-600 dark:text-blue-400 mb-2\">📈 TECHNICAL ANALYSIS</h5>\n              <ul className=\"space-y-1\">\n                {result.prediction.reasoning.technical_factors.map((factor: string, index: number) => (\n                  <li key={index} className=\"text-xs text-gray-600 dark:text-gray-400 flex items-start\">\n                    <span className=\"text-blue-500 mr-2\">•</span>\n                    {factor}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n\n          {/* Historical Reasoning */}\n          {result.prediction.reasoning.historical_factors && (\n            <div className=\"mb-4\">\n              <h5 className=\"text-xs font-medium text-green-600 dark:text-green-400 mb-2\">📊 HISTORICAL ANALYSIS</h5>\n              <ul className=\"space-y-1\">\n                {result.prediction.reasoning.historical_factors.map((factor: string, index: number) => (\n                  <li key={index} className=\"text-xs text-gray-600 dark:text-gray-400 flex items-start\">\n                    <span className=\"text-green-500 mr-2\">•</span>\n                    {factor}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n\n          {/* Fundamental Reasoning */}\n          {result.prediction.reasoning.fundamental_factors && (\n            <div className=\"mb-4\">\n              <h5 className=\"text-xs font-medium text-purple-600 dark:text-purple-400 mb-2\">💰 FUNDAMENTAL ANALYSIS</h5>\n              <ul className=\"space-y-1\">\n                {result.prediction.reasoning.fundamental_factors.map((factor: string, index: number) => (\n                  <li key={index} className=\"text-xs text-gray-600 dark:text-gray-400 flex items-start\">\n                    <span className=\"text-purple-500 mr-2\">•</span>\n                    {factor}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n\n          {/* Risk Factors */}\n          {result.prediction.reasoning.risk_factors && (\n            <div className=\"mb-4\">\n              <h5 className=\"text-xs font-medium text-red-600 dark:text-red-400 mb-2\">⚠️ RISK FACTORS</h5>\n              <ul className=\"space-y-1\">\n                {result.prediction.reasoning.risk_factors.map((factor: string, index: number) => (\n                  <li key={index} className=\"text-xs text-gray-600 dark:text-gray-400 flex items-start\">\n                    <span className=\"text-red-500 mr-2\">•</span>\n                    {factor}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n\n          {/* Target Rationale */}\n          {result.prediction.reasoning.target_rationale && (\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n              <h5 className=\"text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">🎯 TARGET RATIONALE</h5>\n              <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                {result.prediction.reasoning.target_rationale}\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Market Context */}\n      {result.market_context && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">📊 Market Context</h4>\n          <div className=\"grid grid-cols-2 gap-3 text-xs\">\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">Market Phase:</span>\n              <p className=\"font-medium text-gray-900 dark:text-white capitalize\">\n                {result.market_context.market_phase}\n              </p>\n            </div>\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">Trend Strength:</span>\n              <p className=\"font-medium text-gray-900 dark:text-white\">\n                {(result.market_context.trend_strength * 100).toFixed(0)}%\n              </p>\n            </div>\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">24h Change:</span>\n              <p className={`font-medium ${result.market_context.price_change_24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                {result.market_context.price_change_24h >= 0 ? '+' : ''}{result.market_context.price_change_24h.toFixed(2)}%\n              </p>\n            </div>\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">7d Change:</span>\n              <p className={`font-medium ${result.market_context.price_change_7d >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                {result.market_context.price_change_7d >= 0 ? '+' : ''}{result.market_context.price_change_7d.toFixed(2)}%\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Analysis Metadata */}\n      <div className=\"text-xs text-gray-500 dark:text-gray-400 pt-4 border-t border-gray-200 dark:border-gray-600\">\n        <p>Analysis ID: {result.analysis_id}</p>\n        <p>Timestamp: {new Date(result.timestamp).toLocaleString()}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;;AAMe,SAAS,gBAAgB,EAAE,MAAM,EAAwB;IACtE,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;0BAAE;;;;;;;;;;;IAGT;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ,gBAAgB;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,wBAAwB,CAAC;QAC7B,OAAQ,gBAAgB;YACtB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,OAAO,GAAG,KAAK,KAAK,CAAC,aAAa,KAAK,CAAC,CAAC;IAC3C;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAW,CAAC,kEAAkE,EAAE,uBAAuB,OAAO,WAAW,EAAE,iBAAiB;kCAC9I,sBAAsB,OAAO,WAAW,EAAE;;;;;;kCAE7C,6LAAC;wBAAE,WAAU;;4BAAgD;4BAC9C,iBAAiB,OAAO,WAAW,EAAE,cAAc;;;;;;;;;;;;;0BAKpE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAmE;;;;;;0CAChF,6LAAC;gCAAE,WAAU;0CACV,OAAO,WAAW,EAAE,eAAe,YAAY,OAAO,WAAW,CAAC,YAAY,IAAI;;;;;;;;;;;;kCAGvF,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAE,WAAU;0CAAmE;;;;;;0CAChF,6LAAC;gCAAE,WAAU;0CACV,OAAO,WAAW,EAAE,gBAAgB;;;;;;;;;;;;;;;;;;YAM1C,OAAO,WAAW,EAAE,iCACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,WAAW,CAAC,eAAe,CAAC,UAAU,KAAK,SAAS,iBAC3D,OAAO,WAAW,CAAC,eAAe,CAAC,UAAU,KAAK,WAAW,oBAC7D,kBACA;kDACC,OAAO,WAAW,CAAC,eAAe,CAAC,UAAU,EAAE;;;;;;;;;;;;4BAGnD,OAAO,WAAW,CAAC,eAAe,CAAC,SAAS,kBAC3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAU;kDACb,YAAY,OAAO,WAAW,CAAC,eAAe,CAAC,SAAS;;;;;;;;;;;;4BAI9D,OAAO,WAAW,CAAC,eAAe,CAAC,WAAW,kBAC7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAU;kDACb,YAAY,OAAO,WAAW,CAAC,eAAe,CAAC,WAAW;;;;;;;;;;;;4BAIhE,OAAO,WAAW,CAAC,eAAe,CAAC,iBAAiB,kBACnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAU;;4CAAoD;4CAC/D,OAAO,WAAW,CAAC,eAAe,CAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YASlE,OAAO,oBAAoB,kBAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;4BACZ,OAAO,oBAAoB,CAAC,GAAG,kBAC9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,oBAAoB,CAAC,GAAG,GAAG,KAAK,iBACvC,OAAO,oBAAoB,CAAC,GAAG,GAAG,KAAK,mBACvC,iCACA;kDACC,OAAO,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;4BAK9C,OAAO,oBAAoB,CAAC,IAAI,kBAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAK,WAAU;0DACb,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ;;;;;;;;;;;;kDAGpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAK,WAAU;0DACb,OAAO,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;;;;;;;;;;;;;;;;;;4BAMzD,OAAO,oBAAoB,CAAC,eAAe,kBAC1C,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,OAAO,oBAAoB,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC5E,6LAAC;wCAAc,WAAU;;0DACvB,6LAAC;gDAAK,WAAU;;oDAA4C,IAAI,WAAW;oDAAG;;;;;;;0DAC9E,6LAAC;gDAAK,WAAU;0DACb,YAAY;;;;;;;uCAHP;;;;;;;;;;;;;;;;;;;;;;YAcrB,OAAO,UAAU,EAAE,mCAClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;4BACZ,OAAO,UAAU,CAAC,iBAAiB,CAAC,eAAe,kBAClD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,UAAU,CAAC,iBAAiB,CAAC,eAAe,KAAK,YAAY,mBACpE,OAAO,UAAU,CAAC,iBAAiB,CAAC,eAAe,KAAK,YAAY,iBACpE,iCACA;kDACC,OAAO,UAAU,CAAC,iBAAiB,CAAC,eAAe,EAAE;;;;;;;;;;;;4BAK3D,OAAO,UAAU,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,SAAS,mBAClE,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;kDACZ,OAAO,UAAU,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,SAAiB,sBAC9E,6LAAC;gDAAiB,WAAU;0DACzB;+CADQ;;;;;;;;;;;;;;;;4BAQlB,OAAO,UAAU,CAAC,iBAAiB,CAAC,cAAc,EAAE,SAAS,mBAC5D,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;kDACZ,OAAO,UAAU,CAAC,iBAAiB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,SAAiB,sBACxE,6LAAC;gDAAiB,WAAU;0DACzB;+CADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYxB,OAAO,qBAAqB,kBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,eAAe,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGpF,6LAAC;gDAAK,WAAU;;oDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,eAAe,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,gBAAgB,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGrF,6LAAC;gDAAK,WAAU;;oDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,gBAAgB,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK/E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,iBAAiB,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGtF,6LAAC;gDAAK,WAAU;;oDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,iBAAiB,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAKhF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,eAAe,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGpF,6LAAC;gDAAK,WAAU;;oDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,eAAe,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK9E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAuD;;;;;;sDACvE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;wDAAC;;;;;;;;;;;8DAGhF,6LAAC;oDAAK,WAAU;;wDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,WAAW,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjF,OAAO,UAAU,EAAE,2BAClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;oBAKtE,OAAO,UAAU,CAAC,SAAS,CAAC,iBAAiB,kBAC5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAG,WAAU;0CACX,OAAO,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,QAAgB,sBAClE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;4CACpC;;uCAFM;;;;;;;;;;;;;;;;oBAUhB,OAAO,UAAU,CAAC,SAAS,CAAC,kBAAkB,kBAC7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAC5E,6LAAC;gCAAG,WAAU;0CACX,OAAO,UAAU,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,QAAgB,sBACnE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;4CACrC;;uCAFM;;;;;;;;;;;;;;;;oBAUhB,OAAO,UAAU,CAAC,SAAS,CAAC,mBAAmB,kBAC9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgE;;;;;;0CAC9E,6LAAC;gCAAG,WAAU;0CACX,OAAO,UAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAgB,sBACpE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;0DAAuB;;;;;;4CACtC;;uCAFM;;;;;;;;;;;;;;;;oBAUhB,OAAO,UAAU,CAAC,SAAS,CAAC,YAAY,kBACvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0D;;;;;;0CACxE,6LAAC;gCAAG,WAAU;0CACX,OAAO,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,QAAgB,sBAC7D,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;4CACnC;;uCAFM;;;;;;;;;;;;;;;;oBAUhB,OAAO,UAAU,CAAC,SAAS,CAAC,gBAAgB,kBAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAE,WAAU;0CACV,OAAO,UAAU,CAAC,SAAS,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;YAQtD,OAAO,cAAc,kBACpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAE,WAAU;kDACV,OAAO,cAAc,CAAC,YAAY;;;;;;;;;;;;0CAGvC,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAE,WAAU;;4CACV,CAAC,OAAO,cAAc,CAAC,cAAc,GAAG,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAG7D,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAE,WAAW,CAAC,YAAY,EAAE,OAAO,cAAc,CAAC,gBAAgB,IAAI,IAAI,mBAAmB,gBAAgB;;4CAC3G,OAAO,cAAc,CAAC,gBAAgB,IAAI,IAAI,MAAM;4CAAI,OAAO,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAG/G,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAE,WAAW,CAAC,YAAY,EAAE,OAAO,cAAc,CAAC,eAAe,IAAI,IAAI,mBAAmB,gBAAgB;;4CAC1G,OAAO,cAAc,CAAC,eAAe,IAAI,IAAI,MAAM;4CAAI,OAAO,cAAc,CAAC,eAAe,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAE;4BAAc,OAAO,WAAW;;;;;;;kCACnC,6LAAC;;4BAAE;4BAAY,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;AAIhE;KA7awB", "debugId": null}}, {"offset": {"line": 1694, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/components/ParameterForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface ParameterFormProps {\n  onParametersChange?: (params: any) => void;\n}\n\nexport default function ParameterForm({ onParametersChange }: ParameterFormProps) {\n  const [parameters, setParameters] = useState({\n    coinSymbol: 'BTC',\n    timeframe: '1h',\n    chartType: 'candlestick',\n    additionalNotes: ''\n  });\n\n  const [supportedCoins, setSupportedCoins] = useState<string[]>([]);\n  const [timeframes, setTimeframes] = useState<any[]>([]);\n\n  useEffect(() => {\n    // Fetch supported coins and timeframes from API\n    fetchSupportedOptions();\n  }, []);\n\n  useEffect(() => {\n    // Notify parent component of parameter changes\n    if (onParametersChange) {\n      onParametersChange(parameters);\n    }\n  }, [parameters, onParametersChange]);\n\n  const fetchSupportedOptions = async () => {\n    try {\n      // Fetch supported coins\n      const coinsResponse = await fetch('http://localhost:8000/api/v1/supported-coins');\n      if (coinsResponse.ok) {\n        const coinsData = await coinsResponse.json();\n        setSupportedCoins(coinsData.supported_coins || []);\n      }\n\n      // Fetch timeframes\n      const timeframesResponse = await fetch('http://localhost:8000/api/v1/timeframes');\n      if (timeframesResponse.ok) {\n        const timeframesData = await timeframesResponse.json();\n        setTimeframes(timeframesData.timeframes || []);\n      }\n    } catch (error) {\n      console.error('Error fetching options:', error);\n      // Set default values if API is not available\n      setSupportedCoins(['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK']);\n      setTimeframes([\n        { value: '1m', label: '1 Minute' },\n        { value: '5m', label: '5 Minutes' },\n        { value: '15m', label: '15 Minutes' },\n        { value: '1h', label: '1 Hour' },\n        { value: '4h', label: '4 Hours' },\n        { value: '1d', label: '1 Day' },\n        { value: '1w', label: '1 Week' }\n      ]);\n    }\n  };\n\n  const handleInputChange = (field: string, value: string) => {\n    setParameters(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Coin Symbol */}\n      <div>\n        <label htmlFor=\"coinSymbol\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Cryptocurrency Symbol\n        </label>\n        <select\n          id=\"coinSymbol\"\n          value={parameters.coinSymbol}\n          onChange={(e) => handleInputChange('coinSymbol', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n        >\n          {supportedCoins.map(coin => (\n            <option key={coin} value={coin}>{coin}</option>\n          ))}\n        </select>\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          Select the cryptocurrency shown in your chart\n        </p>\n      </div>\n\n      {/* Timeframe */}\n      <div>\n        <label htmlFor=\"timeframe\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Chart Timeframe\n        </label>\n        <select\n          id=\"timeframe\"\n          value={parameters.timeframe}\n          onChange={(e) => handleInputChange('timeframe', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n        >\n          {timeframes.map(tf => (\n            <option key={tf.value} value={tf.value}>{tf.label}</option>\n          ))}\n        </select>\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          The time interval of each candle/data point\n        </p>\n      </div>\n\n      {/* Chart Type */}\n      <div>\n        <label htmlFor=\"chartType\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Chart Type\n        </label>\n        <select\n          id=\"chartType\"\n          value={parameters.chartType}\n          onChange={(e) => handleInputChange('chartType', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n        >\n          <option value=\"candlestick\">Candlestick</option>\n          <option value=\"line\">Line Chart</option>\n          <option value=\"area\">Area Chart</option>\n          <option value=\"ohlc\">OHLC Bars</option>\n        </select>\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          The type of chart visualization\n        </p>\n      </div>\n\n      {/* Additional Notes */}\n      <div>\n        <label htmlFor=\"additionalNotes\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Additional Notes (Optional)\n        </label>\n        <textarea\n          id=\"additionalNotes\"\n          value={parameters.additionalNotes}\n          onChange={(e) => handleInputChange('additionalNotes', e.target.value)}\n          rows={3}\n          placeholder=\"Any specific indicators, patterns, or context you'd like the AI to consider...\"\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500\"\n        />\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          Provide context about indicators, market conditions, or specific analysis requests\n        </p>\n      </div>\n\n      {/* Analysis Options */}\n      <div className=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n        <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Analysis Focus</h4>\n        <div className=\"grid grid-cols-2 gap-3\">\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              defaultChecked\n              className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n            />\n            <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">Technical Indicators</span>\n          </label>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              defaultChecked\n              className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n            />\n            <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">Pattern Recognition</span>\n          </label>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              defaultChecked\n              className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n            />\n            <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">Volume Analysis</span>\n          </label>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              defaultChecked\n              className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n            />\n            <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">Risk Assessment</span>\n          </label>\n        </div>\n      </div>\n\n      {/* Current Parameters Summary */}\n      <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n        <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Current Settings</h4>\n        <div className=\"text-xs text-gray-600 dark:text-gray-400 space-y-1\">\n          <p><span className=\"font-medium\">Coin:</span> {parameters.coinSymbol}</p>\n          <p><span className=\"font-medium\">Timeframe:</span> {parameters.timeframe}</p>\n          <p><span className=\"font-medium\">Chart Type:</span> {parameters.chartType}</p>\n          {parameters.additionalNotes && (\n            <p><span className=\"font-medium\">Notes:</span> {parameters.additionalNotes.substring(0, 50)}{parameters.additionalNotes.length > 50 ? '...' : ''}</p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAQe,SAAS,cAAc,EAAE,kBAAkB,EAAsB;;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,YAAY;QACZ,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,gDAAgD;YAChD;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,+CAA+C;YAC/C,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;QACF;kCAAG;QAAC;QAAY;KAAmB;IAEnC,MAAM,wBAAwB;QAC5B,IAAI;YACF,wBAAwB;YACxB,MAAM,gBAAgB,MAAM,MAAM;YAClC,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,kBAAkB,UAAU,eAAe,IAAI,EAAE;YACnD;YAEA,mBAAmB;YACnB,MAAM,qBAAqB,MAAM,MAAM;YACvC,IAAI,mBAAmB,EAAE,EAAE;gBACzB,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;gBACpD,cAAc,eAAe,UAAU,IAAI,EAAE;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,6CAA6C;YAC7C,kBAAkB;gBAAC;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;aAAO;YAC7D,cAAc;gBACZ;oBAAE,OAAO;oBAAM,OAAO;gBAAW;gBACjC;oBAAE,OAAO;oBAAM,OAAO;gBAAY;gBAClC;oBAAE,OAAO;oBAAO,OAAO;gBAAa;gBACpC;oBAAE,OAAO;oBAAM,OAAO;gBAAS;gBAC/B;oBAAE,OAAO;oBAAM,OAAO;gBAAU;gBAChC;oBAAE,OAAO;oBAAM,OAAO;gBAAQ;gBAC9B;oBAAE,OAAO;oBAAM,OAAO;gBAAS;aAChC;QACH;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAa,WAAU;kCAAkE;;;;;;kCAGxG,6LAAC;wBACC,IAAG;wBACH,OAAO,WAAW,UAAU;wBAC5B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC/D,WAAU;kCAET,eAAe,GAAG,CAAC,CAAA,qBAClB,6LAAC;gCAAkB,OAAO;0CAAO;+BAApB;;;;;;;;;;kCAGjB,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAY,WAAU;kCAAkE;;;;;;kCAGvG,6LAAC;wBACC,IAAG;wBACH,OAAO,WAAW,SAAS;wBAC3B,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC9D,WAAU;kCAET,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gCAAsB,OAAO,GAAG,KAAK;0CAAG,GAAG,KAAK;+BAApC,GAAG,KAAK;;;;;;;;;;kCAGzB,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAY,WAAU;kCAAkE;;;;;;kCAGvG,6LAAC;wBACC,IAAG;wBACH,OAAO,WAAW,SAAS;wBAC3B,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC9D,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAc;;;;;;0CAC5B,6LAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6LAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6LAAC;gCAAO,OAAM;0CAAO;;;;;;;;;;;;kCAEvB,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAkB,WAAU;kCAAkE;;;;;;kCAG7G,6LAAC;wBACC,IAAG;wBACH,OAAO,WAAW,eAAe;wBACjC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;wBACpE,MAAM;wBACN,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,cAAc;wCACd,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;0CAElE,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,cAAc;wCACd,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;0CAElE,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,cAAc;wCACd,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;0CAElE,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,cAAc;wCACd,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;0BAMtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDAAE,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAY;oCAAE,WAAW,UAAU;;;;;;;0CACpE,6LAAC;;kDAAE,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAiB;oCAAE,WAAW,SAAS;;;;;;;0CACxE,6LAAC;;kDAAE,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAkB;oCAAE,WAAW,SAAS;;;;;;;4BACxE,WAAW,eAAe,kBACzB,6LAAC;;kDAAE,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAa;oCAAE,WAAW,eAAe,CAAC,SAAS,CAAC,GAAG;oCAAK,WAAW,eAAe,CAAC,MAAM,GAAG,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;AAM1J;GAnMwB;KAAA", "debugId": null}}, {"offset": {"line": 2237, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport ImageUpload from '@/components/ImageUpload';\nimport AnalysisResults from '@/components/AnalysisResults';\nimport ParameterForm from '@/components/ParameterForm';\n\nexport default function Home() {\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n\n  const handleAnalysisComplete = (result: any) => {\n    setAnalysisResult(result);\n    setIsAnalyzing(false);\n  };\n\n  const handleAnalysisStart = () => {\n    setIsAnalyzing(true);\n    setAnalysisResult(null);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">AI</span>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  Crypto Chart Analysis\n                </h1>\n                <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                  AI-powered cryptocurrency chart analysis with position recommendations\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\n                ● Live\n              </span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Upload Section */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n                Upload Chart Image\n              </h2>\n              <ImageUpload\n                onAnalysisStart={handleAnalysisStart}\n                onAnalysisComplete={handleAnalysisComplete}\n                isAnalyzing={isAnalyzing}\n              />\n            </div>\n\n            {/* Parameters Section */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n                Analysis Parameters\n              </h2>\n              <ParameterForm />\n            </div>\n          </div>\n\n          {/* Results Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sticky top-8\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n                Analysis Results\n              </h2>\n              {isAnalyzing ? (\n                <div className=\"flex items-center justify-center py-12\">\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n                </div>\n              ) : analysisResult ? (\n                <AnalysisResults result={analysisResult} />\n              ) : (\n                <div className=\"text-center py-12 text-gray-500 dark:text-gray-400\">\n                  <div className=\"w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-sm\">Upload a chart to see AI analysis results</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Features Section */}\n        <div className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"w-12 h-12 mx-auto mb-4 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">AI-Powered Analysis</h3>\n            <p className=\"text-gray-600 dark:text-gray-300 text-sm\">Advanced computer vision and machine learning for accurate chart interpretation</p>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"w-12 h-12 mx-auto mb-4 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">Technical Indicators</h3>\n            <p className=\"text-gray-600 dark:text-gray-300 text-sm\">RSI, MACD, Moving Averages, Bollinger Bands, and more technical analysis tools</p>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"w-12 h-12 mx-auto mb-4 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">Position Recommendations</h3>\n            <p className=\"text-gray-600 dark:text-gray-300 text-sm\">Clear buy/sell/hold signals with confidence levels and risk assessment</p>\n          </div>\n        </div>\n      </main>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,yBAAyB,CAAC;QAC9B,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,sBAAsB;QAC1B,eAAe;QACf,kBAAkB;IACpB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAK5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA4I;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpK,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAGzE,6LAAC,oIAAA,CAAA,UAAW;gDACV,iBAAiB;gDACjB,oBAAoB;gDACpB,aAAa;;;;;;;;;;;;kDAKjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAGzE,6LAAC,sIAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;0CAKlB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2D;;;;;;wCAGxE,4BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;;;;;;;;;mDAEf,+BACF,6LAAC,wIAAA,CAAA,UAAe;4CAAC,QAAQ;;;;;iEAEzB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAA2C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAClG,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAG1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAA6C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACpG,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAG1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAA+C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtG,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMpE;GAhIwB;KAAA", "debugId": null}}]}