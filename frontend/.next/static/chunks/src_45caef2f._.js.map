{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/components/ImageUpload.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useCallback } from 'react';\nimport { useDropzone } from 'react-dropzone';\n\ninterface ImageUploadProps {\n  onAnalysisStart: () => void;\n  onAnalysisComplete: (result: any) => void;\n  isAnalyzing: boolean;\n}\n\nexport default function ImageUpload({ onAnalysisStart, onAnalysisComplete, isAnalyzing }: ImageUploadProps) {\n  const [uploadedImage, setUploadedImage] = useState<string | null>(null);\n  const [uploadError, setUploadError] = useState<string | null>(null);\n  const [analysisStage, setAnalysisStage] = useState<string>('');\n  const [originalFile, setOriginalFile] = useState<File | null>(null);\n\n  const onDrop = useCallback((acceptedFiles: File[]) => {\n    const file = acceptedFiles[0];\n    if (file) {\n      // Validate file type\n      if (!file.type.startsWith('image/')) {\n        setUploadError('Please upload a valid image file');\n        return;\n      }\n\n      // Validate file size (max 10MB)\n      if (file.size > 10 * 1024 * 1024) {\n        setUploadError('File size must be less than 10MB');\n        return;\n      }\n\n      setUploadError(null);\n\n      // Store original file for re-analysis\n      setOriginalFile(file);\n\n      // Create preview\n      const reader = new FileReader();\n      reader.onload = () => {\n        setUploadedImage(reader.result as string);\n      };\n      reader.readAsDataURL(file);\n\n      // Start analysis\n      analyzeChart(file);\n    }\n  }, []);\n\n  const analyzeChart = async (file: File) => {\n    onAnalysisStart();\n    setAnalysisStage('Uploading image...');\n\n    try {\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('coin_symbol', 'BTC'); // Default for now\n      formData.append('timeframe', '1h');\n      formData.append('chart_type', 'candlestick');\n\n      setAnalysisStage('Collecting market data...');\n\n      const response = await fetch('http://localhost:8000/api/v1/analyze-chart', {\n        method: 'POST',\n        body: formData,\n      });\n\n      if (!response.ok) {\n        const errorText = await response.text();\n        console.error('Backend error:', response.status, errorText);\n        throw new Error(`Analysis failed: ${response.statusText} - ${errorText}`);\n      }\n\n      setAnalysisStage('Processing with AI vision...');\n\n      // Handle JSON response directly (no streaming for now)\n      const result = await response.json();\n      setAnalysisStage('Analysis complete!');\n      onAnalysisComplete(result);\n    } catch (error) {\n      console.error('Analysis error:', error);\n      setUploadError(error instanceof Error ? error.message : 'Analysis failed');\n      setAnalysisStage('');\n      onAnalysisComplete(null);\n    }\n  };\n\n  const { getRootProps, getInputProps, isDragActive } = useDropzone({\n    onDrop,\n    accept: {\n      'image/*': ['.jpeg', '.jpg', '.png', '.gif', '.bmp', '.webp']\n    },\n    multiple: false,\n    disabled: isAnalyzing\n  });\n\n  const removeImage = () => {\n    setUploadedImage(null);\n    setUploadError(null);\n    setOriginalFile(null);\n    setAnalysisStage('');\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      {!uploadedImage ? (\n        <div\n          {...getRootProps()}\n          className={`\n            border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors\n            ${isDragActive \n              ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' \n              : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'\n            }\n            ${isAnalyzing ? 'opacity-50 cursor-not-allowed' : ''}\n          `}\n        >\n          <input {...getInputProps()} />\n          <div className=\"space-y-4\">\n            <div className=\"mx-auto w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12\" />\n              </svg>\n            </div>\n            <div>\n              <p className=\"text-lg font-medium text-gray-900 dark:text-white\">\n                {isDragActive ? 'Drop your chart here' : 'Upload Chart Image'}\n              </p>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400 mt-1\">\n                Drag and drop or click to select a crypto chart screenshot\n              </p>\n              <p className=\"text-xs text-gray-400 dark:text-gray-500 mt-2\">\n                Supports: JPG, PNG, GIF, BMP, WebP (max 10MB)\n              </p>\n            </div>\n          </div>\n        </div>\n      ) : (\n        <div className=\"space-y-4\">\n          <div className=\"relative\">\n            <img\n              src={uploadedImage}\n              alt=\"Uploaded chart\"\n              className=\"w-full h-64 object-contain bg-gray-50 dark:bg-gray-700 rounded-lg border\"\n            />\n            {!isAnalyzing && (\n              <button\n                onClick={removeImage}\n                className=\"absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors\"\n              >\n                <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n                </svg>\n              </button>\n            )}\n            {isAnalyzing && (\n              <div className=\"absolute inset-0 bg-black bg-opacity-50 rounded-lg flex items-center justify-center\">\n                <div className=\"text-center text-white\">\n                  <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto mb-2\"></div>\n                  <p className=\"text-sm font-medium\">AI Chart Analysis</p>\n                  <p className=\"text-xs mt-1 opacity-80\">\n                    {analysisStage || 'Processing...'}\n                  </p>\n                  <div className=\"mt-3 flex justify-center space-x-1\">\n                    <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\"></div>\n                    <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\" style={{ animationDelay: '0.2s' }}></div>\n                    <div className=\"w-2 h-2 bg-white rounded-full animate-pulse\" style={{ animationDelay: '0.4s' }}></div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n          \n          {!isAnalyzing && originalFile && (\n            <div className=\"flex justify-center\">\n              <button\n                onClick={() => analyzeChart(originalFile)}\n                className=\"px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors\"\n              >\n                Re-analyze Chart\n              </button>\n            </div>\n          )}\n        </div>\n      )}\n\n      {uploadError && (\n        <div className=\"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg\">\n          <div className=\"flex items-center\">\n            <svg className=\"w-5 h-5 text-red-400 mr-2\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\" />\n            </svg>\n            <p className=\"text-sm text-red-700 dark:text-red-400\">{uploadError}</p>\n          </div>\n        </div>\n      )}\n\n      <div className=\"text-xs text-gray-500 dark:text-gray-400 space-y-1\">\n        <p>• Upload clear, high-quality chart screenshots for best results</p>\n        <p>• Ensure the chart shows price data, timeframe, and indicators clearly</p>\n        <p>• Supported exchanges: Binance, Coinbase, TradingView, and more</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAWe,SAAS,YAAY,EAAE,eAAe,EAAE,kBAAkB,EAAE,WAAW,EAAoB;;IACxG,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAC9D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAe;IAE9D,MAAM,SAAS,CAAA,GAAA,6JAAA,CAAA,cAAW,AAAD;2CAAE,CAAC;YAC1B,MAAM,OAAO,aAAa,CAAC,EAAE;YAC7B,IAAI,MAAM;gBACR,qBAAqB;gBACrB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;oBACnC,eAAe;oBACf;gBACF;gBAEA,gCAAgC;gBAChC,IAAI,KAAK,IAAI,GAAG,KAAK,OAAO,MAAM;oBAChC,eAAe;oBACf;gBACF;gBAEA,eAAe;gBAEf,sCAAsC;gBACtC,gBAAgB;gBAEhB,iBAAiB;gBACjB,MAAM,SAAS,IAAI;gBACnB,OAAO,MAAM;uDAAG;wBACd,iBAAiB,OAAO,MAAM;oBAChC;;gBACA,OAAO,aAAa,CAAC;gBAErB,iBAAiB;gBACjB,aAAa;YACf;QACF;0CAAG,EAAE;IAEL,MAAM,eAAe,OAAO;QAC1B;QACA,iBAAiB;QAEjB,IAAI;YACF,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,eAAe,QAAQ,kBAAkB;YACzD,SAAS,MAAM,CAAC,aAAa;YAC7B,SAAS,MAAM,CAAC,cAAc;YAE9B,iBAAiB;YAEjB,MAAM,WAAW,MAAM,MAAM,8CAA8C;gBACzE,QAAQ;gBACR,MAAM;YACR;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,QAAQ,KAAK,CAAC,kBAAkB,SAAS,MAAM,EAAE;gBACjD,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YAC1E;YAEA,iBAAiB;YAEjB,uDAAuD;YACvD,MAAM,SAAS,MAAM,SAAS,IAAI;YAClC,iBAAiB;YACjB,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,mBAAmB;YACjC,eAAe,iBAAiB,QAAQ,MAAM,OAAO,GAAG;YACxD,iBAAiB;YACjB,mBAAmB;QACrB;IACF;IAEA,MAAM,EAAE,YAAY,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,CAAA,GAAA,2KAAA,CAAA,cAAW,AAAD,EAAE;QAChE;QACA,QAAQ;YACN,WAAW;gBAAC;gBAAS;gBAAQ;gBAAQ;gBAAQ;gBAAQ;aAAQ;QAC/D;QACA,UAAU;QACV,UAAU;IACZ;IAEA,MAAM,cAAc;QAClB,iBAAiB;QACjB,eAAe;QACf,gBAAgB;QAChB,iBAAiB;IACnB;IAEA,qBACE,6LAAC;QAAI,WAAU;;YACZ,CAAC,8BACA,6LAAC;gBACE,GAAG,cAAc;gBAClB,WAAW,CAAC;;YAEV,EAAE,eACE,mDACA,wFACH;YACD,EAAE,cAAc,kCAAkC,GAAG;UACvD,CAAC;;kCAED,6LAAC;wBAAO,GAAG,eAAe;;;;;;kCAC1B,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAAwB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAC/E,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,6LAAC;;kDACC,6LAAC;wCAAE,WAAU;kDACV,eAAe,yBAAyB;;;;;;kDAE3C,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;kDAG7D,6LAAC;wCAAE,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;qCAOnE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,KAAK;gCACL,KAAI;gCACJ,WAAU;;;;;;4BAEX,CAAC,6BACA,6LAAC;gCACC,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;4BAI1E,6BACC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAE,WAAU;sDAAsB;;;;;;sDACnC,6LAAC;4CAAE,WAAU;sDACV,iBAAiB;;;;;;sDAEpB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;;;;;8DACf,6LAAC;oDAAI,WAAU;oDAA8C,OAAO;wDAAE,gBAAgB;oDAAO;;;;;;8DAC7F,6LAAC;oDAAI,WAAU;oDAA8C,OAAO;wDAAE,gBAAgB;oDAAO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAOtG,CAAC,eAAe,8BACf,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BACC,SAAS,IAAM,aAAa;4BAC5B,WAAU;sCACX;;;;;;;;;;;;;;;;;YAQR,6BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;4BAA4B,MAAK;4BAAO,QAAO;4BAAe,SAAQ;sCACnF,cAAA,6LAAC;gCAAK,eAAc;gCAAQ,gBAAe;gCAAQ,aAAa;gCAAG,GAAE;;;;;;;;;;;sCAEvE,6LAAC;4BAAE,WAAU;sCAA0C;;;;;;;;;;;;;;;;;0BAK7D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;kCAAE;;;;;;kCACH,6LAAC;kCAAE;;;;;;kCACH,6LAAC;kCAAE;;;;;;;;;;;;;;;;;;AAIX;GAjMwB;;QA4EgC,2KAAA,CAAA,cAAW;;;KA5E3C", "debugId": null}}, {"offset": {"line": 442, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/components/AnalysisResults.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\n\ninterface AnalysisResultsProps {\n  result: any;\n}\n\nexport default function AnalysisResults({ result }: AnalysisResultsProps) {\n  const [activeTab, setActiveTab] = useState('overview');\n  if (!result) {\n    return (\n      <div className=\"text-center py-12 text-gray-500 dark:text-gray-400\">\n        <div className=\"w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center\">\n          <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n          </svg>\n        </div>\n        <p className=\"text-sm\">Upload a chart to see AI analysis results</p>\n      </div>\n    );\n  }\n\n  const formatPrice = (price: number) => {\n    return new Intl.NumberFormat('en-US', {\n      style: 'currency',\n      currency: 'USD',\n      minimumFractionDigits: 2,\n      maximumFractionDigits: 2\n    }).format(price);\n  };\n\n  const getRecommendationColor = (recommendation: string) => {\n    switch (recommendation?.toLowerCase()) {\n      case 'strong_buy':\n      case 'buy':\n        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900';\n      case 'strong_sell':\n      case 'sell':\n        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900';\n      case 'hold':\n        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900';\n      default:\n        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900';\n    }\n  };\n\n  const formatRecommendation = (recommendation: string) => {\n    return recommendation?.replace('_', ' ').toUpperCase() || 'UNKNOWN';\n  };\n\n  const getScoreColor = (score: number) => {\n    if (score >= 0.8) return 'bg-green-500';\n    if (score >= 0.6) return 'bg-yellow-500';\n    return 'bg-red-500';\n  };\n\n  const currentPrice = result.market_context?.current_price || 0;\n  const targetPrice = result.predictions?.target_price || 0;\n  const priceChange = targetPrice > 0 ? ((targetPrice - currentPrice) / currentPrice) * 100 : 0;\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Enhanced Header with Action Button */}\n      <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-lg p-6\">\n        <div className=\"flex items-center justify-between mb-6\">\n          <div>\n            <h2 className=\"text-3xl font-bold text-gray-900 dark:text-white\">\n              Analysis Results\n            </h2>\n            <p className=\"text-sm text-gray-600 dark:text-gray-400 mt-1\">\n              {new Date(result.timestamp).toLocaleString()}\n            </p>\n          </div>\n          <div className={`px-4 py-2 rounded-full text-sm font-medium ${getRecommendationColor(result.predictions?.recommendation)}`}>\n            {formatRecommendation(result.predictions?.recommendation)}\n          </div>\n        </div>\n\n        {/* Enhanced Key Metrics Grid */}\n        <div className=\"grid grid-cols-2 md:grid-cols-5 gap-4\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 text-center\">\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {((result.predictions?.confidence || 0) * 100).toFixed(0)}%\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">Confidence</div>\n            <div className=\"w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2\">\n              <div\n                className={`h-2 rounded-full ${getScoreColor(result.predictions?.confidence || 0)}`}\n                style={{ width: `${(result.predictions?.confidence || 0) * 100}%` }}\n              ></div>\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 text-center\">\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              ${targetPrice.toLocaleString()}\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">Target Price</div>\n            <div className={`text-xs mt-1 ${priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n              {priceChange > 0 ? '+' : ''}{priceChange.toFixed(1)}%\n            </div>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 text-center\">\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {result.predictions?.time_horizon || 'N/A'}\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">Time Horizon</div>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 text-center\">\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white capitalize\">\n              {result.predictions?.risk_assessment?.risk_level || 'N/A'}\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">Risk Level</div>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg p-4 text-center\">\n            <div className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n              {result.predictions?.risk_assessment?.risk_reward_ratio || 'N/A'}\n            </div>\n            <div className=\"text-sm text-gray-500 dark:text-gray-400\">Risk/Reward</div>\n          </div>\n        </div>\n      </div>\n\n      {/* Tabs Navigation */}\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg\">\n        <div className=\"border-b border-gray-200 dark:border-gray-700\">\n          <nav className=\"flex space-x-8 px-6\">\n            {[\n              { id: 'overview', label: 'Overview', icon: '📊' },\n              { id: 'technical', label: 'Technical', icon: '📈' },\n              { id: 'reasoning', label: 'AI Reasoning', icon: '🧠' },\n              { id: 'market', label: 'Market Context', icon: '🌍' }\n            ].map((tab) => (\n              <button\n                key={tab.id}\n                onClick={() => setActiveTab(tab.id)}\n                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${\n                  activeTab === tab.id\n                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'\n                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'\n                }`}\n              >\n                <span>{tab.icon}</span>\n                <span>{tab.label}</span>\n              </button>\n            ))}\n          </nav>\n        </div>\n\n        <div className=\"p-6\">\n          {activeTab === 'overview' && (\n            <div className=\"space-y-6\">\n              {/* Multi-factor Analysis */}\n              <div>\n                <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                  Multi-Factor Analysis\n                </h3>\n                <div className=\"space-y-3\">\n                  {result.multi_factor_analysis && Object.entries(result.multi_factor_analysis).map(([key, value]) => {\n                    if (key === 'final_score') return null;\n                    return (\n                      <div key={key} className=\"flex items-center justify-between\">\n                        <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize\">\n                          {key.replace('_', ' ')}\n                        </span>\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                            <div\n                              className={`h-2 rounded-full ${getScoreColor(value as number)}`}\n                              style={{ width: `${(value as number) * 100}%` }}\n                            ></div>\n                          </div>\n                          <span className=\"text-sm font-medium text-gray-900 dark:text-white w-12\">\n                            {((value as number) * 100).toFixed(0)}%\n                          </span>\n                        </div>\n                      </div>\n                    );\n                  })}\n                </div>\n              </div>\n\n              {/* Final Score */}\n              {result.multi_factor_analysis?.final_score && (\n                <div className=\"bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between\">\n                    <span className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                      Overall Score\n                    </span>\n                    <span className=\"text-3xl font-bold text-blue-600 dark:text-blue-400\">\n                      {(result.multi_factor_analysis.final_score * 100).toFixed(0)}%\n                    </span>\n                  </div>\n                </div>\n              )}\n            </div>\n          )}\n\n          {activeTab === 'technical' && (\n            <div className=\"space-y-4\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                Technical Indicators & Patterns\n              </h3>\n              {result.technical_indicators ? (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {Object.entries(result.technical_indicators).map(([key, value]) => (\n                    <div key={key} className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n                      <div className=\"text-sm font-medium text-gray-700 dark:text-gray-300 capitalize\">\n                        {key.replace('_', ' ')}\n                      </div>\n                      <div className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                        {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              ) : (\n                <p className=\"text-gray-500 dark:text-gray-400\">Technical analysis data will appear here</p>\n              )}\n            </div>\n          )}\n\n          {activeTab === 'reasoning' && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-4\">\n                🧠 AI Reasoning & Analysis\n              </h3>\n              {result.prediction?.reasoning ? (\n                Object.entries(result.prediction.reasoning).map(([category, factors]) => {\n                  if (category === 'target_rationale') {\n                    return (\n                      <div key={category} className=\"bg-blue-50 dark:bg-blue-900 rounded-lg p-4\">\n                        <h4 className=\"text-md font-semibold text-blue-900 dark:text-blue-100 mb-2\">\n                          🎯 Target Price Rationale\n                        </h4>\n                        <p className=\"text-blue-800 dark:text-blue-200\">{factors as string}</p>\n                      </div>\n                    );\n                  }\n                  return (\n                    <div key={category} className=\"border-l-4 border-blue-500 pl-4\">\n                      <h4 className=\"text-md font-semibold text-gray-900 dark:text-white mb-2 capitalize\">\n                        {category.includes('technical') && '📈 '}\n                        {category.includes('historical') && '📊 '}\n                        {category.includes('fundamental') && '💰 '}\n                        {category.includes('risk') && '⚠️ '}\n                        {category.replace('_', ' ')}\n                      </h4>\n                      <ul className=\"space-y-2\">\n                        {(factors as string[]).map((factor, index) => (\n                          <li key={index} className=\"text-gray-700 dark:text-gray-300 text-sm flex items-start\">\n                            <span className=\"text-blue-500 mr-2 mt-1\">•</span>\n                            <span>{factor}</span>\n                          </li>\n                        ))}\n                      </ul>\n                    </div>\n                  );\n                })\n              ) : (\n                <p className=\"text-gray-500 dark:text-gray-400\">AI reasoning will appear here after analysis</p>\n              )}\n            </div>\n          )}\n\n          {activeTab === 'market' && (\n            <div className=\"space-y-6\">\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">\n                🌍 Market Context & Data\n              </h3>\n              {result.market_context ? (\n                <div className=\"space-y-4\">\n                  <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n                    <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center\">\n                      <div className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                        Current Price\n                      </div>\n                      <div className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                        ${result.market_context.current_price?.toLocaleString()}\n                      </div>\n                    </div>\n                    <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center\">\n                      <div className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                        24h Change\n                      </div>\n                      <div className={`text-xl font-bold ${\n                        result.market_context.price_change_24h >= 0\n                          ? 'text-green-600 dark:text-green-400'\n                          : 'text-red-600 dark:text-red-400'\n                      }`}>\n                        {result.market_context.price_change_24h >= 0 ? '+' : ''}{result.market_context.price_change_24h?.toFixed(2)}%\n                      </div>\n                    </div>\n                    <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center\">\n                      <div className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                        Market Phase\n                      </div>\n                      <div className=\"text-xl font-bold text-gray-900 dark:text-white capitalize\">\n                        {result.market_context.market_phase}\n                      </div>\n                    </div>\n                    <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center\">\n                      <div className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                        Volatility\n                      </div>\n                      <div className=\"text-xl font-bold text-gray-900 dark:text-white\">\n                        {result.market_context.volatility_30d?.toFixed(1)}%\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Support & Resistance */}\n                  {(result.market_context.support_levels?.length > 0 || result.market_context.resistance_levels?.length > 0) && (\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                      {result.market_context.support_levels?.length > 0 && (\n                        <div className=\"bg-green-50 dark:bg-green-900 rounded-lg p-4\">\n                          <h4 className=\"text-sm font-semibold text-green-800 dark:text-green-200 mb-2\">Support Levels</h4>\n                          <div className=\"space-y-1\">\n                            {result.market_context.support_levels.map((level, index) => (\n                              <div key={index} className=\"text-green-700 dark:text-green-300 font-medium\">\n                                ${level.toLocaleString()}\n                              </div>\n                            ))}\n                          </div>\n                        </div>\n                      )}\n\n                      {result.market_context.resistance_levels?.length > 0 && (\n                        <div className=\"bg-red-50 dark:bg-red-900 rounded-lg p-4\">\n                          <h4 className=\"text-sm font-semibold text-red-800 dark:text-red-200 mb-2\">Resistance Levels</h4>\n                          <div className=\"space-y-1\">\n                            {result.market_context.resistance_levels.map((level, index) => (\n                              <div key={index} className=\"text-red-700 dark:text-red-300 font-medium\">\n                                ${level.toLocaleString()}\n                              </div>\n                            ))}\n                          </div>\n                        </div>\n                      )}\n                    </div>\n                  )}\n                </div>\n              ) : (\n                <p className=\"text-gray-500 dark:text-gray-400\">Market context data will appear here</p>\n              )}\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* Risk Assessment */}\n      {result.predictions?.risk_assessment && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Risk Assessment</h4>\n          <div className=\"space-y-2\">\n            <div className=\"flex justify-between\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Risk Level:</span>\n              <span className={`text-sm font-medium ${\n                result.predictions.risk_assessment.risk_level === 'high' ? 'text-red-600' :\n                result.predictions.risk_assessment.risk_level === 'medium' ? 'text-yellow-600' :\n                'text-green-600'\n              }`}>\n                {result.predictions.risk_assessment.risk_level?.toUpperCase()}\n              </span>\n            </div>\n            {result.predictions.risk_assessment.stop_loss && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Stop Loss:</span>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {formatPrice(result.predictions.risk_assessment.stop_loss)}\n                </span>\n              </div>\n            )}\n            {result.predictions.risk_assessment.take_profit && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Take Profit:</span>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {formatPrice(result.predictions.risk_assessment.take_profit)}\n                </span>\n              </div>\n            )}\n            {result.predictions.risk_assessment.risk_reward_ratio && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Risk/Reward:</span>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  1:{result.predictions.risk_assessment.risk_reward_ratio}\n                </span>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Technical Indicators */}\n      {result.technical_indicators && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Technical Indicators</h4>\n          <div className=\"space-y-3\">\n            {result.technical_indicators.rsi && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">RSI:</span>\n                <span className={`text-sm font-medium ${\n                  result.technical_indicators.rsi > 70 ? 'text-red-600' :\n                  result.technical_indicators.rsi < 30 ? 'text-green-600' :\n                  'text-gray-900 dark:text-white'\n                }`}>\n                  {result.technical_indicators.rsi.toFixed(1)}\n                </span>\n              </div>\n            )}\n            \n            {result.technical_indicators.macd && (\n              <div className=\"space-y-1\">\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">MACD:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {result.technical_indicators.macd.macd?.toFixed(4)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between\">\n                  <span className=\"text-sm text-gray-600 dark:text-gray-400\">Signal:</span>\n                  <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                    {result.technical_indicators.macd.signal?.toFixed(4)}\n                  </span>\n                </div>\n              </div>\n            )}\n\n            {result.technical_indicators.moving_averages && (\n              <div className=\"space-y-1\">\n                {Object.entries(result.technical_indicators.moving_averages).map(([key, value]) => (\n                  <div key={key} className=\"flex justify-between\">\n                    <span className=\"text-sm text-gray-600 dark:text-gray-400\">{key.toUpperCase()}:</span>\n                    <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                      {formatPrice(value as number)}\n                    </span>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Detected Patterns */}\n      {result.chart_data?.detected_patterns && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Detected Patterns</h4>\n          <div className=\"space-y-2\">\n            {result.chart_data.detected_patterns.trend_direction && (\n              <div className=\"flex justify-between\">\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Trend:</span>\n                <span className={`text-sm font-medium ${\n                  result.chart_data.detected_patterns.trend_direction === 'bullish' ? 'text-green-600' :\n                  result.chart_data.detected_patterns.trend_direction === 'bearish' ? 'text-red-600' :\n                  'text-gray-900 dark:text-white'\n                }`}>\n                  {result.chart_data.detected_patterns.trend_direction?.toUpperCase()}\n                </span>\n              </div>\n            )}\n            \n            {result.chart_data.detected_patterns.candlestick_patterns?.length > 0 && (\n              <div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Candlestick Patterns:</span>\n                <div className=\"mt-1 flex flex-wrap gap-1\">\n                  {result.chart_data.detected_patterns.candlestick_patterns.map((pattern: string, index: number) => (\n                    <span key={index} className=\"inline-block px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded\">\n                      {pattern}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {result.chart_data.detected_patterns.chart_patterns?.length > 0 && (\n              <div>\n                <span className=\"text-sm text-gray-600 dark:text-gray-400\">Chart Patterns:</span>\n                <div className=\"mt-1 flex flex-wrap gap-1\">\n                  {result.chart_data.detected_patterns.chart_patterns.map((pattern: string, index: number) => (\n                    <span key={index} className=\"inline-block px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded\">\n                      {pattern}\n                    </span>\n                  ))}\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      )}\n\n      {/* Multi-Factor Analysis Scores */}\n      {result.multi_factor_analysis && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">Multi-Factor Analysis</h4>\n          <div className=\"space-y-3\">\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Technical Score:</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-blue-500 h-2 rounded-full\"\n                    style={{ width: `${(result.multi_factor_analysis.technical_score || 0) * 100}%` }}\n                  ></div>\n                </div>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {((result.multi_factor_analysis.technical_score || 0) * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Historical Score:</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-green-500 h-2 rounded-full\"\n                    style={{ width: `${(result.multi_factor_analysis.historical_score || 0) * 100}%` }}\n                  ></div>\n                </div>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {((result.multi_factor_analysis.historical_score || 0) * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Fundamental Score:</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-purple-500 h-2 rounded-full\"\n                    style={{ width: `${(result.multi_factor_analysis.fundamental_score || 0) * 100}%` }}\n                  ></div>\n                </div>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {((result.multi_factor_analysis.fundamental_score || 0) * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n\n            <div className=\"flex justify-between items-center\">\n              <span className=\"text-sm text-gray-600 dark:text-gray-400\">Sentiment Score:</span>\n              <div className=\"flex items-center space-x-2\">\n                <div className=\"w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2\">\n                  <div\n                    className=\"bg-yellow-500 h-2 rounded-full\"\n                    style={{ width: `${(result.multi_factor_analysis.sentiment_score || 0) * 100}%` }}\n                  ></div>\n                </div>\n                <span className=\"text-sm font-medium text-gray-900 dark:text-white\">\n                  {((result.multi_factor_analysis.sentiment_score || 0) * 100).toFixed(0)}%\n                </span>\n              </div>\n            </div>\n\n            <div className=\"pt-2 border-t border-gray-200 dark:border-gray-600\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-sm font-medium text-gray-700 dark:text-gray-300\">Final Score:</span>\n                <div className=\"flex items-center space-x-2\">\n                  <div className=\"w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-3\">\n                    <div\n                      className=\"bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-3 rounded-full\"\n                      style={{ width: `${(result.multi_factor_analysis.final_score || 0) * 100}%` }}\n                    ></div>\n                  </div>\n                  <span className=\"text-lg font-bold text-gray-900 dark:text-white\">\n                    {((result.multi_factor_analysis.final_score || 0) * 100).toFixed(0)}%\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Detailed AI Reasoning */}\n      {result.prediction?.reasoning && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">\n            🧠 Detailed AI Reasoning\n          </h4>\n\n          {/* Technical Reasoning */}\n          {result.prediction.reasoning.technical_factors && (\n            <div className=\"mb-4\">\n              <h5 className=\"text-xs font-medium text-blue-600 dark:text-blue-400 mb-2\">📈 TECHNICAL ANALYSIS</h5>\n              <ul className=\"space-y-1\">\n                {result.prediction.reasoning.technical_factors.map((factor: string, index: number) => (\n                  <li key={index} className=\"text-xs text-gray-600 dark:text-gray-400 flex items-start\">\n                    <span className=\"text-blue-500 mr-2\">•</span>\n                    {factor}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n\n          {/* Historical Reasoning */}\n          {result.prediction.reasoning.historical_factors && (\n            <div className=\"mb-4\">\n              <h5 className=\"text-xs font-medium text-green-600 dark:text-green-400 mb-2\">📊 HISTORICAL ANALYSIS</h5>\n              <ul className=\"space-y-1\">\n                {result.prediction.reasoning.historical_factors.map((factor: string, index: number) => (\n                  <li key={index} className=\"text-xs text-gray-600 dark:text-gray-400 flex items-start\">\n                    <span className=\"text-green-500 mr-2\">•</span>\n                    {factor}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n\n          {/* Fundamental Reasoning */}\n          {result.prediction.reasoning.fundamental_factors && (\n            <div className=\"mb-4\">\n              <h5 className=\"text-xs font-medium text-purple-600 dark:text-purple-400 mb-2\">💰 FUNDAMENTAL ANALYSIS</h5>\n              <ul className=\"space-y-1\">\n                {result.prediction.reasoning.fundamental_factors.map((factor: string, index: number) => (\n                  <li key={index} className=\"text-xs text-gray-600 dark:text-gray-400 flex items-start\">\n                    <span className=\"text-purple-500 mr-2\">•</span>\n                    {factor}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n\n          {/* Risk Factors */}\n          {result.prediction.reasoning.risk_factors && (\n            <div className=\"mb-4\">\n              <h5 className=\"text-xs font-medium text-red-600 dark:text-red-400 mb-2\">⚠️ RISK FACTORS</h5>\n              <ul className=\"space-y-1\">\n                {result.prediction.reasoning.risk_factors.map((factor: string, index: number) => (\n                  <li key={index} className=\"text-xs text-gray-600 dark:text-gray-400 flex items-start\">\n                    <span className=\"text-red-500 mr-2\">•</span>\n                    {factor}\n                  </li>\n                ))}\n              </ul>\n            </div>\n          )}\n\n          {/* Target Rationale */}\n          {result.prediction.reasoning.target_rationale && (\n            <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-3\">\n              <h5 className=\"text-xs font-medium text-gray-700 dark:text-gray-300 mb-1\">🎯 TARGET RATIONALE</h5>\n              <p className=\"text-xs text-gray-600 dark:text-gray-400\">\n                {result.prediction.reasoning.target_rationale}\n              </p>\n            </div>\n          )}\n        </div>\n      )}\n\n      {/* Market Context */}\n      {result.market_context && (\n        <div className=\"border border-gray-200 dark:border-gray-600 rounded-lg p-4\">\n          <h4 className=\"text-sm font-medium text-gray-900 dark:text-white mb-3\">📊 Market Context</h4>\n          <div className=\"grid grid-cols-2 gap-3 text-xs\">\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">Market Phase:</span>\n              <p className=\"font-medium text-gray-900 dark:text-white capitalize\">\n                {result.market_context.market_phase}\n              </p>\n            </div>\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">Trend Strength:</span>\n              <p className=\"font-medium text-gray-900 dark:text-white\">\n                {(result.market_context.trend_strength * 100).toFixed(0)}%\n              </p>\n            </div>\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">24h Change:</span>\n              <p className={`font-medium ${result.market_context.price_change_24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                {result.market_context.price_change_24h >= 0 ? '+' : ''}{result.market_context.price_change_24h.toFixed(2)}%\n              </p>\n            </div>\n            <div>\n              <span className=\"text-gray-500 dark:text-gray-400\">7d Change:</span>\n              <p className={`font-medium ${result.market_context.price_change_7d >= 0 ? 'text-green-600' : 'text-red-600'}`}>\n                {result.market_context.price_change_7d >= 0 ? '+' : ''}{result.market_context.price_change_7d.toFixed(2)}%\n              </p>\n            </div>\n          </div>\n        </div>\n      )}\n\n      {/* Analysis Metadata */}\n      <div className=\"text-xs text-gray-500 dark:text-gray-400 pt-4 border-t border-gray-200 dark:border-gray-600\">\n        <p>Analysis ID: {result.analysis_id}</p>\n        <p>Timestamp: {new Date(result.timestamp).toLocaleString()}</p>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAQe,SAAS,gBAAgB,EAAE,MAAM,EAAwB;;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,IAAI,CAAC,QAAQ;QACX,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;wBAAU,MAAK;wBAAO,QAAO;wBAAe,SAAQ;kCACjE,cAAA,6LAAC;4BAAK,eAAc;4BAAQ,gBAAe;4BAAQ,aAAa;4BAAG,GAAE;;;;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAE,WAAU;8BAAU;;;;;;;;;;;;IAG7B;IAEA,MAAM,cAAc,CAAC;QACnB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;YACV,uBAAuB;YACvB,uBAAuB;QACzB,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,yBAAyB,CAAC;QAC9B,OAAQ,gBAAgB;YACtB,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAO,gBAAgB,QAAQ,KAAK,KAAK,iBAAiB;IAC5D;IAEA,MAAM,gBAAgB,CAAC;QACrB,IAAI,SAAS,KAAK,OAAO;QACzB,IAAI,SAAS,KAAK,OAAO;QACzB,OAAO;IACT;IAEA,MAAM,eAAe,OAAO,cAAc,EAAE,iBAAiB;IAC7D,MAAM,cAAc,OAAO,WAAW,EAAE,gBAAgB;IACxD,MAAM,cAAc,cAAc,IAAI,AAAC,CAAC,cAAc,YAAY,IAAI,eAAgB,MAAM;IAE5F,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAmD;;;;;;kDAGjE,6LAAC;wCAAE,WAAU;kDACV,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;;;;;;;;;;;;0CAG9C,6LAAC;gCAAI,WAAW,CAAC,2CAA2C,EAAE,uBAAuB,OAAO,WAAW,EAAE,iBAAiB;0CACvH,qBAAqB,OAAO,WAAW,EAAE;;;;;;;;;;;;kCAK9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CACZ,CAAC,CAAC,OAAO,WAAW,EAAE,cAAc,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;kDAE5D,6LAAC;wCAAI,WAAU;kDAA2C;;;;;;kDAC1D,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAW,CAAC,iBAAiB,EAAE,cAAc,OAAO,WAAW,EAAE,cAAc,IAAI;4CACnF,OAAO;gDAAE,OAAO,GAAG,CAAC,OAAO,WAAW,EAAE,cAAc,CAAC,IAAI,IAAI,CAAC,CAAC;4CAAC;;;;;;;;;;;;;;;;;0CAKxE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;4CAAmD;4CAC9D,YAAY,cAAc;;;;;;;kDAE9B,6LAAC;wCAAI,WAAU;kDAA2C;;;;;;kDAC1D,6LAAC;wCAAI,WAAW,CAAC,aAAa,EAAE,eAAe,IAAI,mBAAmB,gBAAgB;;4CACnF,cAAc,IAAI,MAAM;4CAAI,YAAY,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAIxD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,OAAO,WAAW,EAAE,gBAAgB;;;;;;kDAEvC,6LAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAG5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,OAAO,WAAW,EAAE,iBAAiB,cAAc;;;;;;kDAEtD,6LAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;0CAG5D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACZ,OAAO,WAAW,EAAE,iBAAiB,qBAAqB;;;;;;kDAE7D,6LAAC;wCAAI,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;0BAMhE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACZ;gCACC;oCAAE,IAAI;oCAAY,OAAO;oCAAY,MAAM;gCAAK;gCAChD;oCAAE,IAAI;oCAAa,OAAO;oCAAa,MAAM;gCAAK;gCAClD;oCAAE,IAAI;oCAAa,OAAO;oCAAgB,MAAM;gCAAK;gCACrD;oCAAE,IAAI;oCAAU,OAAO;oCAAkB,MAAM;gCAAK;6BACrD,CAAC,GAAG,CAAC,CAAC,oBACL,6LAAC;oCAEC,SAAS,IAAM,aAAa,IAAI,EAAE;oCAClC,WAAW,CAAC,qEAAqE,EAC/E,cAAc,IAAI,EAAE,GAChB,qDACA,oGACJ;;sDAEF,6LAAC;sDAAM,IAAI,IAAI;;;;;;sDACf,6LAAC;sDAAM,IAAI,KAAK;;;;;;;mCATX,IAAI,EAAE;;;;;;;;;;;;;;;kCAenB,6LAAC;wBAAI,WAAU;;4BACZ,cAAc,4BACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAGzE,6LAAC;gDAAI,WAAU;0DACZ,OAAO,qBAAqB,IAAI,OAAO,OAAO,CAAC,OAAO,qBAAqB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;oDAC7F,IAAI,QAAQ,eAAe,OAAO;oDAClC,qBACE,6LAAC;wDAAc,WAAU;;0EACvB,6LAAC;gEAAK,WAAU;0EACb,IAAI,OAAO,CAAC,KAAK;;;;;;0EAEpB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFACb,cAAA,6LAAC;4EACC,WAAW,CAAC,iBAAiB,EAAE,cAAc,QAAkB;4EAC/D,OAAO;gFAAE,OAAO,GAAG,AAAC,QAAmB,IAAI,CAAC,CAAC;4EAAC;;;;;;;;;;;kFAGlD,6LAAC;wEAAK,WAAU;;4EACb,CAAC,AAAC,QAAmB,GAAG,EAAE,OAAO,CAAC;4EAAG;;;;;;;;;;;;;;uDAZlC;;;;;gDAiBd;;;;;;;;;;;;oCAKH,OAAO,qBAAqB,EAAE,6BAC7B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAK,WAAU;8DAAsD;;;;;;8DAGtE,6LAAC;oDAAK,WAAU;;wDACb,CAAC,OAAO,qBAAqB,CAAC,WAAW,GAAG,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;4BAQxE,cAAc,6BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAGnE,OAAO,oBAAoB,iBAC1B,6LAAC;wCAAI,WAAU;kDACZ,OAAO,OAAO,CAAC,OAAO,oBAAoB,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC5D,6LAAC;gDAAc,WAAU;;kEACvB,6LAAC;wDAAI,WAAU;kEACZ,IAAI,OAAO,CAAC,KAAK;;;;;;kEAEpB,6LAAC;wDAAI,WAAU;kEACZ,OAAO,UAAU,WAAW,KAAK,SAAS,CAAC,OAAO,MAAM,KAAK,OAAO;;;;;;;+CAL/D;;;;;;;;;6DAWd,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;4BAKrD,cAAc,6BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;oCAGxE,OAAO,UAAU,EAAE,YAClB,OAAO,OAAO,CAAC,OAAO,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,CAAC,UAAU,QAAQ;wCAClE,IAAI,aAAa,oBAAoB;4CACnC,qBACE,6LAAC;gDAAmB,WAAU;;kEAC5B,6LAAC;wDAAG,WAAU;kEAA8D;;;;;;kEAG5E,6LAAC;wDAAE,WAAU;kEAAoC;;;;;;;+CAJzC;;;;;wCAOd;wCACA,qBACE,6LAAC;4CAAmB,WAAU;;8DAC5B,6LAAC;oDAAG,WAAU;;wDACX,SAAS,QAAQ,CAAC,gBAAgB;wDAClC,SAAS,QAAQ,CAAC,iBAAiB;wDACnC,SAAS,QAAQ,CAAC,kBAAkB;wDACpC,SAAS,QAAQ,CAAC,WAAW;wDAC7B,SAAS,OAAO,CAAC,KAAK;;;;;;;8DAEzB,6LAAC;oDAAG,WAAU;8DACX,AAAC,QAAqB,GAAG,CAAC,CAAC,QAAQ,sBAClC,6LAAC;4DAAe,WAAU;;8EACxB,6LAAC;oEAAK,WAAU;8EAA0B;;;;;;8EAC1C,6LAAC;8EAAM;;;;;;;2DAFA;;;;;;;;;;;2CAVL;;;;;oCAkBd,mBAEA,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;4BAKrD,cAAc,0BACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAsD;;;;;;oCAGnE,OAAO,cAAc,iBACpB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA4D;;;;;;0EAG3E,6LAAC;gEAAI,WAAU;;oEAAkD;oEAC7D,OAAO,cAAc,CAAC,aAAa,EAAE;;;;;;;;;;;;;kEAG3C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA4D;;;;;;0EAG3E,6LAAC;gEAAI,WAAW,CAAC,kBAAkB,EACjC,OAAO,cAAc,CAAC,gBAAgB,IAAI,IACtC,uCACA,kCACJ;;oEACC,OAAO,cAAc,CAAC,gBAAgB,IAAI,IAAI,MAAM;oEAAI,OAAO,cAAc,CAAC,gBAAgB,EAAE,QAAQ;oEAAG;;;;;;;;;;;;;kEAGhH,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA4D;;;;;;0EAG3E,6LAAC;gEAAI,WAAU;0EACZ,OAAO,cAAc,CAAC,YAAY;;;;;;;;;;;;kEAGvC,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA4D;;;;;;0EAG3E,6LAAC;gEAAI,WAAU;;oEACZ,OAAO,cAAc,CAAC,cAAc,EAAE,QAAQ;oEAAG;;;;;;;;;;;;;;;;;;;4CAMvD,CAAC,OAAO,cAAc,CAAC,cAAc,EAAE,SAAS,KAAK,OAAO,cAAc,CAAC,iBAAiB,EAAE,SAAS,CAAC,mBACvG,6LAAC;gDAAI,WAAU;;oDACZ,OAAO,cAAc,CAAC,cAAc,EAAE,SAAS,mBAC9C,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAAgE;;;;;;0EAC9E,6LAAC;gEAAI,WAAU;0EACZ,OAAO,cAAc,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,OAAO,sBAChD,6LAAC;wEAAgB,WAAU;;4EAAiD;4EACxE,MAAM,cAAc;;uEADd;;;;;;;;;;;;;;;;oDAQjB,OAAO,cAAc,CAAC,iBAAiB,EAAE,SAAS,mBACjD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAG,WAAU;0EAA4D;;;;;;0EAC1E,6LAAC;gEAAI,WAAU;0EACZ,OAAO,cAAc,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,OAAO,sBACnD,6LAAC;wEAAgB,WAAU;;4EAA6C;4EACpE,MAAM,cAAc;;uEADd;;;;;;;;;;;;;;;;;;;;;;;;;;;6DAWxB,6LAAC;wCAAE,WAAU;kDAAmC;;;;;;;;;;;;;;;;;;;;;;;;YAQzD,OAAO,WAAW,EAAE,iCACnB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,WAAW,CAAC,eAAe,CAAC,UAAU,KAAK,SAAS,iBAC3D,OAAO,WAAW,CAAC,eAAe,CAAC,UAAU,KAAK,WAAW,oBAC7D,kBACA;kDACC,OAAO,WAAW,CAAC,eAAe,CAAC,UAAU,EAAE;;;;;;;;;;;;4BAGnD,OAAO,WAAW,CAAC,eAAe,CAAC,SAAS,kBAC3C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAU;kDACb,YAAY,OAAO,WAAW,CAAC,eAAe,CAAC,SAAS;;;;;;;;;;;;4BAI9D,OAAO,WAAW,CAAC,eAAe,CAAC,WAAW,kBAC7C,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAU;kDACb,YAAY,OAAO,WAAW,CAAC,eAAe,CAAC,WAAW;;;;;;;;;;;;4BAIhE,OAAO,WAAW,CAAC,eAAe,CAAC,iBAAiB,kBACnD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAU;;4CAAoD;4CAC/D,OAAO,WAAW,CAAC,eAAe,CAAC,iBAAiB;;;;;;;;;;;;;;;;;;;;;;;;;YASlE,OAAO,oBAAoB,kBAC1B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;4BACZ,OAAO,oBAAoB,CAAC,GAAG,kBAC9B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,oBAAoB,CAAC,GAAG,GAAG,KAAK,iBACvC,OAAO,oBAAoB,CAAC,GAAG,GAAG,KAAK,mBACvC,iCACA;kDACC,OAAO,oBAAoB,CAAC,GAAG,CAAC,OAAO,CAAC;;;;;;;;;;;;4BAK9C,OAAO,oBAAoB,CAAC,IAAI,kBAC/B,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAK,WAAU;0DACb,OAAO,oBAAoB,CAAC,IAAI,CAAC,IAAI,EAAE,QAAQ;;;;;;;;;;;;kDAGpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA2C;;;;;;0DAC3D,6LAAC;gDAAK,WAAU;0DACb,OAAO,oBAAoB,CAAC,IAAI,CAAC,MAAM,EAAE,QAAQ;;;;;;;;;;;;;;;;;;4BAMzD,OAAO,oBAAoB,CAAC,eAAe,kBAC1C,6LAAC;gCAAI,WAAU;0CACZ,OAAO,OAAO,CAAC,OAAO,oBAAoB,CAAC,eAAe,EAAE,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM,iBAC5E,6LAAC;wCAAc,WAAU;;0DACvB,6LAAC;gDAAK,WAAU;;oDAA4C,IAAI,WAAW;oDAAG;;;;;;;0DAC9E,6LAAC;gDAAK,WAAU;0DACb,YAAY;;;;;;;uCAHP;;;;;;;;;;;;;;;;;;;;;;YAcrB,OAAO,UAAU,EAAE,mCAClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;4BACZ,OAAO,UAAU,CAAC,iBAAiB,CAAC,eAAe,kBAClD,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAK,WAAW,CAAC,oBAAoB,EACpC,OAAO,UAAU,CAAC,iBAAiB,CAAC,eAAe,KAAK,YAAY,mBACpE,OAAO,UAAU,CAAC,iBAAiB,CAAC,eAAe,KAAK,YAAY,iBACpE,iCACA;kDACC,OAAO,UAAU,CAAC,iBAAiB,CAAC,eAAe,EAAE;;;;;;;;;;;;4BAK3D,OAAO,UAAU,CAAC,iBAAiB,CAAC,oBAAoB,EAAE,SAAS,mBAClE,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;kDACZ,OAAO,UAAU,CAAC,iBAAiB,CAAC,oBAAoB,CAAC,GAAG,CAAC,CAAC,SAAiB,sBAC9E,6LAAC;gDAAiB,WAAU;0DACzB;+CADQ;;;;;;;;;;;;;;;;4BAQlB,OAAO,UAAU,CAAC,iBAAiB,CAAC,cAAc,EAAE,SAAS,mBAC5D,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;kDACZ,OAAO,UAAU,CAAC,iBAAiB,CAAC,cAAc,CAAC,GAAG,CAAC,CAAC,SAAiB,sBACxE,6LAAC;gDAAiB,WAAU;0DACzB;+CADQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAYxB,OAAO,qBAAqB,kBAC3B,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,eAAe,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGpF,6LAAC;gDAAK,WAAU;;oDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,eAAe,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK9E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,gBAAgB,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGrF,6LAAC;gDAAK,WAAU;;oDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,gBAAgB,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK/E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,iBAAiB,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGtF,6LAAC;gDAAK,WAAU;;oDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,iBAAiB,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAKhF,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAK,WAAU;kDAA2C;;;;;;kDAC3D,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDACC,WAAU;oDACV,OAAO;wDAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,eAAe,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;oDAAC;;;;;;;;;;;0DAGpF,6LAAC;gDAAK,WAAU;;oDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,eAAe,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;oDAAG;;;;;;;;;;;;;;;;;;;0CAK9E,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAK,WAAU;sDAAuD;;;;;;sDACvE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,CAAC,OAAO,qBAAqB,CAAC,WAAW,IAAI,CAAC,IAAI,IAAI,CAAC,CAAC;wDAAC;;;;;;;;;;;8DAGhF,6LAAC;oDAAK,WAAU;;wDACb,CAAC,CAAC,OAAO,qBAAqB,CAAC,WAAW,IAAI,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAUjF,OAAO,UAAU,EAAE,2BAClB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;oBAKtE,OAAO,UAAU,CAAC,SAAS,CAAC,iBAAiB,kBAC5C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAG,WAAU;0CACX,OAAO,UAAU,CAAC,SAAS,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC,QAAgB,sBAClE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;0DAAqB;;;;;;4CACpC;;uCAFM;;;;;;;;;;;;;;;;oBAUhB,OAAO,UAAU,CAAC,SAAS,CAAC,kBAAkB,kBAC7C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA8D;;;;;;0CAC5E,6LAAC;gCAAG,WAAU;0CACX,OAAO,UAAU,CAAC,SAAS,CAAC,kBAAkB,CAAC,GAAG,CAAC,CAAC,QAAgB,sBACnE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;0DAAsB;;;;;;4CACrC;;uCAFM;;;;;;;;;;;;;;;;oBAUhB,OAAO,UAAU,CAAC,SAAS,CAAC,mBAAmB,kBAC9C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAgE;;;;;;0CAC9E,6LAAC;gCAAG,WAAU;0CACX,OAAO,UAAU,CAAC,SAAS,CAAC,mBAAmB,CAAC,GAAG,CAAC,CAAC,QAAgB,sBACpE,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;0DAAuB;;;;;;4CACtC;;uCAFM;;;;;;;;;;;;;;;;oBAUhB,OAAO,UAAU,CAAC,SAAS,CAAC,YAAY,kBACvC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA0D;;;;;;0CACxE,6LAAC;gCAAG,WAAU;0CACX,OAAO,UAAU,CAAC,SAAS,CAAC,YAAY,CAAC,GAAG,CAAC,CAAC,QAAgB,sBAC7D,6LAAC;wCAAe,WAAU;;0DACxB,6LAAC;gDAAK,WAAU;0DAAoB;;;;;;4CACnC;;uCAFM;;;;;;;;;;;;;;;;oBAUhB,OAAO,UAAU,CAAC,SAAS,CAAC,gBAAgB,kBAC3C,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA4D;;;;;;0CAC1E,6LAAC;gCAAE,WAAU;0CACV,OAAO,UAAU,CAAC,SAAS,CAAC,gBAAgB;;;;;;;;;;;;;;;;;;YAQtD,OAAO,cAAc,kBACpB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAyD;;;;;;kCACvE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAE,WAAU;kDACV,OAAO,cAAc,CAAC,YAAY;;;;;;;;;;;;0CAGvC,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAE,WAAU;;4CACV,CAAC,OAAO,cAAc,CAAC,cAAc,GAAG,GAAG,EAAE,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAG7D,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAE,WAAW,CAAC,YAAY,EAAE,OAAO,cAAc,CAAC,gBAAgB,IAAI,IAAI,mBAAmB,gBAAgB;;4CAC3G,OAAO,cAAc,CAAC,gBAAgB,IAAI,IAAI,MAAM;4CAAI,OAAO,cAAc,CAAC,gBAAgB,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;0CAG/G,6LAAC;;kDACC,6LAAC;wCAAK,WAAU;kDAAmC;;;;;;kDACnD,6LAAC;wCAAE,WAAW,CAAC,YAAY,EAAE,OAAO,cAAc,CAAC,eAAe,IAAI,IAAI,mBAAmB,gBAAgB;;4CAC1G,OAAO,cAAc,CAAC,eAAe,IAAI,IAAI,MAAM;4CAAI,OAAO,cAAc,CAAC,eAAe,CAAC,OAAO,CAAC;4CAAG;;;;;;;;;;;;;;;;;;;;;;;;;0BAQnH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;;4BAAE;4BAAc,OAAO,WAAW;;;;;;;kCACnC,6LAAC;;4BAAE;4BAAY,IAAI,KAAK,OAAO,SAAS,EAAE,cAAc;;;;;;;;;;;;;;;;;;;AAIhE;GAnrBwB;KAAA", "debugId": null}}, {"offset": {"line": 2432, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/components/AIChat.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef, useEffect } from 'react';\n\ninterface Message {\n  id: string;\n  type: 'user' | 'ai';\n  content: string;\n  timestamp: Date;\n}\n\ninterface AIChatProps {\n  analysisResult: any;\n  onClose: () => void;\n}\n\nexport default function AIChat({ analysisResult, onClose }: AIChatProps) {\n  const [messages, setMessages] = useState<Message[]>([]);\n  const [inputMessage, setInputMessage] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  const scrollToBottom = () => {\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n  };\n\n  useEffect(() => {\n    scrollToBottom();\n  }, [messages]);\n\n  useEffect(() => {\n    // Add welcome message when chat opens\n    const welcomeMessage: Message = {\n      id: Date.now().toString(),\n      type: 'ai',\n      content: `Hi! I'm your AI assistant. I have analyzed your chart and found a **${analysisResult.predictions?.recommendation?.toUpperCase()}** recommendation with **${((analysisResult.predictions?.confidence || 0) * 100).toFixed(0)}%** confidence. \n\nAsk me anything about:\n• Technical analysis details\n• Risk assessment\n• Market context\n• Historical patterns\n• Price targets and rationale\n\nWhat would you like to know?`,\n      timestamp: new Date()\n    };\n    setMessages([welcomeMessage]);\n  }, [analysisResult]);\n\n  const sendMessage = async () => {\n    if (!inputMessage.trim() || isLoading) return;\n\n    const userMessage: Message = {\n      id: Date.now().toString(),\n      type: 'user',\n      content: inputMessage,\n      timestamp: new Date()\n    };\n\n    setMessages(prev => [...prev, userMessage]);\n    setInputMessage('');\n    setIsLoading(true);\n\n    try {\n      const response = await fetch('http://localhost:8000/api/v1/chat', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        body: JSON.stringify({\n          message: inputMessage,\n          analysis_context: analysisResult\n        }),\n      });\n\n      if (!response.ok) {\n        throw new Error('Failed to get AI response');\n      }\n\n      const data = await response.json();\n\n      const aiMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        type: 'ai',\n        content: data.response,\n        timestamp: new Date()\n      };\n\n      setMessages(prev => [...prev, aiMessage]);\n    } catch (error) {\n      const errorMessage: Message = {\n        id: (Date.now() + 1).toString(),\n        type: 'ai',\n        content: 'Sorry, I encountered an error. Please try again.',\n        timestamp: new Date()\n      };\n      setMessages(prev => [...prev, errorMessage]);\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const handleKeyPress = (e: React.KeyboardEvent) => {\n    if (e.key === 'Enter' && !e.shiftKey) {\n      e.preventDefault();\n      sendMessage();\n    }\n  };\n\n  const suggestedQuestions = [\n    \"Why did you recommend this action?\",\n    \"What are the main risk factors?\",\n    \"Explain the technical indicators\",\n    \"What's the target price rationale?\",\n    \"How confident are you in this analysis?\"\n  ];\n\n  return (\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n      <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-4xl h-[80vh] flex flex-col\">\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center\">\n              <svg className=\"w-4 h-4 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\" />\n              </svg>\n            </div>\n            <div>\n              <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white\">AI Analysis Chat</h3>\n              <p className=\"text-sm text-gray-500 dark:text-gray-400\">Ask questions about your analysis</p>\n            </div>\n          </div>\n          <button\n            onClick={onClose}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n          >\n            <svg className=\"w-6 h-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Messages */}\n        <div className=\"flex-1 overflow-y-auto p-4 space-y-4\">\n          {messages.map((message) => (\n            <div\n              key={message.id}\n              className={`flex ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}\n            >\n              <div\n                className={`max-w-[70%] rounded-lg p-3 ${\n                  message.type === 'user'\n                    ? 'bg-blue-600 text-white'\n                    : 'bg-gray-100 dark:bg-gray-700 text-gray-900 dark:text-white'\n                }`}\n              >\n                <div className=\"text-sm whitespace-pre-wrap\">{message.content}</div>\n                <div className={`text-xs mt-1 ${\n                  message.type === 'user' ? 'text-blue-100' : 'text-gray-500 dark:text-gray-400'\n                }`}>\n                  {message.timestamp.toLocaleTimeString()}\n                </div>\n              </div>\n            </div>\n          ))}\n          \n          {isLoading && (\n            <div className=\"flex justify-start\">\n              <div className=\"bg-gray-100 dark:bg-gray-700 rounded-lg p-3\">\n                <div className=\"flex space-x-1\">\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\"></div>\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.1s' }}></div>\n                  <div className=\"w-2 h-2 bg-gray-400 rounded-full animate-bounce\" style={{ animationDelay: '0.2s' }}></div>\n                </div>\n              </div>\n            </div>\n          )}\n          \n          <div ref={messagesEndRef} />\n        </div>\n\n        {/* Suggested Questions */}\n        {messages.length === 1 && (\n          <div className=\"px-4 py-2 border-t border-gray-200 dark:border-gray-700\">\n            <p className=\"text-sm text-gray-500 dark:text-gray-400 mb-2\">Suggested questions:</p>\n            <div className=\"flex flex-wrap gap-2\">\n              {suggestedQuestions.map((question, index) => (\n                <button\n                  key={index}\n                  onClick={() => setInputMessage(question)}\n                  className=\"text-xs px-3 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors\"\n                >\n                  {question}\n                </button>\n              ))}\n            </div>\n          </div>\n        )}\n\n        {/* Input */}\n        <div className=\"p-4 border-t border-gray-200 dark:border-gray-700\">\n          <div className=\"flex space-x-2\">\n            <textarea\n              value={inputMessage}\n              onChange={(e) => setInputMessage(e.target.value)}\n              onKeyPress={handleKeyPress}\n              placeholder=\"Ask me anything about your analysis...\"\n              className=\"flex-1 resize-none border border-gray-300 dark:border-gray-600 rounded-lg px-3 py-2 text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500\"\n              rows={2}\n              disabled={isLoading}\n            />\n            <button\n              onClick={sendMessage}\n              disabled={!inputMessage.trim() || isLoading}\n              className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 19l9 2-9-18-9 18 9-2zm0 0v-8\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAgBe,SAAS,OAAO,EAAE,cAAc,EAAE,OAAO,EAAe;;IACrE,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,iBAAiB,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE9C,MAAM,iBAAiB;QACrB,eAAe,OAAO,EAAE,eAAe;YAAE,UAAU;QAAS;IAC9D;IAEA,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG;QAAC;KAAS;IAEb,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4BAAE;YACR,sCAAsC;YACtC,MAAM,iBAA0B;gBAC9B,IAAI,KAAK,GAAG,GAAG,QAAQ;gBACvB,MAAM;gBACN,SAAS,CAAC,oEAAoE,EAAE,eAAe,WAAW,EAAE,gBAAgB,cAAc,yBAAyB,EAAE,CAAC,CAAC,eAAe,WAAW,EAAE,cAAc,CAAC,IAAI,GAAG,EAAE,OAAO,CAAC,GAAG;;;;;;;;;4BAShN,CAAC;gBACvB,WAAW,IAAI;YACjB;YACA,YAAY;gBAAC;aAAe;QAC9B;2BAAG;QAAC;KAAe;IAEnB,MAAM,cAAc;QAClB,IAAI,CAAC,aAAa,IAAI,MAAM,WAAW;QAEvC,MAAM,cAAuB;YAC3B,IAAI,KAAK,GAAG,GAAG,QAAQ;YACvB,MAAM;YACN,SAAS;YACT,WAAW,IAAI;QACjB;QAEA,YAAY,CAAA,OAAQ;mBAAI;gBAAM;aAAY;QAC1C,gBAAgB;QAChB,aAAa;QAEb,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qCAAqC;gBAChE,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS;oBACT,kBAAkB;gBACpB;YACF;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,MAAM,YAAqB;gBACzB,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS,KAAK,QAAQ;gBACtB,WAAW,IAAI;YACjB;YAEA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAU;QAC1C,EAAE,OAAO,OAAO;YACd,MAAM,eAAwB;gBAC5B,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,EAAE,QAAQ;gBAC7B,MAAM;gBACN,SAAS;gBACT,WAAW,IAAI;YACjB;YACA,YAAY,CAAA,OAAQ;uBAAI;oBAAM;iBAAa;QAC7C,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,IAAI,EAAE,GAAG,KAAK,WAAW,CAAC,EAAE,QAAQ,EAAE;YACpC,EAAE,cAAc;YAChB;QACF;IACF;IAEA,MAAM,qBAAqB;QACzB;QACA;QACA;QACA;QACA;KACD;IAED,qBACE,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;YAAI,WAAU;;8BAEb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAI,WAAU;8CACb,cAAA,6LAAC;wCAAI,WAAU;wCAAqB,MAAK;wCAAO,QAAO;wCAAe,SAAQ;kDAC5E,cAAA,6LAAC;4CAAK,eAAc;4CAAQ,gBAAe;4CAAQ,aAAa;4CAAG,GAAE;;;;;;;;;;;;;;;;8CAGzE,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAsD;;;;;;sDACpE,6LAAC;4CAAE,WAAU;sDAA2C;;;;;;;;;;;;;;;;;;sCAG5D,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;gCAAU,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CACjE,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;8BAM3E,6LAAC;oBAAI,WAAU;;wBACZ,SAAS,GAAG,CAAC,CAAC,wBACb,6LAAC;gCAEC,WAAW,CAAC,KAAK,EAAE,QAAQ,IAAI,KAAK,SAAS,gBAAgB,iBAAiB;0CAE9E,cAAA,6LAAC;oCACC,WAAW,CAAC,2BAA2B,EACrC,QAAQ,IAAI,KAAK,SACb,2BACA,8DACJ;;sDAEF,6LAAC;4CAAI,WAAU;sDAA+B,QAAQ,OAAO;;;;;;sDAC7D,6LAAC;4CAAI,WAAW,CAAC,aAAa,EAC5B,QAAQ,IAAI,KAAK,SAAS,kBAAkB,oCAC5C;sDACC,QAAQ,SAAS,CAAC,kBAAkB;;;;;;;;;;;;+BAdpC,QAAQ,EAAE;;;;;wBAoBlB,2BACC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;sDACjG,6LAAC;4CAAI,WAAU;4CAAkD,OAAO;gDAAE,gBAAgB;4CAAO;;;;;;;;;;;;;;;;;;;;;;sCAMzG,6LAAC;4BAAI,KAAK;;;;;;;;;;;;gBAIX,SAAS,MAAM,KAAK,mBACnB,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAE,WAAU;sCAAgD;;;;;;sCAC7D,6LAAC;4BAAI,WAAU;sCACZ,mBAAmB,GAAG,CAAC,CAAC,UAAU,sBACjC,6LAAC;oCAEC,SAAS,IAAM,gBAAgB;oCAC/B,WAAU;8CAET;mCAJI;;;;;;;;;;;;;;;;8BAYf,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCACC,OAAO;gCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;gCAC/C,YAAY;gCACZ,aAAY;gCACZ,WAAU;gCACV,MAAM;gCACN,UAAU;;;;;;0CAEZ,6LAAC;gCACC,SAAS;gCACT,UAAU,CAAC,aAAa,IAAI,MAAM;gCAClC,WAAU;0CAEV,cAAA,6LAAC;oCAAI,WAAU;oCAAU,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjE,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQrF;GAnNwB;KAAA", "debugId": null}}, {"offset": {"line": 2867, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/components/ParameterForm.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\n\ninterface ParameterFormProps {\n  onParametersChange?: (params: any) => void;\n}\n\nexport default function ParameterForm({ onParametersChange }: ParameterFormProps) {\n  const [parameters, setParameters] = useState({\n    coinSymbol: 'BTC',\n    timeframe: '1h',\n    chartType: 'candlestick',\n    additionalNotes: ''\n  });\n\n  const [supportedCoins, setSupportedCoins] = useState<string[]>([]);\n  const [timeframes, setTimeframes] = useState<any[]>([]);\n\n  useEffect(() => {\n    // Fetch supported coins and timeframes from API\n    fetchSupportedOptions();\n  }, []);\n\n  useEffect(() => {\n    // Notify parent component of parameter changes\n    if (onParametersChange) {\n      onParametersChange(parameters);\n    }\n  }, [parameters, onParametersChange]);\n\n  const fetchSupportedOptions = async () => {\n    try {\n      // Fetch supported coins\n      const coinsResponse = await fetch('http://localhost:8000/api/v1/supported-coins');\n      if (coinsResponse.ok) {\n        const coinsData = await coinsResponse.json();\n        setSupportedCoins(coinsData.supported_coins || []);\n      }\n\n      // Fetch timeframes\n      const timeframesResponse = await fetch('http://localhost:8000/api/v1/timeframes');\n      if (timeframesResponse.ok) {\n        const timeframesData = await timeframesResponse.json();\n        setTimeframes(timeframesData.timeframes || []);\n      }\n    } catch (error) {\n      console.error('Error fetching options:', error);\n      // Set default values if API is not available\n      setSupportedCoins(['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK']);\n      setTimeframes([\n        { value: '1m', label: '1 Minute' },\n        { value: '5m', label: '5 Minutes' },\n        { value: '15m', label: '15 Minutes' },\n        { value: '1h', label: '1 Hour' },\n        { value: '4h', label: '4 Hours' },\n        { value: '1d', label: '1 Day' },\n        { value: '1w', label: '1 Week' }\n      ]);\n    }\n  };\n\n  const handleInputChange = (field: string, value: string) => {\n    setParameters(prev => ({\n      ...prev,\n      [field]: value\n    }));\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Coin Symbol */}\n      <div>\n        <label htmlFor=\"coinSymbol\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Cryptocurrency Symbol\n        </label>\n        <select\n          id=\"coinSymbol\"\n          value={parameters.coinSymbol}\n          onChange={(e) => handleInputChange('coinSymbol', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n        >\n          {supportedCoins.map(coin => (\n            <option key={coin} value={coin}>{coin}</option>\n          ))}\n        </select>\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          Select the cryptocurrency shown in your chart\n        </p>\n      </div>\n\n      {/* Timeframe */}\n      <div>\n        <label htmlFor=\"timeframe\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Chart Timeframe\n        </label>\n        <select\n          id=\"timeframe\"\n          value={parameters.timeframe}\n          onChange={(e) => handleInputChange('timeframe', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n        >\n          {timeframes.map(tf => (\n            <option key={tf.value} value={tf.value}>{tf.label}</option>\n          ))}\n        </select>\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          The time interval of each candle/data point\n        </p>\n      </div>\n\n      {/* Chart Type */}\n      <div>\n        <label htmlFor=\"chartType\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Chart Type\n        </label>\n        <select\n          id=\"chartType\"\n          value={parameters.chartType}\n          onChange={(e) => handleInputChange('chartType', e.target.value)}\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white\"\n        >\n          <option value=\"candlestick\">Candlestick</option>\n          <option value=\"line\">Line Chart</option>\n          <option value=\"area\">Area Chart</option>\n          <option value=\"ohlc\">OHLC Bars</option>\n        </select>\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          The type of chart visualization\n        </p>\n      </div>\n\n      {/* Additional Notes */}\n      <div>\n        <label htmlFor=\"additionalNotes\" className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n          Additional Notes (Optional)\n        </label>\n        <textarea\n          id=\"additionalNotes\"\n          value={parameters.additionalNotes}\n          onChange={(e) => handleInputChange('additionalNotes', e.target.value)}\n          rows={3}\n          placeholder=\"Any specific indicators, patterns, or context you'd like the AI to consider...\"\n          className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500\"\n        />\n        <p className=\"text-xs text-gray-500 dark:text-gray-400 mt-1\">\n          Provide context about indicators, market conditions, or specific analysis requests\n        </p>\n      </div>\n\n      {/* Analysis Options */}\n      <div className=\"border-t border-gray-200 dark:border-gray-600 pt-4\">\n        <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-3\">Analysis Focus</h4>\n        <div className=\"grid grid-cols-2 gap-3\">\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              defaultChecked\n              className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n            />\n            <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">Technical Indicators</span>\n          </label>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              defaultChecked\n              className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n            />\n            <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">Pattern Recognition</span>\n          </label>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              defaultChecked\n              className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n            />\n            <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">Volume Analysis</span>\n          </label>\n          <label className=\"flex items-center\">\n            <input\n              type=\"checkbox\"\n              defaultChecked\n              className=\"rounded border-gray-300 text-blue-600 focus:ring-blue-500\"\n            />\n            <span className=\"ml-2 text-sm text-gray-600 dark:text-gray-400\">Risk Assessment</span>\n          </label>\n        </div>\n      </div>\n\n      {/* Current Parameters Summary */}\n      <div className=\"bg-gray-50 dark:bg-gray-700 rounded-lg p-4\">\n        <h4 className=\"text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">Current Settings</h4>\n        <div className=\"text-xs text-gray-600 dark:text-gray-400 space-y-1\">\n          <p><span className=\"font-medium\">Coin:</span> {parameters.coinSymbol}</p>\n          <p><span className=\"font-medium\">Timeframe:</span> {parameters.timeframe}</p>\n          <p><span className=\"font-medium\">Chart Type:</span> {parameters.chartType}</p>\n          {parameters.additionalNotes && (\n            <p><span className=\"font-medium\">Notes:</span> {parameters.additionalNotes.substring(0, 50)}{parameters.additionalNotes.length > 50 ? '...' : ''}</p>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;;;AAFA;;AAQe,SAAS,cAAc,EAAE,kBAAkB,EAAsB;;IAC9E,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAC3C,YAAY;QACZ,WAAW;QACX,WAAW;QACX,iBAAiB;IACnB;IAEA,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,gDAAgD;YAChD;QACF;kCAAG,EAAE;IAEL,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,+CAA+C;YAC/C,IAAI,oBAAoB;gBACtB,mBAAmB;YACrB;QACF;kCAAG;QAAC;QAAY;KAAmB;IAEnC,MAAM,wBAAwB;QAC5B,IAAI;YACF,wBAAwB;YACxB,MAAM,gBAAgB,MAAM,MAAM;YAClC,IAAI,cAAc,EAAE,EAAE;gBACpB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,kBAAkB,UAAU,eAAe,IAAI,EAAE;YACnD;YAEA,mBAAmB;YACnB,MAAM,qBAAqB,MAAM,MAAM;YACvC,IAAI,mBAAmB,EAAE,EAAE;gBACzB,MAAM,iBAAiB,MAAM,mBAAmB,IAAI;gBACpD,cAAc,eAAe,UAAU,IAAI,EAAE;YAC/C;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;YACzC,6CAA6C;YAC7C,kBAAkB;gBAAC;gBAAO;gBAAO;gBAAO;gBAAO;gBAAO;aAAO;YAC7D,cAAc;gBACZ;oBAAE,OAAO;oBAAM,OAAO;gBAAW;gBACjC;oBAAE,OAAO;oBAAM,OAAO;gBAAY;gBAClC;oBAAE,OAAO;oBAAO,OAAO;gBAAa;gBACpC;oBAAE,OAAO;oBAAM,OAAO;gBAAS;gBAC/B;oBAAE,OAAO;oBAAM,OAAO;gBAAU;gBAChC;oBAAE,OAAO;oBAAM,OAAO;gBAAQ;gBAC9B;oBAAE,OAAO;oBAAM,OAAO;gBAAS;aAChC;QACH;IACF;IAEA,MAAM,oBAAoB,CAAC,OAAe;QACxC,cAAc,CAAA,OAAQ,CAAC;gBACrB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;IACH;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAa,WAAU;kCAAkE;;;;;;kCAGxG,6LAAC;wBACC,IAAG;wBACH,OAAO,WAAW,UAAU;wBAC5B,UAAU,CAAC,IAAM,kBAAkB,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC/D,WAAU;kCAET,eAAe,GAAG,CAAC,CAAA,qBAClB,6LAAC;gCAAkB,OAAO;0CAAO;+BAApB;;;;;;;;;;kCAGjB,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAY,WAAU;kCAAkE;;;;;;kCAGvG,6LAAC;wBACC,IAAG;wBACH,OAAO,WAAW,SAAS;wBAC3B,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC9D,WAAU;kCAET,WAAW,GAAG,CAAC,CAAA,mBACd,6LAAC;gCAAsB,OAAO,GAAG,KAAK;0CAAG,GAAG,KAAK;+BAApC,GAAG,KAAK;;;;;;;;;;kCAGzB,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAY,WAAU;kCAAkE;;;;;;kCAGvG,6LAAC;wBACC,IAAG;wBACH,OAAO,WAAW,SAAS;wBAC3B,UAAU,CAAC,IAAM,kBAAkB,aAAa,EAAE,MAAM,CAAC,KAAK;wBAC9D,WAAU;;0CAEV,6LAAC;gCAAO,OAAM;0CAAc;;;;;;0CAC5B,6LAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6LAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6LAAC;gCAAO,OAAM;0CAAO;;;;;;;;;;;;kCAEvB,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,6LAAC;;kCACC,6LAAC;wBAAM,SAAQ;wBAAkB,WAAU;kCAAkE;;;;;;kCAG7G,6LAAC;wBACC,IAAG;wBACH,OAAO,WAAW,eAAe;wBACjC,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;wBACpE,MAAM;wBACN,aAAY;wBACZ,WAAU;;;;;;kCAEZ,6LAAC;wBAAE,WAAU;kCAAgD;;;;;;;;;;;;0BAM/D,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,cAAc;wCACd,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;0CAElE,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,cAAc;wCACd,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;0CAElE,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,cAAc;wCACd,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;0CAElE,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;wCACC,MAAK;wCACL,cAAc;wCACd,WAAU;;;;;;kDAEZ,6LAAC;wCAAK,WAAU;kDAAgD;;;;;;;;;;;;;;;;;;;;;;;;0BAMtE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA4D;;;;;;kCAC1E,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDAAE,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAY;oCAAE,WAAW,UAAU;;;;;;;0CACpE,6LAAC;;kDAAE,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAiB;oCAAE,WAAW,SAAS;;;;;;;0CACxE,6LAAC;;kDAAE,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAkB;oCAAE,WAAW,SAAS;;;;;;;4BACxE,WAAW,eAAe,kBACzB,6LAAC;;kDAAE,6LAAC;wCAAK,WAAU;kDAAc;;;;;;oCAAa;oCAAE,WAAW,eAAe,CAAC,SAAS,CAAC,GAAG;oCAAK,WAAW,eAAe,CAAC,MAAM,GAAG,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;;;;;;;AAM1J;GAnMwB;KAAA", "debugId": null}}, {"offset": {"line": 3410, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/api/test-backend.ts"], "sourcesContent": ["// Simple script to test frontend-backend connectivity\n\nexport async function testBackendConnection() {\n  try {\n    // Use health endpoint instead of analyze-chart for testing\n    const response = await fetch('http://localhost:8000/health');\n    if (response.ok) {\n      console.log('✅ Frontend can reach backend API!');\n      return true;\n    } else {\n      console.error('❌ Backend API responded with error:', response.status, response.statusText);\n      return false;\n    }\n  } catch (error) {\n    console.error('❌ Could not connect to backend API:', error);\n    return false;\n  }\n}"], "names": [], "mappings": "AAAA,sDAAsD;;;;AAE/C,eAAe;IACpB,IAAI;QACF,2DAA2D;QAC3D,MAAM,WAAW,MAAM,MAAM;QAC7B,IAAI,SAAS,EAAE,EAAE;YACf,QAAQ,GAAG,CAAC;YACZ,OAAO;QACT,OAAO;YACL,QAAQ,KAAK,CAAC,uCAAuC,SAAS,MAAM,EAAE,SAAS,UAAU;YACzF,OAAO;QACT;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO;IACT;AACF", "debugId": null}}, {"offset": {"line": 3439, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Chart%20Analysis/frontend/src/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport ImageUpload from '@/components/ImageUpload';\nimport AnalysisResults from '@/components/AnalysisResults';\nimport AIChat from '@/components/AIChat';\nimport ParameterForm from '@/components/ParameterForm';\nimport { testBackendConnection } from '@/api/test-backend';\n\nexport default function Home() {\n  const [analysisResult, setAnalysisResult] = useState(null);\n  const [isAnalyzing, setIsAnalyzing] = useState(false);\n  const [backendStatus, setBackendStatus] = useState<'checking' | 'connected' | 'error'>('checking');\n  const [showChat, setShowChat] = useState(false);\n\n  useEffect(() => {\n    testBackendConnection().then((ok) => {\n      setBackendStatus(ok ? 'connected' : 'error');\n    });\n  }, []);\n\n  const handleAnalysisComplete = (result: any) => {\n    setAnalysisResult(result);\n    setIsAnalyzing(false);\n  };\n\n  const handleAnalysisStart = () => {\n    setIsAnalyzing(true);\n    setAnalysisResult(null);\n  };\n\n  const handleStartChat = (result: any) => {\n    setShowChat(true);\n  };\n\n  const handleCloseChat = () => {\n    setShowChat(false);\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800\">\n      {/* Backend Status Banner */}\n      <div className=\"w-full text-center py-2\">\n        {backendStatus === 'checking' && (\n          <span className=\"text-gray-500\">Checking backend connection...</span>\n        )}\n        {backendStatus === 'connected' && (\n          <span className=\"text-green-600 font-semibold\">Backend Connected</span>\n        )}\n        {backendStatus === 'error' && (\n          <span className=\"text-red-600 font-semibold\">Backend Connection Error</span>\n        )}\n      </div>\n\n      {/* Header */}\n      <header className=\"bg-white dark:bg-gray-800 shadow-sm border-b\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\">\n          <div className=\"flex items-center justify-between\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center\">\n                <span className=\"text-white font-bold text-sm\">AI</span>\n              </div>\n              <div>\n                <h1 className=\"text-2xl font-bold text-gray-900 dark:text-white\">\n                  Crypto Chart Analysis\n                </h1>\n                <p className=\"text-sm text-gray-600 dark:text-gray-300\">\n                  AI-powered cryptocurrency chart analysis with position recommendations\n                </p>\n              </div>\n            </div>\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200\">\n                ● Live\n              </span>\n            </div>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n          {/* Upload Section */}\n          <div className=\"lg:col-span-2 space-y-6\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n                Upload Chart Image\n              </h2>\n              <ImageUpload\n                onAnalysisStart={handleAnalysisStart}\n                onAnalysisComplete={handleAnalysisComplete}\n                isAnalyzing={isAnalyzing}\n              />\n            </div>\n\n            {/* Parameters Section */}\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n                Analysis Parameters\n              </h2>\n              <ParameterForm />\n            </div>\n          </div>\n\n          {/* Results Section */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sticky top-8\">\n              <h2 className=\"text-xl font-semibold text-gray-900 dark:text-white mb-4\">\n                Analysis Results\n              </h2>\n              {isAnalyzing ? (\n                <div className=\"flex items-center justify-center py-12\">\n                  <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500\"></div>\n                </div>\n              ) : analysisResult ? (\n                <AnalysisResults result={analysisResult} onStartChat={handleStartChat} />\n              ) : (\n                <div className=\"text-center py-12 text-gray-500 dark:text-gray-400\">\n                  <div className=\"w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center\">\n                    <svg className=\"w-8 h-8\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n                    </svg>\n                  </div>\n                  <p className=\"text-sm\">Upload a chart to see AI analysis results</p>\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n\n        {/* Features Section */}\n        <div className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"w-12 h-12 mx-auto mb-4 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-blue-600 dark:text-blue-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 10V3L4 14h7v7l9-11h-7z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">AI-Powered Analysis</h3>\n            <p className=\"text-gray-600 dark:text-gray-300 text-sm\">Advanced computer vision and machine learning for accurate chart interpretation</p>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"w-12 h-12 mx-auto mb-4 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-green-600 dark:text-green-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">Technical Indicators</h3>\n            <p className=\"text-gray-600 dark:text-gray-300 text-sm\">RSI, MACD, Moving Averages, Bollinger Bands, and more technical analysis tools</p>\n          </div>\n\n          <div className=\"bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 text-center\">\n            <div className=\"w-12 h-12 mx-auto mb-4 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center\">\n              <svg className=\"w-6 h-6 text-purple-600 dark:text-purple-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1\" />\n              </svg>\n            </div>\n            <h3 className=\"text-lg font-semibold text-gray-900 dark:text-white mb-2\">Position Recommendations</h3>\n            <p className=\"text-gray-600 dark:text-gray-300 text-sm\">Clear buy/sell/hold signals with confidence levels and risk assessment</p>\n          </div>\n        </div>\n      </main>\n\n      {/* AI Chat Modal */}\n      {showChat && analysisResult && (\n        <AIChat\n          analysisResult={analysisResult}\n          onClose={handleCloseChat}\n        />\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;;;AAPA;;;;;;;AASe,SAAS;;IACtB,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAsC;IACvF,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0BAAE;YACR,CAAA,GAAA,gIAAA,CAAA,wBAAqB,AAAD,IAAI,IAAI;kCAAC,CAAC;oBAC5B,iBAAiB,KAAK,cAAc;gBACtC;;QACF;yBAAG,EAAE;IAEL,MAAM,yBAAyB,CAAC;QAC9B,kBAAkB;QAClB,eAAe;IACjB;IAEA,MAAM,sBAAsB;QAC1B,eAAe;QACf,kBAAkB;IACpB;IAEA,MAAM,kBAAkB,CAAC;QACvB,YAAY;IACd;IAEA,MAAM,kBAAkB;QACtB,YAAY;IACd;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;oBACZ,kBAAkB,4BACjB,6LAAC;wBAAK,WAAU;kCAAgB;;;;;;oBAEjC,kBAAkB,6BACjB,6LAAC;wBAAK,WAAU;kCAA+B;;;;;;oBAEhD,kBAAkB,yBACjB,6LAAC;wBAAK,WAAU;kCAA6B;;;;;;;;;;;;0BAKjD,6LAAC;gBAAO,WAAU;0BAChB,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;kDAEjD,6LAAC;;0DACC,6LAAC;gDAAG,WAAU;0DAAmD;;;;;;0DAGjE,6LAAC;gDAAE,WAAU;0DAA2C;;;;;;;;;;;;;;;;;;0CAK5D,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAK,WAAU;8CAA4I;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASpK,6LAAC;gBAAK,WAAU;;kCACd,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAGzE,6LAAC,oIAAA,CAAA,UAAW;gDACV,iBAAiB;gDACjB,oBAAoB;gDACpB,aAAa;;;;;;;;;;;;kDAKjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA2D;;;;;;0DAGzE,6LAAC,sIAAA,CAAA,UAAa;;;;;;;;;;;;;;;;;0CAKlB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAG,WAAU;sDAA2D;;;;;;wCAGxE,4BACC,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;;;;;;;;;mDAEf,+BACF,6LAAC,wIAAA,CAAA,UAAe;4CAAC,QAAQ;4CAAgB,aAAa;;;;;iEAEtD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;wDAAU,MAAK;wDAAO,QAAO;wDAAe,SAAQ;kEACjE,cAAA,6LAAC;4DAAK,eAAc;4DAAQ,gBAAe;4DAAQ,aAAa;4DAAG,GAAE;;;;;;;;;;;;;;;;8DAGzE,6LAAC;oDAAE,WAAU;8DAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAA2C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDAClG,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAG1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAA6C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACpG,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;0CAG1D,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;4CAA+C,MAAK;4CAAO,QAAO;4CAAe,SAAQ;sDACtG,cAAA,6LAAC;gDAAK,eAAc;gDAAQ,gBAAe;gDAAQ,aAAa;gDAAG,GAAE;;;;;;;;;;;;;;;;kDAGzE,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDACzE,6LAAC;wCAAE,WAAU;kDAA2C;;;;;;;;;;;;;;;;;;;;;;;;YAM7D,YAAY,gCACX,6LAAC,+HAAA,CAAA,UAAM;gBACL,gBAAgB;gBAChB,SAAS;;;;;;;;;;;;AAKnB;GArKwB;KAAA", "debugId": null}}]}