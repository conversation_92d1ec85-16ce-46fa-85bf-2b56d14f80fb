'use client';

import { useState } from 'react';

interface AnalysisResultsProps {
  result: any;
}

export default function AnalysisResults({ result }: AnalysisResultsProps) {
  const [activeTab, setActiveTab] = useState('overview');
  if (!result) {
    return (
      <div className="text-center py-12 text-gray-500 dark:text-gray-400">
        <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
          <svg className="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
          </svg>
        </div>
        <p className="text-sm">Upload a chart to see AI analysis results</p>
      </div>
    );
  }

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation?.toLowerCase()) {
      case 'strong_buy':
      case 'buy':
        return 'text-green-600 bg-green-100 dark:text-green-400 dark:bg-green-900';
      case 'strong_sell':
      case 'sell':
        return 'text-red-600 bg-red-100 dark:text-red-400 dark:bg-red-900';
      case 'hold':
        return 'text-yellow-600 bg-yellow-100 dark:text-yellow-400 dark:bg-yellow-900';
      default:
        return 'text-gray-600 bg-gray-100 dark:text-gray-400 dark:bg-gray-900';
    }
  };

  const formatRecommendation = (recommendation: string) => {
    return recommendation?.replace('_', ' ').toUpperCase() || 'UNKNOWN';
  };

  const getScoreColor = (score: number) => {
    if (score >= 0.8) return 'bg-green-500';
    if (score >= 0.6) return 'bg-yellow-500';
    return 'bg-red-500';
  };

  const currentPrice = result.market_context?.current_price || 0;
  const targetPrice = result.predictions?.target_price || 0;
  const priceChange = targetPrice > 0 ? ((targetPrice - currentPrice) / currentPrice) * 100 : 0;

  return (
    <div className="space-y-6">
      {/* Enhanced Header with Action Button */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-800 dark:to-gray-700 rounded-xl shadow-lg p-6">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 dark:text-white">
              Analysis Results
            </h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              {new Date(result.timestamp).toLocaleString()}
            </p>
          </div>
          <div className={`px-4 py-2 rounded-full text-sm font-medium ${getRecommendationColor(result.predictions?.recommendation)}`}>
            {formatRecommendation(result.predictions?.recommendation)}
          </div>
        </div>

        {/* Enhanced Key Metrics Grid */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {((result.predictions?.confidence || 0) * 100).toFixed(0)}%
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Confidence</div>
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2 mt-2">
              <div
                className={`h-2 rounded-full ${getScoreColor(result.predictions?.confidence || 0)}`}
                style={{ width: `${(result.predictions?.confidence || 0) * 100}%` }}
              ></div>
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              ${targetPrice.toLocaleString()}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Target Price</div>
            <div className={`text-xs mt-1 ${priceChange >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {priceChange > 0 ? '+' : ''}{priceChange.toFixed(1)}%
            </div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {result.predictions?.time_horizon || 'N/A'}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Time Horizon</div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white capitalize">
              {result.predictions?.risk_assessment?.risk_level || 'N/A'}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Risk Level</div>
          </div>

          <div className="bg-white dark:bg-gray-800 rounded-lg p-4 text-center">
            <div className="text-2xl font-bold text-gray-900 dark:text-white">
              {result.predictions?.risk_assessment?.risk_reward_ratio || 'N/A'}
            </div>
            <div className="text-sm text-gray-500 dark:text-gray-400">Risk/Reward</div>
          </div>
        </div>
      </div>

      {/* Tabs Navigation */}
      <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg">
        <div className="border-b border-gray-200 dark:border-gray-700">
          <nav className="flex space-x-8 px-6">
            {[
              { id: 'overview', label: 'Overview', icon: '📊' },
              { id: 'technical', label: 'Technical', icon: '📈' },
              { id: 'reasoning', label: 'AI Reasoning', icon: '🧠' },
              { id: 'market', label: 'Market Context', icon: '🌍' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600 dark:text-blue-400'
                    : 'border-transparent text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </nav>
        </div>

        <div className="p-6">
          {activeTab === 'overview' && (
            <div className="space-y-6">
              {/* Multi-factor Analysis */}
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                  Multi-Factor Analysis
                </h3>
                <div className="space-y-3">
                  {result.multi_factor_analysis && Object.entries(result.multi_factor_analysis).map(([key, value]) => {
                    if (key === 'final_score') return null;
                    return (
                      <div key={key} className="flex items-center justify-between">
                        <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                          {key.replace('_', ' ')}
                        </span>
                        <div className="flex items-center space-x-3">
                          <div className="w-32 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                            <div
                              className={`h-2 rounded-full ${getScoreColor(value as number)}`}
                              style={{ width: `${(value as number) * 100}%` }}
                            ></div>
                          </div>
                          <span className="text-sm font-medium text-gray-900 dark:text-white w-12">
                            {((value as number) * 100).toFixed(0)}%
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Final Score */}
              {result.multi_factor_analysis?.final_score && (
                <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-gray-700 dark:to-gray-600 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <span className="text-lg font-semibold text-gray-900 dark:text-white">
                      Overall Score
                    </span>
                    <span className="text-3xl font-bold text-blue-600 dark:text-blue-400">
                      {(result.multi_factor_analysis.final_score * 100).toFixed(0)}%
                    </span>
                  </div>
                </div>
              )}
            </div>
          )}

          {activeTab === 'technical' && (
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                Technical Indicators & Patterns
              </h3>
              {result.technical_indicators ? (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {Object.entries(result.technical_indicators).map(([key, value]) => (
                    <div key={key} className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                        {key.replace('_', ' ')}
                      </div>
                      <div className="text-lg font-semibold text-gray-900 dark:text-white">
                        {typeof value === 'object' ? JSON.stringify(value, null, 2) : String(value)}
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">Technical analysis data will appear here</p>
              )}
            </div>
          )}

          {activeTab === 'reasoning' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                🧠 AI Reasoning & Analysis
              </h3>
              {result.prediction?.reasoning ? (
                Object.entries(result.prediction.reasoning).map(([category, factors]) => {
                  if (category === 'target_rationale') {
                    return (
                      <div key={category} className="bg-blue-50 dark:bg-blue-900 rounded-lg p-4">
                        <h4 className="text-md font-semibold text-blue-900 dark:text-blue-100 mb-2">
                          🎯 Target Price Rationale
                        </h4>
                        <p className="text-blue-800 dark:text-blue-200">{factors as string}</p>
                      </div>
                    );
                  }
                  return (
                    <div key={category} className="border-l-4 border-blue-500 pl-4">
                      <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-2 capitalize">
                        {category.includes('technical') && '📈 '}
                        {category.includes('historical') && '📊 '}
                        {category.includes('fundamental') && '💰 '}
                        {category.includes('risk') && '⚠️ '}
                        {category.replace('_', ' ')}
                      </h4>
                      <ul className="space-y-2">
                        {(factors as string[]).map((factor, index) => (
                          <li key={index} className="text-gray-700 dark:text-gray-300 text-sm flex items-start">
                            <span className="text-blue-500 mr-2 mt-1">•</span>
                            <span>{factor}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                })
              ) : (
                <p className="text-gray-500 dark:text-gray-400">AI reasoning will appear here after analysis</p>
              )}
            </div>
          )}

          {activeTab === 'market' && (
            <div className="space-y-6">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                🌍 Market Context & Data
              </h3>
              {result.market_context ? (
                <div className="space-y-4">
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Current Price
                      </div>
                      <div className="text-xl font-bold text-gray-900 dark:text-white">
                        ${result.market_context.current_price?.toLocaleString()}
                      </div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        24h Change
                      </div>
                      <div className={`text-xl font-bold ${
                        result.market_context.price_change_24h >= 0
                          ? 'text-green-600 dark:text-green-400'
                          : 'text-red-600 dark:text-red-400'
                      }`}>
                        {result.market_context.price_change_24h >= 0 ? '+' : ''}{result.market_context.price_change_24h?.toFixed(2)}%
                      </div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Market Phase
                      </div>
                      <div className="text-xl font-bold text-gray-900 dark:text-white capitalize">
                        {result.market_context.market_phase}
                      </div>
                    </div>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
                      <div className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                        Volatility
                      </div>
                      <div className="text-xl font-bold text-gray-900 dark:text-white">
                        {result.market_context.volatility_30d?.toFixed(1)}%
                      </div>
                    </div>
                  </div>

                  {/* Support & Resistance */}
                  {(result.market_context.support_levels?.length > 0 || result.market_context.resistance_levels?.length > 0) && (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {result.market_context.support_levels?.length > 0 && (
                        <div className="bg-green-50 dark:bg-green-900 rounded-lg p-4">
                          <h4 className="text-sm font-semibold text-green-800 dark:text-green-200 mb-2">Support Levels</h4>
                          <div className="space-y-1">
                            {result.market_context.support_levels.map((level, index) => (
                              <div key={index} className="text-green-700 dark:text-green-300 font-medium">
                                ${level.toLocaleString()}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}

                      {result.market_context.resistance_levels?.length > 0 && (
                        <div className="bg-red-50 dark:bg-red-900 rounded-lg p-4">
                          <h4 className="text-sm font-semibold text-red-800 dark:text-red-200 mb-2">Resistance Levels</h4>
                          <div className="space-y-1">
                            {result.market_context.resistance_levels.map((level, index) => (
                              <div key={index} className="text-red-700 dark:text-red-300 font-medium">
                                ${level.toLocaleString()}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              ) : (
                <p className="text-gray-500 dark:text-gray-400">Market context data will appear here</p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Risk Assessment */}
      {result.predictions?.risk_assessment && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Risk Assessment</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Risk Level:</span>
              <span className={`text-sm font-medium ${
                result.predictions.risk_assessment.risk_level === 'high' ? 'text-red-600' :
                result.predictions.risk_assessment.risk_level === 'medium' ? 'text-yellow-600' :
                'text-green-600'
              }`}>
                {result.predictions.risk_assessment.risk_level?.toUpperCase()}
              </span>
            </div>
            {result.predictions.risk_assessment.stop_loss && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Stop Loss:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatPrice(result.predictions.risk_assessment.stop_loss)}
                </span>
              </div>
            )}
            {result.predictions.risk_assessment.take_profit && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Take Profit:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatPrice(result.predictions.risk_assessment.take_profit)}
                </span>
              </div>
            )}
            {result.predictions.risk_assessment.risk_reward_ratio && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Risk/Reward:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  1:{result.predictions.risk_assessment.risk_reward_ratio}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Technical Indicators */}
      {result.technical_indicators && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Technical Indicators</h4>
          <div className="space-y-3">
            {result.technical_indicators.rsi && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">RSI:</span>
                <span className={`text-sm font-medium ${
                  result.technical_indicators.rsi > 70 ? 'text-red-600' :
                  result.technical_indicators.rsi < 30 ? 'text-green-600' :
                  'text-gray-900 dark:text-white'
                }`}>
                  {result.technical_indicators.rsi.toFixed(1)}
                </span>
              </div>
            )}
            
            {result.technical_indicators.macd && (
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">MACD:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {result.technical_indicators.macd.macd?.toFixed(4)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Signal:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {result.technical_indicators.macd.signal?.toFixed(4)}
                  </span>
                </div>
              </div>
            )}

            {result.technical_indicators.moving_averages && (
              <div className="space-y-1">
                {Object.entries(result.technical_indicators.moving_averages).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{key.toUpperCase()}:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatPrice(value as number)}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Detected Patterns */}
      {result.chart_data?.detected_patterns && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Detected Patterns</h4>
          <div className="space-y-2">
            {result.chart_data.detected_patterns.trend_direction && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Trend:</span>
                <span className={`text-sm font-medium ${
                  result.chart_data.detected_patterns.trend_direction === 'bullish' ? 'text-green-600' :
                  result.chart_data.detected_patterns.trend_direction === 'bearish' ? 'text-red-600' :
                  'text-gray-900 dark:text-white'
                }`}>
                  {result.chart_data.detected_patterns.trend_direction?.toUpperCase()}
                </span>
              </div>
            )}
            
            {result.chart_data.detected_patterns.candlestick_patterns?.length > 0 && (
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Candlestick Patterns:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {result.chart_data.detected_patterns.candlestick_patterns.map((pattern: string, index: number) => (
                    <span key={index} className="inline-block px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {result.chart_data.detected_patterns.chart_patterns?.length > 0 && (
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Chart Patterns:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {result.chart_data.detected_patterns.chart_patterns.map((pattern: string, index: number) => (
                    <span key={index} className="inline-block px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Multi-Factor Analysis Scores */}
      {result.multi_factor_analysis && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Multi-Factor Analysis</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Technical Score:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ width: `${(result.multi_factor_analysis.technical_score || 0) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {((result.multi_factor_analysis.technical_score || 0) * 100).toFixed(0)}%
                </span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Historical Score:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${(result.multi_factor_analysis.historical_score || 0) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {((result.multi_factor_analysis.historical_score || 0) * 100).toFixed(0)}%
                </span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Fundamental Score:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full"
                    style={{ width: `${(result.multi_factor_analysis.fundamental_score || 0) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {((result.multi_factor_analysis.fundamental_score || 0) * 100).toFixed(0)}%
                </span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Sentiment Score:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-yellow-500 h-2 rounded-full"
                    style={{ width: `${(result.multi_factor_analysis.sentiment_score || 0) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {((result.multi_factor_analysis.sentiment_score || 0) * 100).toFixed(0)}%
                </span>
              </div>
            </div>

            <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Final Score:</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-3 rounded-full"
                      style={{ width: `${(result.multi_factor_analysis.final_score || 0) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    {((result.multi_factor_analysis.final_score || 0) * 100).toFixed(0)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detailed AI Reasoning */}
      {result.prediction?.reasoning && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            🧠 Detailed AI Reasoning
          </h4>

          {/* Technical Reasoning */}
          {result.prediction.reasoning.technical_factors && (
            <div className="mb-4">
              <h5 className="text-xs font-medium text-blue-600 dark:text-blue-400 mb-2">📈 TECHNICAL ANALYSIS</h5>
              <ul className="space-y-1">
                {result.prediction.reasoning.technical_factors.map((factor: string, index: number) => (
                  <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    {factor}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Historical Reasoning */}
          {result.prediction.reasoning.historical_factors && (
            <div className="mb-4">
              <h5 className="text-xs font-medium text-green-600 dark:text-green-400 mb-2">📊 HISTORICAL ANALYSIS</h5>
              <ul className="space-y-1">
                {result.prediction.reasoning.historical_factors.map((factor: string, index: number) => (
                  <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
                    <span className="text-green-500 mr-2">•</span>
                    {factor}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Fundamental Reasoning */}
          {result.prediction.reasoning.fundamental_factors && (
            <div className="mb-4">
              <h5 className="text-xs font-medium text-purple-600 dark:text-purple-400 mb-2">💰 FUNDAMENTAL ANALYSIS</h5>
              <ul className="space-y-1">
                {result.prediction.reasoning.fundamental_factors.map((factor: string, index: number) => (
                  <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
                    <span className="text-purple-500 mr-2">•</span>
                    {factor}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Risk Factors */}
          {result.prediction.reasoning.risk_factors && (
            <div className="mb-4">
              <h5 className="text-xs font-medium text-red-600 dark:text-red-400 mb-2">⚠️ RISK FACTORS</h5>
              <ul className="space-y-1">
                {result.prediction.reasoning.risk_factors.map((factor: string, index: number) => (
                  <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
                    <span className="text-red-500 mr-2">•</span>
                    {factor}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Target Rationale */}
          {result.prediction.reasoning.target_rationale && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">🎯 TARGET RATIONALE</h5>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {result.prediction.reasoning.target_rationale}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Market Context */}
      {result.market_context && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">📊 Market Context</h4>
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div>
              <span className="text-gray-500 dark:text-gray-400">Market Phase:</span>
              <p className="font-medium text-gray-900 dark:text-white capitalize">
                {result.market_context.market_phase}
              </p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Trend Strength:</span>
              <p className="font-medium text-gray-900 dark:text-white">
                {(result.market_context.trend_strength * 100).toFixed(0)}%
              </p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">24h Change:</span>
              <p className={`font-medium ${result.market_context.price_change_24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {result.market_context.price_change_24h >= 0 ? '+' : ''}{result.market_context.price_change_24h.toFixed(2)}%
              </p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">7d Change:</span>
              <p className={`font-medium ${result.market_context.price_change_7d >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {result.market_context.price_change_7d >= 0 ? '+' : ''}{result.market_context.price_change_7d.toFixed(2)}%
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Analysis Metadata */}
      <div className="text-xs text-gray-500 dark:text-gray-400 pt-4 border-t border-gray-200 dark:border-gray-600">
        <p>Analysis ID: {result.analysis_id}</p>
        <p>Timestamp: {new Date(result.timestamp).toLocaleString()}</p>
      </div>
    </div>
  );
}
