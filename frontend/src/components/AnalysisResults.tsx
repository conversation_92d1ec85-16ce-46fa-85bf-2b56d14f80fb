'use client';

interface AnalysisResultsProps {
  result: any;
}

export default function AnalysisResults({ result }: AnalysisResultsProps) {
  if (!result) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        <p>No analysis results available</p>
      </div>
    );
  }

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation?.toLowerCase()) {
      case 'strong_buy':
        return 'bg-green-500 text-white';
      case 'buy':
        return 'bg-green-400 text-white';
      case 'hold':
        return 'bg-yellow-500 text-white';
      case 'sell':
        return 'bg-red-400 text-white';
      case 'strong_sell':
        return 'bg-red-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getRecommendationText = (recommendation: string) => {
    switch (recommendation?.toLowerCase()) {
      case 'strong_buy':
        return 'Strong Buy';
      case 'buy':
        return 'Buy';
      case 'hold':
        return 'Hold';
      case 'sell':
        return 'Sell';
      case 'strong_sell':
        return 'Strong Sell';
      default:
        return 'Unknown';
    }
  };

  const formatConfidence = (confidence: number) => {
    return `${Math.round(confidence * 100)}%`;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };

  return (
    <div className="space-y-6">
      {/* Main Recommendation */}
      <div className="text-center">
        <div className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold ${getRecommendationColor(result.predictions?.recommendation)}`}>
          {getRecommendationText(result.predictions?.recommendation)}
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
          Confidence: {formatConfidence(result.predictions?.confidence || 0)}
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Target Price</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {result.predictions?.target_price ? formatPrice(result.predictions.target_price) : 'N/A'}
          </p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Time Horizon</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {result.predictions?.time_horizon || 'N/A'}
          </p>
        </div>
      </div>

      {/* Risk Assessment */}
      {result.predictions?.risk_assessment && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Risk Assessment</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Risk Level:</span>
              <span className={`text-sm font-medium ${
                result.predictions.risk_assessment.risk_level === 'high' ? 'text-red-600' :
                result.predictions.risk_assessment.risk_level === 'medium' ? 'text-yellow-600' :
                'text-green-600'
              }`}>
                {result.predictions.risk_assessment.risk_level?.toUpperCase()}
              </span>
            </div>
            {result.predictions.risk_assessment.stop_loss && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Stop Loss:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatPrice(result.predictions.risk_assessment.stop_loss)}
                </span>
              </div>
            )}
            {result.predictions.risk_assessment.take_profit && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Take Profit:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatPrice(result.predictions.risk_assessment.take_profit)}
                </span>
              </div>
            )}
            {result.predictions.risk_assessment.risk_reward_ratio && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Risk/Reward:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  1:{result.predictions.risk_assessment.risk_reward_ratio}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Technical Indicators */}
      {result.technical_indicators && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Technical Indicators</h4>
          <div className="space-y-3">
            {result.technical_indicators.rsi && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">RSI:</span>
                <span className={`text-sm font-medium ${
                  result.technical_indicators.rsi > 70 ? 'text-red-600' :
                  result.technical_indicators.rsi < 30 ? 'text-green-600' :
                  'text-gray-900 dark:text-white'
                }`}>
                  {result.technical_indicators.rsi.toFixed(1)}
                </span>
              </div>
            )}
            
            {result.technical_indicators.macd && (
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">MACD:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {result.technical_indicators.macd.macd?.toFixed(4)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Signal:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {result.technical_indicators.macd.signal?.toFixed(4)}
                  </span>
                </div>
              </div>
            )}

            {result.technical_indicators.moving_averages && (
              <div className="space-y-1">
                {Object.entries(result.technical_indicators.moving_averages).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{key.toUpperCase()}:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatPrice(value as number)}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Detected Patterns */}
      {result.chart_data?.detected_patterns && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Detected Patterns</h4>
          <div className="space-y-2">
            {result.chart_data.detected_patterns.trend_direction && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Trend:</span>
                <span className={`text-sm font-medium ${
                  result.chart_data.detected_patterns.trend_direction === 'bullish' ? 'text-green-600' :
                  result.chart_data.detected_patterns.trend_direction === 'bearish' ? 'text-red-600' :
                  'text-gray-900 dark:text-white'
                }`}>
                  {result.chart_data.detected_patterns.trend_direction?.toUpperCase()}
                </span>
              </div>
            )}
            
            {result.chart_data.detected_patterns.candlestick_patterns?.length > 0 && (
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Candlestick Patterns:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {result.chart_data.detected_patterns.candlestick_patterns.map((pattern: string, index: number) => (
                    <span key={index} className="inline-block px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {result.chart_data.detected_patterns.chart_patterns?.length > 0 && (
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Chart Patterns:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {result.chart_data.detected_patterns.chart_patterns.map((pattern: string, index: number) => (
                    <span key={index} className="inline-block px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* AI Reasoning */}
      {result.predictions?.reasoning && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">AI Analysis</h4>
          <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
            {result.predictions.reasoning}
          </p>
        </div>
      )}

      {/* Analysis Metadata */}
      <div className="text-xs text-gray-500 dark:text-gray-400 pt-4 border-t border-gray-200 dark:border-gray-600">
        <p>Analysis ID: {result.analysis_id}</p>
        <p>Timestamp: {new Date(result.timestamp).toLocaleString()}</p>
      </div>
    </div>
  );
}
