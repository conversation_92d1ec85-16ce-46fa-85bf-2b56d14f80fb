'use client';

interface AnalysisResultsProps {
  result: any;
}

export default function AnalysisResults({ result }: AnalysisResultsProps) {
  if (!result) {
    return (
      <div className="text-center py-8 text-gray-500 dark:text-gray-400">
        <p>No analysis results available</p>
      </div>
    );
  }

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation?.toLowerCase()) {
      case 'strong_buy':
        return 'bg-green-500 text-white';
      case 'buy':
        return 'bg-green-400 text-white';
      case 'hold':
        return 'bg-yellow-500 text-white';
      case 'sell':
        return 'bg-red-400 text-white';
      case 'strong_sell':
        return 'bg-red-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getRecommendationText = (recommendation: string) => {
    switch (recommendation?.toLowerCase()) {
      case 'strong_buy':
        return 'Strong Buy';
      case 'buy':
        return 'Buy';
      case 'hold':
        return 'Hold';
      case 'sell':
        return 'Sell';
      case 'strong_sell':
        return 'Strong Sell';
      default:
        return 'Unknown';
    }
  };

  const formatConfidence = (confidence: number) => {
    return `${Math.round(confidence * 100)}%`;
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(price);
  };

  return (
    <div className="space-y-6">
      {/* Main Recommendation */}
      <div className="text-center">
        <div className={`inline-flex items-center px-4 py-2 rounded-full text-lg font-bold ${getRecommendationColor(result.predictions?.recommendation)}`}>
          {getRecommendationText(result.predictions?.recommendation)}
        </div>
        <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
          Confidence: {formatConfidence(result.predictions?.confidence || 0)}
        </p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Target Price</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {result.predictions?.target_price ? formatPrice(result.predictions.target_price) : 'N/A'}
          </p>
        </div>
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
          <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">Time Horizon</p>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">
            {result.predictions?.time_horizon || 'N/A'}
          </p>
        </div>
      </div>

      {/* Risk Assessment */}
      {result.predictions?.risk_assessment && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Risk Assessment</h4>
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-sm text-gray-600 dark:text-gray-400">Risk Level:</span>
              <span className={`text-sm font-medium ${
                result.predictions.risk_assessment.risk_level === 'high' ? 'text-red-600' :
                result.predictions.risk_assessment.risk_level === 'medium' ? 'text-yellow-600' :
                'text-green-600'
              }`}>
                {result.predictions.risk_assessment.risk_level?.toUpperCase()}
              </span>
            </div>
            {result.predictions.risk_assessment.stop_loss && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Stop Loss:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatPrice(result.predictions.risk_assessment.stop_loss)}
                </span>
              </div>
            )}
            {result.predictions.risk_assessment.take_profit && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Take Profit:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {formatPrice(result.predictions.risk_assessment.take_profit)}
                </span>
              </div>
            )}
            {result.predictions.risk_assessment.risk_reward_ratio && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Risk/Reward:</span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  1:{result.predictions.risk_assessment.risk_reward_ratio}
                </span>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Technical Indicators */}
      {result.technical_indicators && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Technical Indicators</h4>
          <div className="space-y-3">
            {result.technical_indicators.rsi && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">RSI:</span>
                <span className={`text-sm font-medium ${
                  result.technical_indicators.rsi > 70 ? 'text-red-600' :
                  result.technical_indicators.rsi < 30 ? 'text-green-600' :
                  'text-gray-900 dark:text-white'
                }`}>
                  {result.technical_indicators.rsi.toFixed(1)}
                </span>
              </div>
            )}
            
            {result.technical_indicators.macd && (
              <div className="space-y-1">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">MACD:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {result.technical_indicators.macd.macd?.toFixed(4)}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Signal:</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-white">
                    {result.technical_indicators.macd.signal?.toFixed(4)}
                  </span>
                </div>
              </div>
            )}

            {result.technical_indicators.moving_averages && (
              <div className="space-y-1">
                {Object.entries(result.technical_indicators.moving_averages).map(([key, value]) => (
                  <div key={key} className="flex justify-between">
                    <span className="text-sm text-gray-600 dark:text-gray-400">{key.toUpperCase()}:</span>
                    <span className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatPrice(value as number)}
                    </span>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Detected Patterns */}
      {result.chart_data?.detected_patterns && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Detected Patterns</h4>
          <div className="space-y-2">
            {result.chart_data.detected_patterns.trend_direction && (
              <div className="flex justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-400">Trend:</span>
                <span className={`text-sm font-medium ${
                  result.chart_data.detected_patterns.trend_direction === 'bullish' ? 'text-green-600' :
                  result.chart_data.detected_patterns.trend_direction === 'bearish' ? 'text-red-600' :
                  'text-gray-900 dark:text-white'
                }`}>
                  {result.chart_data.detected_patterns.trend_direction?.toUpperCase()}
                </span>
              </div>
            )}
            
            {result.chart_data.detected_patterns.candlestick_patterns?.length > 0 && (
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Candlestick Patterns:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {result.chart_data.detected_patterns.candlestick_patterns.map((pattern: string, index: number) => (
                    <span key={index} className="inline-block px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-xs rounded">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}

            {result.chart_data.detected_patterns.chart_patterns?.length > 0 && (
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Chart Patterns:</span>
                <div className="mt-1 flex flex-wrap gap-1">
                  {result.chart_data.detected_patterns.chart_patterns.map((pattern: string, index: number) => (
                    <span key={index} className="inline-block px-2 py-1 bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200 text-xs rounded">
                      {pattern}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}

      {/* Multi-Factor Analysis Scores */}
      {result.multi_factor_analysis && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Multi-Factor Analysis</h4>
          <div className="space-y-3">
            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Technical Score:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-blue-500 h-2 rounded-full"
                    style={{ width: `${(result.multi_factor_analysis.technical_score || 0) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {((result.multi_factor_analysis.technical_score || 0) * 100).toFixed(0)}%
                </span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Historical Score:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-green-500 h-2 rounded-full"
                    style={{ width: `${(result.multi_factor_analysis.historical_score || 0) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {((result.multi_factor_analysis.historical_score || 0) * 100).toFixed(0)}%
                </span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Fundamental Score:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-purple-500 h-2 rounded-full"
                    style={{ width: `${(result.multi_factor_analysis.fundamental_score || 0) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {((result.multi_factor_analysis.fundamental_score || 0) * 100).toFixed(0)}%
                </span>
              </div>
            </div>

            <div className="flex justify-between items-center">
              <span className="text-sm text-gray-600 dark:text-gray-400">Sentiment Score:</span>
              <div className="flex items-center space-x-2">
                <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                  <div
                    className="bg-yellow-500 h-2 rounded-full"
                    style={{ width: `${(result.multi_factor_analysis.sentiment_score || 0) * 100}%` }}
                  ></div>
                </div>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {((result.multi_factor_analysis.sentiment_score || 0) * 100).toFixed(0)}%
                </span>
              </div>
            </div>

            <div className="pt-2 border-t border-gray-200 dark:border-gray-600">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Final Score:</span>
                <div className="flex items-center space-x-2">
                  <div className="w-24 bg-gray-200 dark:bg-gray-700 rounded-full h-3">
                    <div
                      className="bg-gradient-to-r from-red-500 via-yellow-500 to-green-500 h-3 rounded-full"
                      style={{ width: `${(result.multi_factor_analysis.final_score || 0) * 100}%` }}
                    ></div>
                  </div>
                  <span className="text-lg font-bold text-gray-900 dark:text-white">
                    {((result.multi_factor_analysis.final_score || 0) * 100).toFixed(0)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Detailed AI Reasoning */}
      {result.prediction?.reasoning && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">
            🧠 Detailed AI Reasoning
          </h4>

          {/* Technical Reasoning */}
          {result.prediction.reasoning.technical_factors && (
            <div className="mb-4">
              <h5 className="text-xs font-medium text-blue-600 dark:text-blue-400 mb-2">📈 TECHNICAL ANALYSIS</h5>
              <ul className="space-y-1">
                {result.prediction.reasoning.technical_factors.map((factor: string, index: number) => (
                  <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
                    <span className="text-blue-500 mr-2">•</span>
                    {factor}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Historical Reasoning */}
          {result.prediction.reasoning.historical_factors && (
            <div className="mb-4">
              <h5 className="text-xs font-medium text-green-600 dark:text-green-400 mb-2">📊 HISTORICAL ANALYSIS</h5>
              <ul className="space-y-1">
                {result.prediction.reasoning.historical_factors.map((factor: string, index: number) => (
                  <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
                    <span className="text-green-500 mr-2">•</span>
                    {factor}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Fundamental Reasoning */}
          {result.prediction.reasoning.fundamental_factors && (
            <div className="mb-4">
              <h5 className="text-xs font-medium text-purple-600 dark:text-purple-400 mb-2">💰 FUNDAMENTAL ANALYSIS</h5>
              <ul className="space-y-1">
                {result.prediction.reasoning.fundamental_factors.map((factor: string, index: number) => (
                  <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
                    <span className="text-purple-500 mr-2">•</span>
                    {factor}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Risk Factors */}
          {result.prediction.reasoning.risk_factors && (
            <div className="mb-4">
              <h5 className="text-xs font-medium text-red-600 dark:text-red-400 mb-2">⚠️ RISK FACTORS</h5>
              <ul className="space-y-1">
                {result.prediction.reasoning.risk_factors.map((factor: string, index: number) => (
                  <li key={index} className="text-xs text-gray-600 dark:text-gray-400 flex items-start">
                    <span className="text-red-500 mr-2">•</span>
                    {factor}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {/* Target Rationale */}
          {result.prediction.reasoning.target_rationale && (
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3">
              <h5 className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">🎯 TARGET RATIONALE</h5>
              <p className="text-xs text-gray-600 dark:text-gray-400">
                {result.prediction.reasoning.target_rationale}
              </p>
            </div>
          )}
        </div>
      )}

      {/* Market Context */}
      {result.market_context && (
        <div className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">📊 Market Context</h4>
          <div className="grid grid-cols-2 gap-3 text-xs">
            <div>
              <span className="text-gray-500 dark:text-gray-400">Market Phase:</span>
              <p className="font-medium text-gray-900 dark:text-white capitalize">
                {result.market_context.market_phase}
              </p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">Trend Strength:</span>
              <p className="font-medium text-gray-900 dark:text-white">
                {(result.market_context.trend_strength * 100).toFixed(0)}%
              </p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">24h Change:</span>
              <p className={`font-medium ${result.market_context.price_change_24h >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {result.market_context.price_change_24h >= 0 ? '+' : ''}{result.market_context.price_change_24h.toFixed(2)}%
              </p>
            </div>
            <div>
              <span className="text-gray-500 dark:text-gray-400">7d Change:</span>
              <p className={`font-medium ${result.market_context.price_change_7d >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                {result.market_context.price_change_7d >= 0 ? '+' : ''}{result.market_context.price_change_7d.toFixed(2)}%
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Analysis Metadata */}
      <div className="text-xs text-gray-500 dark:text-gray-400 pt-4 border-t border-gray-200 dark:border-gray-600">
        <p>Analysis ID: {result.analysis_id}</p>
        <p>Timestamp: {new Date(result.timestamp).toLocaleString()}</p>
      </div>
    </div>
  );
}
