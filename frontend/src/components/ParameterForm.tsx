'use client';

import { useState, useEffect } from 'react';

interface ParameterFormProps {
  onParametersChange?: (params: any) => void;
}

export default function ParameterForm({ onParametersChange }: ParameterFormProps) {
  const [parameters, setParameters] = useState({
    coinSymbol: 'BTC',
    timeframe: '1h',
    chartType: 'candlestick',
    additionalNotes: ''
  });

  const [supportedCoins, setSupportedCoins] = useState<string[]>([]);
  const [timeframes, setTimeframes] = useState<any[]>([]);

  useEffect(() => {
    // Fetch supported coins and timeframes from API
    fetchSupportedOptions();
  }, []);

  useEffect(() => {
    // Notify parent component of parameter changes
    if (onParametersChange) {
      onParametersChange(parameters);
    }
  }, [parameters, onParametersChange]);

  const fetchSupportedOptions = async () => {
    try {
      // Fetch supported coins
      const coinsResponse = await fetch('http://localhost:8000/api/v1/supported-coins');
      if (coinsResponse.ok) {
        const coinsData = await coinsResponse.json();
        setSupportedCoins(coinsData.supported_coins || []);
      }

      // Fetch timeframes
      const timeframesResponse = await fetch('http://localhost:8000/api/v1/timeframes');
      if (timeframesResponse.ok) {
        const timeframesData = await timeframesResponse.json();
        setTimeframes(timeframesData.timeframes || []);
      }
    } catch (error) {
      console.error('Error fetching options:', error);
      // Set default values if API is not available
      setSupportedCoins(['BTC', 'ETH', 'ADA', 'SOL', 'DOT', 'LINK']);
      setTimeframes([
        { value: '1m', label: '1 Minute' },
        { value: '5m', label: '5 Minutes' },
        { value: '15m', label: '15 Minutes' },
        { value: '1h', label: '1 Hour' },
        { value: '4h', label: '4 Hours' },
        { value: '1d', label: '1 Day' },
        { value: '1w', label: '1 Week' }
      ]);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setParameters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="space-y-6">
      {/* Coin Symbol */}
      <div>
        <label htmlFor="coinSymbol" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Cryptocurrency Symbol
        </label>
        <select
          id="coinSymbol"
          value={parameters.coinSymbol}
          onChange={(e) => handleInputChange('coinSymbol', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          {supportedCoins.map(coin => (
            <option key={coin} value={coin}>{coin}</option>
          ))}
        </select>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Select the cryptocurrency shown in your chart
        </p>
      </div>

      {/* Timeframe */}
      <div>
        <label htmlFor="timeframe" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Chart Timeframe
        </label>
        <select
          id="timeframe"
          value={parameters.timeframe}
          onChange={(e) => handleInputChange('timeframe', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          {timeframes.map(tf => (
            <option key={tf.value} value={tf.value}>{tf.label}</option>
          ))}
        </select>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          The time interval of each candle/data point
        </p>
      </div>

      {/* Chart Type */}
      <div>
        <label htmlFor="chartType" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Chart Type
        </label>
        <select
          id="chartType"
          value={parameters.chartType}
          onChange={(e) => handleInputChange('chartType', e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="candlestick">Candlestick</option>
          <option value="line">Line Chart</option>
          <option value="area">Area Chart</option>
          <option value="ohlc">OHLC Bars</option>
        </select>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          The type of chart visualization
        </p>
      </div>

      {/* Additional Notes */}
      <div>
        <label htmlFor="additionalNotes" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
          Additional Notes (Optional)
        </label>
        <textarea
          id="additionalNotes"
          value={parameters.additionalNotes}
          onChange={(e) => handleInputChange('additionalNotes', e.target.value)}
          rows={3}
          placeholder="Any specific indicators, patterns, or context you'd like the AI to consider..."
          className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Provide context about indicators, market conditions, or specific analysis requests
        </p>
      </div>

      {/* Analysis Options */}
      <div className="border-t border-gray-200 dark:border-gray-600 pt-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">Analysis Focus</h4>
        <div className="grid grid-cols-2 gap-3">
          <label className="flex items-center">
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Technical Indicators</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Pattern Recognition</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Volume Analysis</span>
          </label>
          <label className="flex items-center">
            <input
              type="checkbox"
              defaultChecked
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <span className="ml-2 text-sm text-gray-600 dark:text-gray-400">Risk Assessment</span>
          </label>
        </div>
      </div>

      {/* Current Parameters Summary */}
      <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Current Settings</h4>
        <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
          <p><span className="font-medium">Coin:</span> {parameters.coinSymbol}</p>
          <p><span className="font-medium">Timeframe:</span> {parameters.timeframe}</p>
          <p><span className="font-medium">Chart Type:</span> {parameters.chartType}</p>
          {parameters.additionalNotes && (
            <p><span className="font-medium">Notes:</span> {parameters.additionalNotes.substring(0, 50)}{parameters.additionalNotes.length > 50 ? '...' : ''}</p>
          )}
        </div>
      </div>
    </div>
  );
}
