// Simple script to test frontend-backend connectivity

export async function testBackendConnection() {
  try {
    // Use health endpoint instead of analyze-chart for testing
    const response = await fetch('http://localhost:8000/health');
    if (response.ok) {
      console.log('✅ Frontend can reach backend API!');
      return true;
    } else {
      console.error('❌ Backend API responded with error:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('❌ Could not connect to backend API:', error);
    return false;
  }
}