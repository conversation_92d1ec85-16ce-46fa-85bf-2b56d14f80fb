// Simple script to test frontend-backend connectivity

export async function testBackendConnection() {
  try {
    // Try a health endpoint if available, otherwise use analyze-chart with dummy data
    const response = await fetch('http://localhost:8000/api/v1/analyze-chart', {
      method: 'POST',
      body: new FormData(), // Empty form data for test
    });
    if (response.ok) {
      console.log('✅ Frontend can reach backend API!');
      return true;
    } else {
      console.error('❌ Backend API responded with error:', response.status, response.statusText);
      return false;
    }
  } catch (error) {
    console.error('❌ Could not connect to backend API:', error);
    return false;
  }
} 