version: '3.8'

services:
  # Backend API Service
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8000:8000"
    environment:
      - PYTHONPATH=/app
      - ENVIRONMENT=development
    volumes:
      - ./backend:/app
      - ./backend/uploads:/app/uploads
    depends_on:
      - redis
    networks:
      - chart-analysis-network

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    volumes:
      - ./frontend:/app
      - /app/node_modules
    depends_on:
      - backend
    networks:
      - chart-analysis-network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - chart-analysis-network

  # PostgreSQL Database (optional for storing analysis history)
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: chart_analysis
      POSTGRES_USER: chart_user
      POSTGRES_PASSWORD: chart_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - chart-analysis-network

volumes:
  redis_data:
  postgres_data:

networks:
  chart-analysis-network:
    driver: bridge
